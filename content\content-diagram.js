/**
 * Opens the diagram editor modal. Creates the canvas and toolbar.
 */
function openDiagramEditor() {
    // Close any existing editor first
    document.querySelector('.Stashy-diagram-editor')?.remove();

    // --- Editor State (local to this instance) ---
    let isDiagramDrawing = false;
    let diagramBrushColor = '#000000';
    let diagramBrushSize = 2;
    let diagramTool = 'pen'; // 'pen', 'eraser', 'rectangle', 'circle', 'line', 'text', 'select', 'connector'
    let diagramStartX, diagramStartY;
    let diagramHistory = [];
    let diagramHistoryIndex = -1;
    let diagramTempCanvasData = null; // For shape previews
    let activeTextShape = null; // For tracking text editing in shapes
    let shapes = []; // Array to store shapes with text
    let selectedShape = null; // Currently selected shape
    let isGridVisible = true; // Grid visibility
    let gridSize = 20; // Grid size in pixels
    let snapToGrid = true; // Snap to grid
    let zoomLevel = 1.0; // Zoom level
    let diagramStartShape = null; // For connector tool
    let diagramEndShape = null; // For connector tool
    let connectors = []; // Array to store connector lines
    let baseCanvasImageData = null; // Store the base canvas (freehand drawings only) separately

    // --- Create Editor Elements ---
    const diagramDiv = document.createElement('div');
    diagramDiv.classList.add('Stashy-diagram-editor'); // Base class from CSS
    diagramDiv.id = 'Stashy-diagram-editor-instance'; // Unique ID for this instance
    diagramDiv.setAttribute('role', 'dialog');
    diagramDiv.setAttribute('aria-modal', 'true');
    diagramDiv.setAttribute('aria-labelledby', 'Stashy-diagram-title');

    // Make the diagram editor draggable and position it initially
    diagramDiv.style.position = 'fixed';
    diagramDiv.style.zIndex = '9999';
    diagramDiv.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';
    diagramDiv.style.borderRadius = '8px';
    diagramDiv.style.border = '1px solid #ccc';

    // Position in the center of the screen
    diagramDiv.style.top = '50%';
    diagramDiv.style.left = '50%';
    diagramDiv.style.transform = 'translate(-50%, -50%)';



    // --- Title Bar with Controls ---
    const titleBar = document.createElement('div');
    titleBar.className = 'Stashy-diagram-titlebar';
    titleBar.style.display = 'flex';
    titleBar.style.justifyContent = 'space-between';
    titleBar.style.alignItems = 'center';
    titleBar.style.marginBottom = '10px';
    titleBar.style.paddingBottom = '8px';
    titleBar.style.borderBottom = '1px solid var(--border-color, #eee)';
    titleBar.style.cursor = 'move'; // Indicate draggable title
    titleBar.style.userSelect = 'none'; // Prevent text selection during drag

    // Add a visual drag handle to make it clear the title bar is draggable
    const dragHandle = document.createElement('span');
    dragHandle.innerHTML = '⋮⋮'; // Vertical dots as drag handle
    dragHandle.style.marginRight = '8px';
    dragHandle.style.fontSize = '14px';
    dragHandle.style.color = '#888';
    dragHandle.style.cursor = 'move';

    // Title
    const editorTitle = document.createElement('h4');
    editorTitle.id = 'Stashy-diagram-title';
    editorTitle.textContent = 'Diagram Editor';
    editorTitle.style.margin = '0';
    editorTitle.style.padding = '0';
    editorTitle.style.userSelect = 'none';

    // Controls container
    const titleControls = document.createElement('div');
    titleControls.className = 'Stashy-diagram-controls';
    titleControls.style.display = 'flex';
    titleControls.style.gap = '8px';

    // Help button
    const helpButton = document.createElement('button');
    helpButton.className = 'Stashy-diagram-control-btn';
    helpButton.innerHTML = '❓';
    helpButton.title = 'Help';
    helpButton.setAttribute('aria-label', 'Show Help');
    helpButton.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent dragging
        showDiagramHelp();
    });

    // Assemble title bar
    titleControls.appendChild(helpButton);

    // Create a container for the drag handle and title
    const titleContainer = document.createElement('div');
    titleContainer.style.display = 'flex';
    titleContainer.style.alignItems = 'center';

    titleContainer.appendChild(dragHandle);
    titleContainer.appendChild(editorTitle);

    titleBar.appendChild(titleContainer);
    titleBar.appendChild(titleControls);
    diagramDiv.appendChild(titleBar);

    // --- Main Content Container (for canvas and shapes library) ---
    const mainContentContainer = document.createElement('div');
    mainContentContainer.style.display = 'flex';
    mainContentContainer.style.flexGrow = '1';
    mainContentContainer.style.gap = '10px';

    // --- Shapes Library Panel ---
    const shapesLibraryPanel = document.createElement('div');
    shapesLibraryPanel.classList.add('Stashy-shapes-library');
    shapesLibraryPanel.style.width = '120px';
    shapesLibraryPanel.style.backgroundColor = '#f0f0f0';
    shapesLibraryPanel.style.border = '1px solid #ccc';
    shapesLibraryPanel.style.borderRadius = '4px';
    shapesLibraryPanel.style.padding = '8px';
    shapesLibraryPanel.style.overflowY = 'auto';
    shapesLibraryPanel.style.display = 'flex';
    shapesLibraryPanel.style.flexDirection = 'column';
    shapesLibraryPanel.style.gap = '10px';

    // Add title to shapes library
    const shapesLibraryTitle = document.createElement('h5');
    shapesLibraryTitle.textContent = 'Shapes Library';
    shapesLibraryTitle.style.margin = '0 0 5px 0';
    shapesLibraryTitle.style.fontSize = '14px';
    shapesLibraryTitle.style.textAlign = 'center';
    shapesLibraryTitle.style.borderBottom = '1px solid #ddd';
    shapesLibraryTitle.style.paddingBottom = '5px';
    shapesLibraryPanel.appendChild(shapesLibraryTitle);

    // --- Create Predefined Shapes ---
    // Helper function to create shape items
    const createShapeItem = (name, drawFunction) => {
        const shapeContainer = document.createElement('div');
        shapeContainer.classList.add('Stashy-shape-item');
        shapeContainer.style.backgroundColor = '#fff';
        shapeContainer.style.border = '1px solid #ddd';
        shapeContainer.style.borderRadius = '4px';
        shapeContainer.style.padding = '5px';
        shapeContainer.style.cursor = 'grab';
        shapeContainer.style.textAlign = 'center';
        shapeContainer.title = `Add ${name}`;
        shapeContainer.setAttribute('data-shape', name.toLowerCase());
        shapeContainer.setAttribute('draggable', 'true');

        // Create mini-canvas for shape preview
        const previewCanvas = document.createElement('canvas');
        previewCanvas.width = 80;
        previewCanvas.height = 60;
        previewCanvas.style.display = 'block';
        previewCanvas.style.margin = '0 auto 5px auto';
        previewCanvas.style.pointerEvents = 'none'; // Prevent canvas from interfering with drag

        // Draw shape preview
        const ctx = previewCanvas.getContext('2d', { willReadFrequently: true });
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, previewCanvas.width, previewCanvas.height);
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        drawFunction(ctx, previewCanvas.width, previewCanvas.height);

        // Add shape label
        const shapeLabel = document.createElement('div');
        shapeLabel.textContent = name;
        shapeLabel.style.fontSize = '12px';
        shapeLabel.style.fontWeight = 'bold';

        shapeContainer.appendChild(previewCanvas);
        shapeContainer.appendChild(shapeLabel);

        // Add drag event listeners
        shapeContainer.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', name.toLowerCase());
            e.dataTransfer.effectAllowed = 'copy';
            shapeContainer.style.opacity = '0.6';
        });

        shapeContainer.addEventListener('dragend', () => {
            shapeContainer.style.opacity = '1';
        });

        return shapeContainer;
    };

    // Rectangle shape
    shapesLibraryPanel.appendChild(createShapeItem('Rectangle', (ctx, width, height) => {
        ctx.strokeRect(10, 10, width - 20, height - 20);
    }));

    // Square shape
    shapesLibraryPanel.appendChild(createShapeItem('Square', (ctx, width, height) => {
        const size = Math.min(width, height) - 20;
        const x = (width - size) / 2;
        const y = (height - size) / 2;
        ctx.strokeRect(x, y, size, size);
    }));

    // Circle shape
    shapesLibraryPanel.appendChild(createShapeItem('Circle', (ctx, width, height) => {
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 2 - 10;
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.stroke();
    }));

    // Diamond shape
    shapesLibraryPanel.appendChild(createShapeItem('Diamond', (ctx, width, height) => {
        const centerX = width / 2;
        const centerY = height / 2;
        const size = Math.min(width, height) / 2 - 10;

        ctx.beginPath();
        ctx.moveTo(centerX, centerY - size); // Top
        ctx.lineTo(centerX + size, centerY); // Right
        ctx.lineTo(centerX, centerY + size); // Bottom
        ctx.lineTo(centerX - size, centerY); // Left
        ctx.closePath();
        ctx.stroke();
    }));

    // Flowchart: Process (rectangle with text)
    shapesLibraryPanel.appendChild(createShapeItem('Process', (ctx, width, height) => {
        ctx.strokeRect(10, 15, width - 20, height - 30);
        ctx.font = '10px Arial';
        ctx.fillStyle = '#000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Process', width / 2, height / 2);
    }));

    // Flowchart: Decision (diamond with text)
    shapesLibraryPanel.appendChild(createShapeItem('Decision', (ctx, width, height) => {
        const centerX = width / 2;
        const centerY = height / 2;
        const size = Math.min(width, height) / 2 - 10;

        ctx.beginPath();
        ctx.moveTo(centerX, centerY - size); // Top
        ctx.lineTo(centerX + size, centerY); // Right
        ctx.lineTo(centerX, centerY + size); // Bottom
        ctx.lineTo(centerX - size, centerY); // Left
        ctx.closePath();
        ctx.stroke();

        ctx.font = '9px Arial';
        ctx.fillStyle = '#000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Decision', width / 2, height / 2);
    }));

    // Flowchart: Start/End (rounded rectangle)
    shapesLibraryPanel.appendChild(createShapeItem('Terminal', (ctx, width, height) => {
        const rectWidth = width - 20;
        const rectHeight = height - 30;
        const cornerRadius = 15;

        ctx.beginPath();
        ctx.moveTo(10 + cornerRadius, 15);
        ctx.lineTo(10 + rectWidth - cornerRadius, 15);
        ctx.arcTo(10 + rectWidth, 15, 10 + rectWidth, 15 + cornerRadius, cornerRadius);
        ctx.lineTo(10 + rectWidth, 15 + rectHeight - cornerRadius);
        ctx.arcTo(10 + rectWidth, 15 + rectHeight, 10 + rectWidth - cornerRadius, 15 + rectHeight, cornerRadius);
        ctx.lineTo(10 + cornerRadius, 15 + rectHeight);
        ctx.arcTo(10, 15 + rectHeight, 10, 15 + rectHeight - cornerRadius, cornerRadius);
        ctx.lineTo(10, 15 + cornerRadius);
        ctx.arcTo(10, 15, 10 + cornerRadius, 15, cornerRadius);
        ctx.closePath();
        ctx.stroke();

        ctx.font = '10px Arial';
        ctx.fillStyle = '#000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Start/End', width / 2, height / 2);
    }));

    // Flowchart: Data (parallelogram)
    shapesLibraryPanel.appendChild(createShapeItem('Data', (ctx, width, height) => {
        const rectWidth = width - 20;
        const rectHeight = height - 30;
        const offset = 15; // Slant offset

        ctx.beginPath();
        ctx.moveTo(10 + offset, 15);
        ctx.lineTo(10 + rectWidth, 15);
        ctx.lineTo(10 + rectWidth - offset, 15 + rectHeight);
        ctx.lineTo(10, 15 + rectHeight);
        ctx.closePath();
        ctx.stroke();

        ctx.font = '10px Arial';
        ctx.fillStyle = '#000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Data', width / 2, height / 2);
    }));

    // Flowchart: Input/Output (parallelogram with different slant)
    shapesLibraryPanel.appendChild(createShapeItem('I/O', (ctx, width, height) => {
        const rectWidth = width - 20;
        const rectHeight = height - 30;
        const offset = 12; // Slant offset

        ctx.beginPath();
        ctx.moveTo(10, 15);
        ctx.lineTo(10 + rectWidth - offset, 15);
        ctx.lineTo(10 + rectWidth, 15 + rectHeight);
        ctx.lineTo(10 + offset, 15 + rectHeight);
        ctx.closePath();
        ctx.stroke();

        ctx.font = '10px Arial';
        ctx.fillStyle = '#000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Input/Output', width / 2, height / 2);
    }));

    // Flowchart: Document (rectangle with wavy bottom)
    shapesLibraryPanel.appendChild(createShapeItem('Document', (ctx, width, height) => {
        const rectWidth = width - 20;
        const rectHeight = height - 30;

        ctx.beginPath();
        ctx.moveTo(10, 15);
        ctx.lineTo(10 + rectWidth, 15);
        ctx.lineTo(10 + rectWidth, 15 + rectHeight - 10);

        // Wavy bottom
        const waveWidth = rectWidth / 4;
        ctx.bezierCurveTo(
            10 + rectWidth - waveWidth/2, 15 + rectHeight + 5,
            10 + rectWidth - waveWidth, 15 + rectHeight - 5,
            10 + rectWidth - waveWidth, 15 + rectHeight
        );
        ctx.bezierCurveTo(
            10 + rectWidth - waveWidth - waveWidth/2, 15 + rectHeight + 5,
            10 + rectWidth - waveWidth*2, 15 + rectHeight - 5,
            10 + rectWidth - waveWidth*2, 15 + rectHeight
        );
        ctx.bezierCurveTo(
            10 + rectWidth - waveWidth*2 - waveWidth/2, 15 + rectHeight + 5,
            10 + rectWidth - waveWidth*3, 15 + rectHeight - 5,
            10 + rectWidth - waveWidth*3, 15 + rectHeight
        );
        ctx.bezierCurveTo(
            10 + rectWidth - waveWidth*3 - waveWidth/2, 15 + rectHeight + 5,
            10, 15 + rectHeight - 5,
            10, 15 + rectHeight - 10
        );

        ctx.closePath();
        ctx.stroke();

        ctx.font = '10px Arial';
        ctx.fillStyle = '#000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Document', width / 2, height / 2 - 5);
    }));

    // Flowchart: Predefined Process (rectangle with double sides)
    shapesLibraryPanel.appendChild(createShapeItem('Subroutine', (ctx, width, height) => {
        const rectWidth = width - 20;
        const rectHeight = height - 30;
        const innerOffset = 4; // Distance between outer and inner rectangles

        // Outer rectangle
        ctx.strokeRect(10, 15, rectWidth, rectHeight);

        // Inner rectangle
        ctx.strokeRect(10 + innerOffset, 15 + innerOffset,
                      rectWidth - innerOffset*2, rectHeight - innerOffset*2);

        ctx.font = '9px Arial';
        ctx.fillStyle = '#000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Subroutine', width / 2, height / 2);
    }));

    // --- Canvas Setup ---
    const diagramCanvasContainer = document.createElement('div');
    diagramCanvasContainer.style.flexGrow = '1'; // Take available space
    diagramCanvasContainer.style.position = 'relative';
    diagramCanvasContainer.style.backgroundColor = '#f8f8f8'; // Light background for container
    diagramCanvasContainer.style.border = '1px solid #ccc';
    diagramCanvasContainer.style.borderRadius = '4px';
    diagramCanvasContainer.style.overflow = 'hidden'; // Clip canvas if needed

    // Create grid canvas (background)
    const gridCanvas = document.createElement('canvas');
    const canvasWidth = 800; // Increased from 560
    const canvasHeight = 600; // Increased from 380
    gridCanvas.width = canvasWidth;
    gridCanvas.height = canvasHeight;
    gridCanvas.style.position = 'absolute';
    gridCanvas.style.top = '0';
    gridCanvas.style.left = '0';
    gridCanvas.style.pointerEvents = 'none'; // Allow clicks to pass through
    diagramCanvasContainer.appendChild(gridCanvas);

    // Main drawing canvas
    const diagramCanvas = document.createElement('canvas');
    diagramCanvas.width = canvasWidth;
    diagramCanvas.height = canvasHeight;
    diagramCanvas.style.background = 'transparent'; // Transparent to show grid
    diagramCanvas.style.cursor = 'crosshair';
    diagramCanvas.style.display = 'block'; // Prevent extra space below canvas
    diagramCanvas.style.position = 'absolute'; // Position above grid (changed from relative)
    diagramCanvas.style.top = '0';
    diagramCanvas.style.left = '0';
    diagramCanvas.style.zIndex = '1';
    diagramCanvasContainer.appendChild(diagramCanvas);

    // Canvas contexts with willReadFrequently set to true for better performance
    // This optimizes for frequent getImageData() operations
    const gridCtx = gridCanvas.getContext('2d', { willReadFrequently: true });
    const diagramCtx = diagramCanvas.getContext('2d', { willReadFrequently: true });

    // Setup drawing context
    diagramCtx.lineCap = 'round'; // Smoother lines
    diagramCtx.lineJoin = 'round';
    diagramCtx.fillStyle = '#ffffff'; // Ensure background is white for saving/history
    diagramCtx.fillRect(0, 0, diagramCanvas.width, diagramCanvas.height);

    // --- Pan functionality ---
    let isPanning = false;
    let panStartX = 0;
    let panStartY = 0;
    let panOffsetX = 0;
    let panOffsetY = 0;

    // Add mouse wheel zoom functionality
    diagramCanvasContainer.addEventListener('wheel', (e) => {
        e.preventDefault();

        // Only zoom if Ctrl key is held down
        if (e.ctrlKey) {
            const rect = diagramCanvasContainer.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            const zoomDirection = e.deltaY > 0 ? -1 : 1;
            const newZoomLevel = Math.max(minZoom, Math.min(maxZoom, zoomLevel + (zoomDirection * zoomStep)));

            if (newZoomLevel !== zoomLevel) {
                zoomLevel = newZoomLevel;
                applyZoom();
            }
        }
    });

    // Add pan functionality with middle mouse button or space+drag
    diagramCanvas.addEventListener('mousedown', (e) => {
        if (e.button === 1 || (e.button === 0 && e.ctrlKey && e.shiftKey)) { // Middle mouse or Ctrl+Shift+Left click
            e.preventDefault();
            isPanning = true;
            panStartX = e.clientX;
            panStartY = e.clientY;
            diagramCanvas.style.cursor = 'grabbing';
        }
    });

    document.addEventListener('mousemove', (e) => {
        if (isPanning) {
            const deltaX = e.clientX - panStartX;
            const deltaY = e.clientY - panStartY;

            panOffsetX += deltaX;
            panOffsetY += deltaY;

            // Apply pan offset to canvas container
            diagramCanvasContainer.style.transform = `scale(${zoomLevel}) translate(${panOffsetX / zoomLevel}px, ${panOffsetY / zoomLevel}px)`;

            panStartX = e.clientX;
            panStartY = e.clientY;
        }
    });

    document.addEventListener('mouseup', (e) => {
        if (isPanning && (e.button === 1 || (e.button === 0 && diagramTool === 'pan'))) {
            isPanning = false;
            diagramCanvas.style.cursor = diagramTool === 'pan' ? 'grab' : 'crosshair';
        }
    });

    /**
     * Draws the grid on the grid canvas
     */
    function drawGrid() {
        if (!gridCtx) return;

        // Clear grid canvas
        gridCtx.clearRect(0, 0, gridCanvas.width, gridCanvas.height);

        // If grid is not visible, return
        if (!isGridVisible) return;

        // Fill with background color
        gridCtx.fillStyle = '#ffffff';
        gridCtx.fillRect(0, 0, gridCanvas.width, gridCanvas.height);

        // Draw grid lines
        gridCtx.beginPath();
        gridCtx.strokeStyle = 'rgba(0,0,0,0.15)';
        gridCtx.lineWidth = 1;

        // Draw vertical lines
        for (let x = 0; x <= gridCanvas.width; x += gridSize) {
            gridCtx.moveTo(x + 0.5, 0); // Add 0.5 for crisp lines
            gridCtx.lineTo(x + 0.5, gridCanvas.height);
        }

        // Draw horizontal lines
        for (let y = 0; y <= gridCanvas.height; y += gridSize) {
            gridCtx.moveTo(0, y + 0.5); // Add 0.5 for crisp lines
            gridCtx.lineTo(gridCanvas.width, y + 0.5);
        }

        gridCtx.stroke();

        // Draw major grid lines (every 5 cells)
        gridCtx.beginPath();
        gridCtx.strokeStyle = 'rgba(0,0,0,0.3)';
        gridCtx.lineWidth = 1;

        // Draw vertical major lines
        for (let x = 0; x <= gridCanvas.width; x += gridSize * 5) {
            gridCtx.moveTo(x + 0.5, 0); // Add 0.5 for crisp lines
            gridCtx.lineTo(x + 0.5, gridCanvas.height);
        }

        // Draw horizontal major lines
        for (let y = 0; y <= gridCanvas.height; y += gridSize * 5) {
            gridCtx.moveTo(0, y + 0.5); // Add 0.5 for crisp lines
            gridCtx.lineTo(gridCanvas.width, y + 0.5);
        }

        gridCtx.stroke();
    }

    /**
     * Snaps a coordinate to the nearest grid point
     * @param {number} coord - The coordinate to snap
     * @returns {number} The snapped coordinate
     */
    function snapToGridPoint(coord) {
        if (!snapToGrid) return coord;
        return Math.round(coord / gridSize) * gridSize;
    }

    // Initial grid draw
    drawGrid();
    diagramCtx.fillStyle = '#ffffff'; // Ensure background is white for saving/history
    diagramCtx.fillRect(0, 0, diagramCanvas.width, diagramCanvas.height);

    /**
     * Shows a help dialog with diagram editor instructions
     */
    function showDiagramHelp() {
        // Check if help dialog already exists
        const existingDialog = document.querySelector('.Stashy-diagram-help');
        if (existingDialog) {
            // If dialog already exists, just focus it instead of creating a new one
            existingDialog.focus();
            return;
        }

        // Create help dialog
        const helpDialog = document.createElement('div');
        helpDialog.className = 'Stashy-diagram-help';
        helpDialog.style.position = 'absolute';
        helpDialog.style.top = '50%';
        helpDialog.style.left = '50%';
        helpDialog.style.transform = 'translate(-50%, -50%)';
        helpDialog.style.width = '500px';
        helpDialog.style.maxWidth = '90%';
        helpDialog.style.maxHeight = '80%';
        helpDialog.style.backgroundColor = 'var(--bg-color, white)';
        helpDialog.style.color = 'var(--text-color, black)';
        helpDialog.style.border = '1px solid var(--border-color, #ccc)';
        helpDialog.style.borderRadius = '8px';
        helpDialog.style.boxShadow = '0 4px 20px rgba(0,0,0,0.2)';
        helpDialog.style.padding = '20px';
        helpDialog.style.zIndex = '10001';
        helpDialog.style.overflowY = 'auto';
        helpDialog.setAttribute('tabindex', '-1'); // Make it focusable

        // Help content
        helpDialog.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; border-bottom: 1px solid var(--border-color, #ddd); padding-bottom: 10px;">
                <h3 style="margin: 0;">Diagram Editor Help</h3>
                <button class="Stashy-diagram-control-btn" style="font-size: 16px;">✕</button>
            </div>

            <h4>Drawing Tools</h4>
            <ul>
                <li><strong>✏️ Pen Tool</strong>: Free-hand drawing</li>
                <li><strong>🧹 Eraser</strong>: Erase parts of the drawing</li>
                <li><strong>⬜ Rectangle</strong>: Draw rectangles</li>
                <li><strong>⚪ Circle</strong>: Draw circles</li>
                <li><strong>➖ Line</strong>: Draw straight lines</li>
                <li><strong>T Text</strong>: Add text to the diagram</li>
                <li><strong>↔️ Connector</strong>: Connect shapes with arrows</li>
                <li><strong>✊ Select</strong>: Select, move, and resize shapes</li>
                <li><strong>✋ Pan</strong>: Pan around the canvas (or use middle mouse/Ctrl+Shift+drag)</li>
            </ul>

            <h4>Shapes Library</h4>
            <p>Drag shapes from the library on the left onto the canvas. Double-click on a shape to add or edit text.</p>

            <h4>Keyboard Shortcuts</h4>
            <ul>
                <li><strong>Ctrl+Z</strong>: Undo</li>
                <li><strong>Ctrl+Y</strong>: Redo</li>
                <li><strong>Delete</strong>: Delete selected shape</li>
                <li><strong>Escape</strong>: Cancel current operation</li>
                <li><strong>G</strong>: Toggle grid visibility</li>
                <li><strong>S</strong>: Toggle snap to grid</li>
                <li><strong>Ctrl+Plus</strong>: Zoom in</li>
                <li><strong>Ctrl+Minus</strong>: Zoom out</li>
                <li><strong>Ctrl+0</strong>: Reset zoom to 100%</li>
                <li><strong>Ctrl+Wheel</strong>: Zoom in/out</li>
            </ul>

            <h4>Tips</h4>
            <ul>
                <li>Use the color picker to change the color of lines and shapes</li>
                <li>Use the size slider to adjust line thickness</li>
                <li>Double-click on any shape to add or edit text</li>
                <li>Use the grid for precise alignment</li>
                <li>Connect shapes with the connector tool to create flowcharts</li>
                <li>Use zoom controls or Ctrl+mouse wheel to zoom in/out for detailed work</li>
                <li>Use the pan tool or middle mouse button to move around large diagrams</li>
                <li>The canvas automatically scrolls when zoomed in beyond the visible area</li>
            </ul>
        `;

        // Add close button functionality
        const closeButton = helpDialog.querySelector('button');
        closeButton.addEventListener('click', () => {
            helpDialog.remove();
        });

        // Add escape key to close
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                helpDialog.remove();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);

        // Add to diagram div
        diagramDiv.appendChild(helpDialog);
    }

    // --- History Functions ---
    function saveDiagramCanvasState() {
        if (!diagramCanvas) return;

        // Discard future states if we draw after undoing
        if (diagramHistoryIndex < diagramHistory.length - 1) {
            diagramHistory = diagramHistory.slice(0, diagramHistoryIndex + 1);
        }

        // Create a state object that includes both the canvas image and the shapes/connectors data
        const state = {
            canvasImage: diagramCanvas.toDataURL(), // Store as base64 PNG
            shapes: JSON.parse(JSON.stringify(shapes || [])), // Deep copy of shapes array
            connectors: JSON.parse(JSON.stringify(connectors || [])), // Deep copy of connectors array
        };

        // Store the state object
        diagramHistory.push(state);
        diagramHistoryIndex++;

        // Limit history size (e.g., 20 steps) for performance/memory
        const MAX_HISTORY = 20;
        if (diagramHistory.length > MAX_HISTORY) {
            diagramHistory.shift();
            diagramHistoryIndex--;
        }

        updateDiagramUndoRedoButtons(); // Update button states
    }

    function restoreDiagramCanvasState(index) {
        if (index < 0 || index >= diagramHistory.length || !diagramCtx) return;

        // Get the state object from history
        const state = diagramHistory[index];

        // Check if state is in the new format (object with canvasImage, shapes, connectors)
        if (typeof state === 'object' && state.canvasImage) {
            // Restore canvas image
            const img = new Image();
            img.onload = () => {
                diagramCtx.clearRect(0, 0, diagramCanvas.width, diagramCanvas.height);
                diagramCtx.drawImage(img, 0, 0);
            };
            img.onerror = () => console.error("Stashy: Failed to load diagram history image.");
            img.src = state.canvasImage;

            // Restore shapes and connectors
            shapes = state.shapes ? JSON.parse(JSON.stringify(state.shapes)) : [];
            connectors = state.connectors ? JSON.parse(JSON.stringify(state.connectors)) : [];

            // Clear selection
            selectedShape = null;
        } else {
            // Legacy format (just the canvas image as string)
            const img = new Image();
            img.onload = () => {
                diagramCtx.clearRect(0, 0, diagramCanvas.width, diagramCanvas.height);
                diagramCtx.drawImage(img, 0, 0);
            };
            img.onerror = () => console.error("Stashy: Failed to load diagram history image.");
            img.src = state;

            // Clear shapes and connectors since we don't have that data
            shapes = [];
            connectors = [];
            selectedShape = null;
        }

        diagramHistoryIndex = index;
        updateDiagramUndoRedoButtons();
    }

    function diagramUndo() { if (diagramHistoryIndex > 0) restoreDiagramCanvasState(diagramHistoryIndex - 1); }
    function diagramRedo() { if (diagramHistoryIndex < diagramHistory.length - 1) restoreDiagramCanvasState(diagramHistoryIndex + 1); }

    function updateDiagramUndoRedoButtons() {
        // Target specifically labelled buttons if needed, otherwise assumes general structure
        const undoBtn = diagramDiv.querySelector('button[title="Undo last action"], button[aria-label="Undo last action"]'); // Adjust selector if needed
        const redoBtn = diagramDiv.querySelector('button[title="Redo last undone action"], button[aria-label="Redo last undone action"]'); // Adjust selector if needed
        if (undoBtn) undoBtn.disabled = diagramHistoryIndex <= 0;
        if (redoBtn) redoBtn.disabled = diagramHistoryIndex >= diagramHistory.length - 1;
    }


    // Save initial blank state
    saveDiagramCanvasState();

    // Initialize base canvas (empty at start)
    saveBaseCanvas();

    // --- Drawing Event Listeners ---
    diagramCanvas.addEventListener('mousedown', (e) => {
        isDiagramDrawing = true;
        diagramStartX = e.offsetX;
        diagramStartY = e.offsetY;

        // Handle text tool separately
        if (diagramTool === 'text') {
            // Create a text input at click position
            const textInput = document.createElement('input');
            textInput.type = 'text';
            textInput.placeholder = 'Enter text';
            textInput.style.position = 'absolute';
            textInput.style.left = `${e.offsetX}px`;
            textInput.style.top = `${e.offsetY}px`;
            textInput.style.transform = 'translate(-50%, -50%)';
            textInput.style.width = '150px';
            textInput.style.textAlign = 'center';
            textInput.style.zIndex = '1000';
            textInput.style.border = '1px solid #aaa';
            textInput.style.borderRadius = '3px';
            textInput.style.padding = '5px';
            textInput.style.backgroundColor = 'white';
            textInput.style.color = 'black';

            // Add input to canvas container
            diagramCanvasContainer.appendChild(textInput);
            textInput.focus();

            // Handle text input
            textInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    if (textInput.value.trim()) {
                        // Create a text shape object
                        const newTextShape = {
                            type: 'text',
                            x: diagramStartX - 75, // Center the text box
                            y: diagramStartY - 15, // Center the text box
                            width: 150,
                            height: 30,
                            text: textInput.value,
                            color: diagramBrushColor,
                            lineWidth: 1,
                            fontSize: 14
                        };

                        // Add to shapes array
                        shapes.push(newTextShape);

                        // Redraw canvas
                        redrawCanvas();

                        // Save canvas state
                        saveDiagramCanvasState();
                    }
                    textInput.remove();
                } else if (e.key === 'Escape') {
                    textInput.remove();
                }
            });

            textInput.addEventListener('blur', () => {
                if (textInput.value.trim()) {
                    // Create a text shape object
                    const newTextShape = {
                        type: 'text',
                        x: diagramStartX - 75, // Center the text box
                        y: diagramStartY - 15, // Center the text box
                        width: 150,
                        height: 30,
                        text: textInput.value,
                        color: diagramBrushColor,
                        lineWidth: 1,
                        fontSize: 14
                    };

                    // Add to shapes array
                    shapes.push(newTextShape);

                    // Redraw canvas
                    redrawCanvas();

                    // Save canvas state
                    saveDiagramCanvasState();
                }
                textInput.remove();
            });

            // Prevent further drawing
            isDiagramDrawing = false;
            return;
        }

        // Handle select tool
        if (diagramTool === 'select') {
            // Check if clicked on a shape
            const x = e.offsetX;
            const y = e.offsetY;

            // Clear any previous selection
            if (selectedShape !== null) {
                selectedShape = null;
                redrawCanvas();
            }

            // Search shapes in reverse order (top to bottom)
            for (let i = shapes.length - 1; i >= 0; i--) {
                const shape = shapes[i];

                // Check if click is inside shape bounds
                if (x >= shape.x && x <= shape.x + shape.width &&
                    y >= shape.y && y <= shape.y + shape.height) {

                    // For diamond and decision shapes, do additional hit testing
                    if (shape.type === 'diamond' || shape.type === 'decision') {
                        const centerX = shape.x + shape.width / 2;
                        const centerY = shape.y + shape.height / 2;
                        const halfWidth = shape.width / 2;
                        const halfHeight = shape.height / 2;

                        // Diamond hit test using point-to-line distance
                        const distTop = Math.abs((y - centerY) - (x - centerX) * (-halfHeight / halfWidth));
                        const distRight = Math.abs((y - centerY) - (x - centerX) * (halfHeight / halfWidth));
                        const distBottom = Math.abs((y - centerY) - (x - centerX) * (-halfHeight / halfWidth));
                        const distLeft = Math.abs((y - centerY) - (x - centerX) * (halfHeight / halfWidth));

                        // If any distance is greater than half height, point is outside
                        if (distTop > halfHeight || distRight > halfHeight ||
                            distBottom > halfHeight || distLeft > halfHeight) {
                            continue;
                        }
                    }

                    // For circle, do circle hit testing
                    if (shape.type === 'circle') {
                        const centerX = shape.x + shape.width / 2;
                        const centerY = shape.y + shape.height / 2;
                        const radius = Math.min(shape.width, shape.height) / 2;

                        const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
                        if (distance > radius) {
                            continue;
                        }
                    }

                    // For line, do line hit testing (check distance from point to line)
                    if (shape.type === 'line') {
                        const lineThreshold = Math.max(5, shape.lineWidth + 3); // Minimum 5px hit area
                        const distanceToLine = distanceFromPointToLine(x, y, shape.startX, shape.startY, shape.endX, shape.endY);
                        if (distanceToLine > lineThreshold) {
                            continue;
                        }
                    }

                    // Found a shape, select it
                    selectedShape = shape;

                    // Store offset for dragging
                    diagramStartX = x - shape.x;
                    diagramStartY = y - shape.y;

                    // Redraw with selection highlight
                    redrawCanvas();

                    // Set cursor to move
                    diagramCanvas.style.cursor = 'move';

                    break;
                }
            }

            return;
        }

        // Handle connector tool
        if (diagramTool === 'connector') {
            // Check if clicked on a shape to start connector
            const x = e.offsetX;
            const y = e.offsetY;

            // Search shapes in reverse order (top to bottom)
            for (let i = shapes.length - 1; i >= 0; i--) {
                const shape = shapes[i];

                // Check if click is inside shape bounds
                if (x >= shape.x && x <= shape.x + shape.width &&
                    y >= shape.y && y <= shape.y + shape.height) {

                    // For diamond and decision shapes, do additional hit testing
                    if (shape.type === 'diamond' || shape.type === 'decision') {
                        const centerX = shape.x + shape.width / 2;
                        const centerY = shape.y + shape.height / 2;
                        const halfWidth = shape.width / 2;
                        const halfHeight = shape.height / 2;

                        // Diamond hit test using point-to-line distance
                        const distTop = Math.abs((y - centerY) - (x - centerX) * (-halfHeight / halfWidth));
                        const distRight = Math.abs((y - centerY) - (x - centerX) * (halfHeight / halfWidth));
                        const distBottom = Math.abs((y - centerY) - (x - centerX) * (-halfHeight / halfWidth));
                        const distLeft = Math.abs((y - centerY) - (x - centerX) * (halfHeight / halfWidth));

                        // If any distance is greater than half height, point is outside
                        if (distTop > halfHeight || distRight > halfHeight ||
                            distBottom > halfHeight || distLeft > halfHeight) {
                            continue;
                        }
                    }

                    // For circle, do circle hit testing
                    if (shape.type === 'circle') {
                        const centerX = shape.x + shape.width / 2;
                        const centerY = shape.y + shape.height / 2;
                        const radius = Math.min(shape.width, shape.height) / 2;

                        const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
                        if (distance > radius) {
                            continue;
                        }
                    }

                    // Store the starting shape and position
                    diagramStartShape = shape;

                    // Calculate center of shape for start point
                    diagramStartX = shape.x + shape.width / 2;
                    diagramStartY = shape.y + shape.height / 2;

                    // Save canvas state for preview
                    diagramTempCanvasData = diagramCtx.getImageData(0, 0, diagramCanvas.width, diagramCanvas.height);

                    // Change cursor to indicate connection is in progress
                    diagramCanvas.style.cursor = 'crosshair';
                    break;
                }
            }

            return;
        }

        // Handle pan tool
        if (diagramTool === 'pan') {
            e.preventDefault();
            isPanning = true;
            panStartX = e.clientX;
            panStartY = e.clientY;
            diagramCanvas.style.cursor = 'grabbing';
            isDiagramDrawing = false; // Don't treat this as drawing
            return;
        }

        // Store canvas state ONLY for shape tools before drawing preview
        if (['rectangle', 'circle', 'line'].includes(diagramTool)) {
            diagramTempCanvasData = diagramCtx.getImageData(0, 0, diagramCanvas.width, diagramCanvas.height);
        }
        diagramCtx.beginPath();
        diagramCtx.moveTo(diagramStartX, diagramStartY); // Essential for pen/line/eraser
    });

    // Add double-click event to edit text in shapes
    diagramCanvas.addEventListener('dblclick', (e) => {
        const x = e.offsetX;
        const y = e.offsetY;

        // Check if click is inside a shape
        for (let i = shapes.length - 1; i >= 0; i--) {
            const shape = shapes[i];

            // Check if click is inside shape bounds
            if (x >= shape.x && x <= shape.x + shape.width &&
                y >= shape.y && y <= shape.y + shape.height) {

                // For diamond and decision shapes, do additional hit testing
                if (shape.type === 'diamond' || shape.type === 'decision') {
                    const centerX = shape.x + shape.width / 2;
                    const centerY = shape.y + shape.height / 2;
                    const halfWidth = shape.width / 2;
                    const halfHeight = shape.height / 2;

                    // Diamond hit test using point-to-line distance
                    const distTop = Math.abs((y - centerY) - (x - centerX) * (-halfHeight / halfWidth));
                    const distRight = Math.abs((y - centerY) - (x - centerX) * (halfHeight / halfWidth));
                    const distBottom = Math.abs((y - centerY) - (x - centerX) * (-halfHeight / halfWidth));
                    const distLeft = Math.abs((y - centerY) - (x - centerX) * (halfHeight / halfWidth));

                    // If any distance is greater than half height, point is outside
                    if (distTop > halfHeight || distRight > halfHeight ||
                        distBottom > halfHeight || distLeft > halfHeight) {
                        continue;
                    }
                }

                // For circle, do circle hit testing
                if (shape.type === 'circle') {
                    const centerX = shape.x + shape.width / 2;
                    const centerY = shape.y + shape.height / 2;
                    const radius = Math.min(shape.width, shape.height) / 2;

                    const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
                    if (distance > radius) {
                        continue;
                    }
                }

                // Found a shape, prompt for text
                promptForShapeText(shape);
                break;
            }
        }
    });

    diagramCanvas.addEventListener('mousemove', (e) => {
        const x = e.offsetX;
        const y = e.offsetY;

        // Update cursor based on tool and context
        if (diagramTool === 'select' && !isDiagramDrawing) {
            // Change cursor based on whether hovering over a shape
            let isOverShape = false;

            for (let i = shapes.length - 1; i >= 0; i--) {
                const shape = shapes[i];
                if (x >= shape.x && x <= shape.x + shape.width &&
                    y >= shape.y && y <= shape.y + shape.height) {
                    isOverShape = true;
                    break;
                }
            }

            diagramCanvas.style.cursor = isOverShape ? 'move' : 'default';
        } else if (diagramTool === 'pan' && !isPanning) {
            diagramCanvas.style.cursor = 'grab';
        }

        // If not drawing, exit early
        if (!isDiagramDrawing) return;

        diagramCtx.lineWidth = diagramBrushSize;

        // Handle select tool dragging
        if (diagramTool === 'select' && selectedShape) {
            // Calculate new position
            let newX = x - diagramStartX;
            let newY = y - diagramStartY;

            // Apply snap to grid if enabled
            if (snapToGrid) {
                newX = snapToGridPoint(newX);
                newY = snapToGridPoint(newY);
            }

            // Handle line shapes differently
            if (selectedShape.type === 'line') {
                // Calculate the offset from the old position
                const deltaX = newX - selectedShape.x;
                const deltaY = newY - selectedShape.y;

                // Update both start and end points
                selectedShape.startX += deltaX;
                selectedShape.startY += deltaY;
                selectedShape.endX += deltaX;
                selectedShape.endY += deltaY;

                // Update bounding box
                selectedShape.x = Math.min(selectedShape.startX, selectedShape.endX);
                selectedShape.y = Math.min(selectedShape.startY, selectedShape.endY);
                selectedShape.width = Math.abs(selectedShape.endX - selectedShape.startX);
                selectedShape.height = Math.abs(selectedShape.endY - selectedShape.startY);
            } else {
                // Update shape position for other shapes
                selectedShape.x = newX;
                selectedShape.y = newY;
            }

            // Redraw canvas
            redrawCanvas();

            return;
        }

        // Handle connector tool
        if (diagramTool === 'connector' && diagramStartShape) {
            if (!diagramTempCanvasData) return;

            // Restore canvas to pre-drag state for preview redraw
            diagramCtx.putImageData(diagramTempCanvasData, 0, 0);

            // Get center of start shape
            const startCenterX = diagramStartShape.x + diagramStartShape.width / 2;
            const startCenterY = diagramStartShape.y + diagramStartShape.height / 2;

            // Calculate start point (intersection with start shape border)
            const startPoint = calculateIntersection(diagramStartShape, x, y, startCenterX, startCenterY);

            // Draw connector line
            diagramCtx.beginPath();
            diagramCtx.globalCompositeOperation = 'source-over';
            diagramCtx.strokeStyle = diagramBrushColor;
            diagramCtx.lineWidth = diagramBrushSize;

            // Draw line with arrow
            drawArrow(diagramCtx, startPoint.x, startPoint.y, x, y);

            return;
        }

        if (diagramTool === 'pen') {
            diagramCtx.globalCompositeOperation = 'source-over'; // Ensure drawing mode
            diagramCtx.strokeStyle = diagramBrushColor;
            diagramCtx.lineTo(x, y);
            diagramCtx.stroke();
            diagramCtx.beginPath(); // Start new path segment for next move
            diagramCtx.moveTo(x, y);
        } else if (diagramTool === 'eraser') {
            diagramCtx.globalCompositeOperation = 'destination-out'; // Erase mode
            diagramCtx.strokeStyle = 'rgba(0,0,0,1)'; // Eraser color doesn't matter, alpha does
            diagramCtx.lineTo(x, y);
            diagramCtx.stroke();
            diagramCtx.beginPath();
            diagramCtx.moveTo(x, y);
        } else if (['rectangle', 'circle', 'line'].includes(diagramTool)) {
            if (!diagramTempCanvasData) return; // Should have been saved on mousedown
            // Restore canvas to pre-drag state for preview redraw
            diagramCtx.putImageData(diagramTempCanvasData, 0, 0);
            diagramCtx.beginPath(); // Start new path for the shape preview
            diagramCtx.globalCompositeOperation = 'source-over'; // Ensure drawing mode
            diagramCtx.strokeStyle = diagramBrushColor;
            diagramCtx.lineWidth = diagramBrushSize;

            if (diagramTool === 'rectangle') {
                diagramCtx.strokeRect(diagramStartX, diagramStartY, x - diagramStartX, y - diagramStartY);
            } else if (diagramTool === 'circle') {
                const dx = x - diagramStartX;
                const dy = y - diagramStartY;
                const radius = Math.sqrt(dx * dx + dy * dy) / 2;
                const centerX = diagramStartX + dx / 2;
                const centerY = diagramStartY + dy / 2;
                diagramCtx.arc(centerX, centerY, Math.abs(radius), 0, 2 * Math.PI);
                diagramCtx.stroke();
            } else if (diagramTool === 'line') {
                diagramCtx.moveTo(diagramStartX, diagramStartY);
                diagramCtx.lineTo(x, y);
                diagramCtx.stroke();
            }
        }
    });

    /**
     * Calculates the intersection point between a line and a shape's border
     * @param {Object} shape - The shape object
     * @param {number} x1 - Line start X (outside the shape)
     * @param {number} y1 - Line start Y (outside the shape)
     * @param {number} x2 - Line end X (center of the shape)
     * @param {number} y2 - Line end Y (center of the shape)
     * @returns {Object} The intersection point {x, y}
     */
    function calculateIntersection(shape, x1, y1, x2, y2) {
        // Get shape center
        const centerX = shape.x + shape.width / 2;
        const centerY = shape.y + shape.height / 2;

        // For circle shapes
        if (shape.type === 'circle') {
            const radius = Math.min(shape.width, shape.height) / 2;
            // Calculate angle from center to line end
            const angle = Math.atan2(y1 - centerY, x1 - centerX);
            // Calculate intersection point on circle border
            return {
                x: centerX + radius * Math.cos(angle),
                y: centerY + radius * Math.sin(angle)
            };
        }

        // For diamond and decision shapes
        if (shape.type === 'diamond' || shape.type === 'decision') {
            // Diamond points (top, right, bottom, left)
            const top = { x: centerX, y: shape.y };
            const right = { x: shape.x + shape.width, y: centerY };
            const bottom = { x: centerX, y: shape.y + shape.height };
            const left = { x: shape.x, y: centerY };

            // Check intersection with each edge
            const intersections = [
                lineIntersection(x1, y1, x2, y2, top.x, top.y, right.x, right.y),
                lineIntersection(x1, y1, x2, y2, right.x, right.y, bottom.x, bottom.y),
                lineIntersection(x1, y1, x2, y2, bottom.x, bottom.y, left.x, left.y),
                lineIntersection(x1, y1, x2, y2, left.x, left.y, top.x, top.y)
            ].filter(p => p !== null);

            // Return the first valid intersection
            if (intersections.length > 0) {
                return intersections[0];
            }
        }

        // For terminal shapes (rounded rectangle)
        if (shape.type === 'terminal') {
            // Simplify as a regular rectangle for now
            // This is an approximation - for perfect results we'd need to calculate
            // intersection with the rounded corners
        }

        // For all other rectangular shapes (process, rectangle, etc.)
        // Calculate intersections with each edge
        const left = lineIntersection(x1, y1, x2, y2, shape.x, shape.y, shape.x, shape.y + shape.height);
        const right = lineIntersection(x1, y1, x2, y2, shape.x + shape.width, shape.y, shape.x + shape.width, shape.y + shape.height);
        const top = lineIntersection(x1, y1, x2, y2, shape.x, shape.y, shape.x + shape.width, shape.y);
        const bottom = lineIntersection(x1, y1, x2, y2, shape.x, shape.y + shape.height, shape.x + shape.width, shape.y + shape.height);

        // Filter out null intersections and find the closest one to the outside point
        const intersections = [left, right, top, bottom].filter(p => p !== null);

        if (intersections.length > 0) {
            // If we have intersections, return the one closest to the outside point
            let closestDist = Infinity;
            let closestPoint = null;

            for (const point of intersections) {
                const dist = Math.sqrt(Math.pow(point.x - x1, 2) + Math.pow(point.y - y1, 2));
                if (dist < closestDist) {
                    closestDist = dist;
                    closestPoint = point;
                }
            }

            return closestPoint;
        }

        // Fallback to center if no intersection found
        return { x: centerX, y: centerY };
    }

    /**
     * Calculates the distance from a point to a line segment
     * @param {number} px - Point X
     * @param {number} py - Point Y
     * @param {number} x1 - Line start X
     * @param {number} y1 - Line start Y
     * @param {number} x2 - Line end X
     * @param {number} y2 - Line end Y
     * @returns {number} The distance from the point to the line
     */
    function distanceFromPointToLine(px, py, x1, y1, x2, y2) {
        const A = px - x1;
        const B = py - y1;
        const C = x2 - x1;
        const D = y2 - y1;

        const dot = A * C + B * D;
        const lenSq = C * C + D * D;

        if (lenSq === 0) {
            // Line is actually a point
            return Math.sqrt(A * A + B * B);
        }

        let param = dot / lenSq;

        let xx, yy;

        if (param < 0) {
            xx = x1;
            yy = y1;
        } else if (param > 1) {
            xx = x2;
            yy = y2;
        } else {
            xx = x1 + param * C;
            yy = y1 + param * D;
        }

        const dx = px - xx;
        const dy = py - yy;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * Calculates the intersection point between two line segments
     * @param {number} x1 - First line start X
     * @param {number} y1 - First line start Y
     * @param {number} x2 - First line end X
     * @param {number} y2 - First line end Y
     * @param {number} x3 - Second line start X
     * @param {number} y3 - Second line start Y
     * @param {number} x4 - Second line end X
     * @param {number} y4 - Second line end Y
     * @returns {Object|null} The intersection point {x, y} or null if no intersection
     */
    function lineIntersection(x1, y1, x2, y2, x3, y3, x4, y4) {
        // Calculate the denominator
        const denominator = ((y4 - y3) * (x2 - x1)) - ((x4 - x3) * (y2 - y1));

        // Lines are parallel if denominator is 0
        if (denominator === 0) {
            return null;
        }

        // Calculate the parameters for the intersection point
        const ua = (((x4 - x3) * (y1 - y3)) - ((y4 - y3) * (x1 - x3))) / denominator;
        const ub = (((x2 - x1) * (y1 - y3)) - ((y2 - y1) * (x1 - x3))) / denominator;

        // Check if the intersection is within both line segments
        if (ua >= 0 && ua <= 1 && ub >= 0 && ub <= 1) {
            // Calculate the intersection point
            const x = x1 + (ua * (x2 - x1));
            const y = y1 + (ua * (y2 - y1));

            return { x, y };
        }

        // No intersection within the line segments
        return null;
    }

    /**
     * Draws an arrow from (x1,y1) to (x2,y2)
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} x1 - Start X
     * @param {number} y1 - Start Y
     * @param {number} x2 - End X
     * @param {number} y2 - End Y
     */
    function drawArrow(ctx, x1, y1, x2, y2) {
        // Line
        ctx.beginPath();
        ctx.moveTo(x1, y1);
        ctx.lineTo(x2, y2);
        ctx.stroke();

        // Arrow head
        const headLength = 15;
        const angle = Math.atan2(y2 - y1, x2 - x1);

        ctx.beginPath();
        ctx.moveTo(x2, y2);
        ctx.lineTo(
            x2 - headLength * Math.cos(angle - Math.PI/6),
            y2 - headLength * Math.sin(angle - Math.PI/6)
        );
        ctx.lineTo(
            x2 - headLength * Math.cos(angle + Math.PI/6),
            y2 - headLength * Math.sin(angle + Math.PI/6)
        );
        ctx.closePath();
        ctx.fillStyle = diagramBrushColor;
        ctx.fill();
    }

    diagramCanvas.addEventListener('mouseup', (e) => {
        if (!isDiagramDrawing) return;
        isDiagramDrawing = false;
        const x = e.offsetX;
        const y = e.offsetY;

        // Handle select tool
        if (diagramTool === 'select' && selectedShape) {
            // Save the new position
            saveDiagramCanvasState();

            // Reset cursor
            diagramCanvas.style.cursor = 'default';
            return;
        }

        // Handle connector tool
        if (diagramTool === 'connector' && diagramStartShape) {
            if (!diagramTempCanvasData) return;

            // Check if ending on a shape
            let endShape = null;

            for (let i = shapes.length - 1; i >= 0; i--) {
                const shape = shapes[i];

                // Skip the start shape
                if (shape === diagramStartShape) continue;

                // Check if mouse is over this shape
                if (x >= shape.x && x <= shape.x + shape.width &&
                    y >= shape.y && y <= shape.y + shape.height) {

                    // For diamond and decision shapes, do additional hit testing
                    if (shape.type === 'diamond' || shape.type === 'decision') {
                        const centerX = shape.x + shape.width / 2;
                        const centerY = shape.y + shape.height / 2;
                        const halfWidth = shape.width / 2;
                        const halfHeight = shape.height / 2;

                        // Diamond hit test using point-to-line distance
                        const distTop = Math.abs((y - centerY) - (x - centerX) * (-halfHeight / halfWidth));
                        const distRight = Math.abs((y - centerY) - (x - centerX) * (halfHeight / halfWidth));
                        const distBottom = Math.abs((y - centerY) - (x - centerX) * (-halfHeight / halfWidth));
                        const distLeft = Math.abs((y - centerY) - (x - centerX) * (halfHeight / halfWidth));

                        // If any distance is greater than half height, point is outside
                        if (distTop > halfHeight || distRight > halfHeight ||
                            distBottom > halfHeight || distLeft > halfHeight) {
                            continue;
                        }
                    }

                    // For circle, do circle hit testing
                    if (shape.type === 'circle') {
                        const centerX = shape.x + shape.width / 2;
                        const centerY = shape.y + shape.height / 2;
                        const radius = Math.min(shape.width, shape.height) / 2;

                        const distance = Math.sqrt(Math.pow(x - centerX, 2) + Math.pow(y - centerY, 2));
                        if (distance > radius) {
                            continue;
                        }
                    }

                    endShape = shape;
                    break;
                }
            }

            // If we have an end shape, create a connector
            if (endShape) {
                // Calculate centers of both shapes
                const startCenterX = diagramStartShape.x + diagramStartShape.width / 2;
                const startCenterY = diagramStartShape.y + diagramStartShape.height / 2;
                const endCenterX = endShape.x + endShape.width / 2;
                const endCenterY = endShape.y + endShape.height / 2;

                // Calculate intersection points with shape borders
                const startPoint = calculateIntersection(diagramStartShape, endCenterX, endCenterY, startCenterX, startCenterY);
                const endPoint = calculateIntersection(endShape, startCenterX, startCenterY, endCenterX, endCenterY);

                // Restore canvas to pre-drag state
                diagramCtx.putImageData(diagramTempCanvasData, 0, 0);

                // Draw the final arrow
                diagramCtx.beginPath();
                diagramCtx.globalCompositeOperation = 'source-over';
                diagramCtx.strokeStyle = diagramBrushColor;
                diagramCtx.lineWidth = diagramBrushSize;

                drawArrow(diagramCtx, startPoint.x, startPoint.y, endPoint.x, endPoint.y);

                // Add connector to the connectors array
                connectors.push({
                    startShape: diagramStartShape,
                    endShape: endShape,
                    startX: startPoint.x,
                    startY: startPoint.y,
                    endX: endPoint.x,
                    endY: endPoint.y,
                    color: diagramBrushColor,
                    lineWidth: diagramBrushSize
                });

                // Save the state
                saveDiagramCanvasState();
            } else {
                // If not ending on a shape, just restore the canvas
                diagramCtx.putImageData(diagramTempCanvasData, 0, 0);
            }

            // Reset variables
            diagramTempCanvasData = null;
            diagramStartShape = null;
            diagramCanvas.style.cursor = 'default';

            return;
        }

        // Finalize drawing based on tool
        if (diagramTool === 'pen') {
            diagramCtx.lineTo(x, y); // Draw last segment
            diagramCtx.stroke();
            // Save base canvas after freehand drawing
            saveBaseCanvas();
        } else if (diagramTool === 'eraser') {
            diagramCtx.lineTo(x, y);
            diagramCtx.stroke();
            diagramCtx.globalCompositeOperation = 'source-over'; // Reset composite operation
            // Save base canvas after erasing
            saveBaseCanvas();
        } else if (['rectangle', 'circle', 'line'].includes(diagramTool)) {
            if (!diagramTempCanvasData) return;
            // Restore state BEFORE drawing the final shape permanently
            diagramCtx.putImageData(diagramTempCanvasData, 0, 0);
            diagramCtx.beginPath();
            diagramCtx.globalCompositeOperation = 'source-over';
            diagramCtx.strokeStyle = diagramBrushColor;
            diagramCtx.lineWidth = diagramBrushSize;

            // Apply snap to grid if enabled
            let startX = diagramStartX;
            let startY = diagramStartY;
            let endX = x;
            let endY = y;

            if (snapToGrid) {
                startX = snapToGridPoint(startX);
                startY = snapToGridPoint(startY);
                endX = snapToGridPoint(endX);
                endY = snapToGridPoint(endY);
            }

            if (diagramTool === 'rectangle') {
                diagramCtx.strokeRect(startX, startY, endX - startX, endY - startY);

                // Add to shapes array for selection
                const newShape = {
                    type: 'rectangle',
                    x: startX,
                    y: startY,
                    width: endX - startX,
                    height: endY - startY,
                    text: '',
                    color: diagramBrushColor,
                    lineWidth: diagramBrushSize
                };

                shapes.push(newShape);
            } else if (diagramTool === 'circle') {
                const dx = endX - startX, dy = endY - startY;
                const radius = Math.sqrt(dx*dx + dy*dy) / 2;
                const centerX = startX + dx/2, centerY = startY + dy/2;
                diagramCtx.arc(centerX, centerY, Math.abs(radius), 0, 2 * Math.PI);
                diagramCtx.stroke();

                // Add to shapes array for selection
                const newShape = {
                    type: 'circle',
                    x: centerX - radius,
                    y: centerY - radius,
                    width: radius * 2,
                    height: radius * 2,
                    text: '',
                    color: diagramBrushColor,
                    lineWidth: diagramBrushSize
                };

                shapes.push(newShape);
            } else if (diagramTool === 'line') {
                diagramCtx.moveTo(startX, startY);
                diagramCtx.lineTo(endX, endY);
                diagramCtx.stroke();

                // Add to shapes array for proper redrawing
                const newShape = {
                    type: 'line',
                    x: Math.min(startX, endX),
                    y: Math.min(startY, endY),
                    width: Math.abs(endX - startX),
                    height: Math.abs(endY - startY),
                    startX: startX,
                    startY: startY,
                    endX: endX,
                    endY: endY,
                    text: '',
                    color: diagramBrushColor,
                    lineWidth: diagramBrushSize
                };

                shapes.push(newShape);
            }
            diagramTempCanvasData = null; // Clear temp data
        }

        saveDiagramCanvasState(); // Save state after drawing is complete
    });

    diagramCanvas.addEventListener('mouseleave', () => {
        if (isDiagramDrawing) {
            // Treat leaving the canvas while drawing like a mouseup
            isDiagramDrawing = false;
            if (diagramTool === 'eraser') {
                diagramCtx.globalCompositeOperation = 'source-over'; // Reset if erasing
            }
            if (diagramTempCanvasData && ['rectangle', 'circle', 'line'].includes(diagramTool)) {
                 // If drawing a shape, cancel the preview on mouseleave? Or finalize?
                 // Let's cancel the preview to avoid partial shapes if mouse leaves accidentally.
                 diagramCtx.putImageData(diagramTempCanvasData, 0, 0); // Restore original state
                 diagramTempCanvasData = null;

            } else {
                 // For pen/eraser, save the partial drawing up to the leave point
                 // Check if anything was actually drawn before saving on leave
                 // (This might need more sophisticated logic to avoid saving state for just a click)
                 if (diagramTool === 'pen' || diagramTool === 'eraser') {

                     saveBaseCanvas(); // Save base canvas after freehand drawing
                     saveDiagramCanvasState();
                 }
            }
        }
    });

    // --- Drag and Drop Event Handlers for Shapes Library ---
    diagramCanvas.addEventListener('dragover', (e) => {
        e.preventDefault(); // Allow drop
        e.dataTransfer.dropEffect = 'copy';
    });

    diagramCanvas.addEventListener('drop', (e) => {
        e.preventDefault();
        const shapeType = e.dataTransfer.getData('text/plain');
        if (!shapeType) return;

        // Get drop position relative to canvas
        const rect = diagramCanvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Draw the shape at drop position
        diagramCtx.strokeStyle = diagramBrushColor;
        diagramCtx.lineWidth = diagramBrushSize;
        diagramCtx.fillStyle = 'rgba(255, 255, 255, 0.7)'; // Semi-transparent fill

        // Save canvas state before adding shape
        diagramTempCanvasData = diagramCtx.getImageData(0, 0, diagramCanvas.width, diagramCanvas.height);

        // Default shape size
        const width = 120;
        const height = 80;

        // Create shape based on type
        let newShape = {
            type: shapeType,
            x: x - width/2,
            y: y - height/2,
            width: width,
            height: height,
            text: '',
            color: diagramBrushColor,
            lineWidth: diagramBrushSize
        };

        // Draw the shape
        drawShape(newShape);

        // Add to shapes array
        shapes.push(newShape);

        // Save canvas state after adding shape
        saveDiagramCanvasState();

        // Prompt for text if it's a flowchart shape
        if (['process', 'decision', 'terminal', 'data', 'i/o', 'document', 'subroutine'].includes(shapeType)) {
            promptForShapeText(newShape);
        }

        // Apply snap to grid if enabled
        if (snapToGrid) {
            newShape.x = snapToGridPoint(newShape.x);
            newShape.y = snapToGridPoint(newShape.y);
            redrawCanvas();
            saveDiagramCanvasState();
        }
    });

    /**
     * Saves the current canvas state as the base layer (freehand drawings)
     * This should be called after freehand drawing operations
     */
    function saveBaseCanvas() {
        // Create a copy of the current canvas without shapes
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = diagramCanvas.width;
        tempCanvas.height = diagramCanvas.height;
        const tempCtx = tempCanvas.getContext('2d');

        // Copy current canvas
        tempCtx.drawImage(diagramCanvas, 0, 0);

        // Remove shapes from the copy by clearing their areas
        if (shapes && shapes.length > 0) {
            tempCtx.save();
            tempCtx.globalCompositeOperation = 'destination-out';
            tempCtx.fillStyle = 'rgba(0,0,0,1)';

            shapes.forEach(shape => {
                const padding = 2;
                tempCtx.fillRect(
                    shape.x - padding,
                    shape.y - padding,
                    shape.width + (padding * 2),
                    shape.height + (padding * 2)
                );
            });

            tempCtx.restore();
        }

        // Store the base canvas data
        baseCanvasImageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
    }

    /**
     * Redraws the entire canvas with all shapes and connectors
     * Preserves freehand drawings by using the stored base canvas
     */
    function redrawCanvas() {
        // Clear canvas
        diagramCtx.clearRect(0, 0, diagramCanvas.width, diagramCanvas.height);

        // Draw grid (this will clear and redraw the grid canvas)
        drawGrid();

        // Restore the base canvas (freehand drawings only) if available
        if (baseCanvasImageData) {
            diagramCtx.putImageData(baseCanvasImageData, 0, 0);
        }

        // Redraw shapes and connectors at their current positions
        redrawShapesAndConnectors();
    }

    /**
     * Helper function to redraw only shapes and connectors
     */
    function redrawShapesAndConnectors() {
        // Ensure shapes array exists
        if (!shapes) shapes = [];

        // Draw all shapes
        for (let i = 0; i < shapes.length; i++) {
            drawShape(shapes[i]);
        }

        // Ensure connectors array exists
        if (!connectors) connectors = [];

        // Draw all connectors
        for (let i = 0; i < connectors.length; i++) {
            const connector = connectors[i];

            // Skip invalid connectors
            if (!connector || !connector.startShape || !connector.endShape) continue;

            // Update connector positions based on current shape positions
            // Calculate centers of both shapes
            const startCenterX = connector.startShape.x + connector.startShape.width / 2;
            const startCenterY = connector.startShape.y + connector.startShape.height / 2;
            const endCenterX = connector.endShape.x + connector.endShape.width / 2;
            const endCenterY = connector.endShape.y + connector.endShape.height / 2;

            // Calculate intersection points with shape borders
            const startPoint = calculateIntersection(connector.startShape, endCenterX, endCenterY, startCenterX, startCenterY);
            const endPoint = calculateIntersection(connector.endShape, startCenterX, startCenterY, endCenterX, endCenterY);

            // Update connector points
            connector.startX = startPoint.x;
            connector.startY = startPoint.y;
            connector.endX = endPoint.x;
            connector.endY = endPoint.y;

            // Draw connector
            diagramCtx.strokeStyle = connector.color;
            diagramCtx.lineWidth = connector.lineWidth;
            drawArrow(diagramCtx, connector.startX, connector.startY, connector.endX, connector.endY);
        }

        // Draw selection highlight if a shape is selected
        if (selectedShape) {
            diagramCtx.save();
            diagramCtx.strokeStyle = '#4285f4';
            diagramCtx.lineWidth = 2;
            diagramCtx.setLineDash([5, 3]);
            diagramCtx.strokeRect(selectedShape.x - 2, selectedShape.y - 2, selectedShape.width + 4, selectedShape.height + 4);
            diagramCtx.restore();
        }
    }

    // Function to draw a shape
    function drawShape(shape) {
        diagramCtx.strokeStyle = shape.color;
        diagramCtx.lineWidth = shape.lineWidth;
        diagramCtx.fillStyle = 'rgba(255, 255, 255, 0.7)';

        switch(shape.type) {
            case 'rectangle':
                diagramCtx.fillRect(shape.x, shape.y, shape.width, shape.height);
                diagramCtx.strokeRect(shape.x, shape.y, shape.width, shape.height);
                break;

            case 'square':
                const size = Math.min(shape.width, shape.height);
                const squareX = shape.x + (shape.width - size) / 2;
                const squareY = shape.y + (shape.height - size) / 2;
                diagramCtx.fillRect(squareX, squareY, size, size);
                diagramCtx.strokeRect(squareX, squareY, size, size);
                break;

            case 'circle':
                const centerX = shape.x + shape.width / 2;
                const centerY = shape.y + shape.height / 2;
                const radius = Math.min(shape.width, shape.height) / 2;
                diagramCtx.beginPath();
                diagramCtx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
                diagramCtx.fill();
                diagramCtx.stroke();
                break;

            case 'line':
                diagramCtx.beginPath();
                diagramCtx.moveTo(shape.startX, shape.startY);
                diagramCtx.lineTo(shape.endX, shape.endY);
                diagramCtx.stroke();
                break;

            case 'diamond':
                const diamondCenterX = shape.x + shape.width / 2;
                const diamondCenterY = shape.y + shape.height / 2;

                diagramCtx.beginPath();
                diagramCtx.moveTo(diamondCenterX, shape.y); // Top
                diagramCtx.lineTo(shape.x + shape.width, diamondCenterY); // Right
                diagramCtx.lineTo(diamondCenterX, shape.y + shape.height); // Bottom
                diagramCtx.lineTo(shape.x, diamondCenterY); // Left
                diagramCtx.closePath();
                diagramCtx.fill();
                diagramCtx.stroke();
                break;

            case 'process':
                diagramCtx.fillRect(shape.x, shape.y, shape.width, shape.height);
                diagramCtx.strokeRect(shape.x, shape.y, shape.width, shape.height);
                break;

            case 'decision':
                const decisionCenterX = shape.x + shape.width / 2;
                const decisionCenterY = shape.y + shape.height / 2;

                diagramCtx.beginPath();
                diagramCtx.moveTo(decisionCenterX, shape.y); // Top
                diagramCtx.lineTo(shape.x + shape.width, decisionCenterY); // Right
                diagramCtx.lineTo(decisionCenterX, shape.y + shape.height); // Bottom
                diagramCtx.lineTo(shape.x, decisionCenterY); // Left
                diagramCtx.closePath();
                diagramCtx.fill();
                diagramCtx.stroke();
                break;

            case 'terminal':
                const cornerRadius = 20;

                diagramCtx.beginPath();
                diagramCtx.moveTo(shape.x + cornerRadius, shape.y);
                diagramCtx.lineTo(shape.x + shape.width - cornerRadius, shape.y);
                diagramCtx.arcTo(shape.x + shape.width, shape.y, shape.x + shape.width, shape.y + cornerRadius, cornerRadius);
                diagramCtx.lineTo(shape.x + shape.width, shape.y + shape.height - cornerRadius);
                diagramCtx.arcTo(shape.x + shape.width, shape.y + shape.height, shape.x + shape.width - cornerRadius, shape.y + shape.height, cornerRadius);
                diagramCtx.lineTo(shape.x + cornerRadius, shape.y + shape.height);
                diagramCtx.arcTo(shape.x, shape.y + shape.height, shape.x, shape.y + shape.height - cornerRadius, cornerRadius);
                diagramCtx.lineTo(shape.x, shape.y + cornerRadius);
                diagramCtx.arcTo(shape.x, shape.y, shape.x + cornerRadius, shape.y, cornerRadius);
                diagramCtx.closePath();
                diagramCtx.fill();
                diagramCtx.stroke();
                break;

            case 'data':
                const dataOffset = shape.width * 0.2; // Slant offset

                diagramCtx.beginPath();
                diagramCtx.moveTo(shape.x + dataOffset, shape.y);
                diagramCtx.lineTo(shape.x + shape.width, shape.y);
                diagramCtx.lineTo(shape.x + shape.width - dataOffset, shape.y + shape.height);
                diagramCtx.lineTo(shape.x, shape.y + shape.height);
                diagramCtx.closePath();
                diagramCtx.fill();
                diagramCtx.stroke();
                break;

            case 'i/o':
                const ioOffset = shape.width * 0.15; // Slant offset

                diagramCtx.beginPath();
                diagramCtx.moveTo(shape.x, shape.y);
                diagramCtx.lineTo(shape.x + shape.width - ioOffset, shape.y);
                diagramCtx.lineTo(shape.x + shape.width, shape.y + shape.height);
                diagramCtx.lineTo(shape.x + ioOffset, shape.y + shape.height);
                diagramCtx.closePath();
                diagramCtx.fill();
                diagramCtx.stroke();
                break;

            case 'document':
                diagramCtx.beginPath();
                diagramCtx.moveTo(shape.x, shape.y);
                diagramCtx.lineTo(shape.x + shape.width, shape.y);
                diagramCtx.lineTo(shape.x + shape.width, shape.y + shape.height - 10);

                // Wavy bottom
                const waveWidth = shape.width / 4;
                diagramCtx.bezierCurveTo(
                    shape.x + shape.width - waveWidth/2, shape.y + shape.height + 5,
                    shape.x + shape.width - waveWidth, shape.y + shape.height - 5,
                    shape.x + shape.width - waveWidth, shape.y + shape.height
                );
                diagramCtx.bezierCurveTo(
                    shape.x + shape.width - waveWidth - waveWidth/2, shape.y + shape.height + 5,
                    shape.x + shape.width - waveWidth*2, shape.y + shape.height - 5,
                    shape.x + shape.width - waveWidth*2, shape.y + shape.height
                );
                diagramCtx.bezierCurveTo(
                    shape.x + shape.width - waveWidth*2 - waveWidth/2, shape.y + shape.height + 5,
                    shape.x + shape.width - waveWidth*3, shape.y + shape.height - 5,
                    shape.x + shape.width - waveWidth*3, shape.y + shape.height
                );
                diagramCtx.bezierCurveTo(
                    shape.x + shape.width - waveWidth*3 - waveWidth/2, shape.y + shape.height + 5,
                    shape.x, shape.y + shape.height - 5,
                    shape.x, shape.y + shape.height - 10
                );

                diagramCtx.closePath();
                diagramCtx.fill();
                diagramCtx.stroke();
                break;

            case 'subroutine':
                const innerOffset = 4; // Distance between outer and inner rectangles

                // Outer rectangle
                diagramCtx.fillRect(shape.x, shape.y, shape.width, shape.height);
                diagramCtx.strokeRect(shape.x, shape.y, shape.width, shape.height);

                // Inner rectangle (just stroke)
                diagramCtx.strokeRect(
                    shape.x + innerOffset,
                    shape.y + innerOffset,
                    shape.width - innerOffset*2,
                    shape.height - innerOffset*2
                );
                break;
        }

        // Handle text-only shapes
        if (shape.type === 'text') {
            // For text-only shapes, we don't draw a background or border
            diagramCtx.font = `${shape.fontSize || 14}px Arial`;
            diagramCtx.fillStyle = shape.color || '#000000';
            diagramCtx.textAlign = 'center';
            diagramCtx.textBaseline = 'middle';

            // Calculate center of text shape
            const textX = shape.x + shape.width / 2;
            const textY = shape.y + shape.height / 2;

            // Draw text
            diagramCtx.fillText(shape.text, textX, textY);

            // If selected, draw a selection box around it
            if (selectedShape === shape) {
                diagramCtx.save();
                diagramCtx.strokeStyle = '#4285f4';
                diagramCtx.lineWidth = 1;
                diagramCtx.setLineDash([3, 3]);
                diagramCtx.strokeRect(shape.x, shape.y, shape.width, shape.height);
                diagramCtx.restore();
            }

            return; // Skip the regular text drawing below
        }

        // Draw text for other shapes if present
        if (shape.text) {
            diagramCtx.font = '14px Arial';
            diagramCtx.fillStyle = '#000000';
            diagramCtx.textAlign = 'center';
            diagramCtx.textBaseline = 'middle';

            // Calculate center of shape
            const textX = shape.x + shape.width / 2;
            const textY = shape.y + shape.height / 2;

            // Draw text
            diagramCtx.fillText(shape.text, textX, textY);
        }
    }

    // Function to prompt for text in a shape
    function promptForShapeText(shape) {
        // Set active shape for text editing
        activeTextShape = shape;

        // Create text input
        const textInput = document.createElement('input');
        textInput.type = 'text';
        textInput.value = shape.text || '';
        textInput.placeholder = 'Enter text for shape';
        textInput.style.position = 'absolute';
        textInput.style.left = `${shape.x + shape.width/2}px`;
        textInput.style.top = `${shape.y + shape.height/2}px`;
        textInput.style.transform = 'translate(-50%, -50%)';
        textInput.style.width = `${Math.min(shape.width - 10, 150)}px`;
        textInput.style.textAlign = 'center';
        textInput.style.zIndex = '1000';
        textInput.style.border = '1px solid #aaa';
        textInput.style.borderRadius = '3px';
        textInput.style.padding = '5px';

        // Add input to canvas container
        diagramCanvasContainer.appendChild(textInput);
        textInput.focus();

        // Handle text input
        textInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                applyShapeText(textInput.value);
                textInput.remove();
            } else if (e.key === 'Escape') {
                textInput.remove();
            }
        });

        textInput.addEventListener('blur', () => {
            applyShapeText(textInput.value);
            textInput.remove();
        });
    }

    // Function to apply text to a shape
    function applyShapeText(text) {
        if (!activeTextShape) return;

        // Update shape text
        activeTextShape.text = text;

        // Redraw canvas with updated text
        redrawCanvas();

        // Save canvas state
        saveDiagramCanvasState();

        // Clear active shape
        activeTextShape = null;
    }

    // This is a duplicate function - the main redrawCanvas function is defined earlier
    // and includes support for connectors and selection highlighting


    // --- Toolbar Creation ---
    const toolbar = document.createElement('div');
    toolbar.classList.add('Stashy-diagram-toolbar'); // Class from CSS

    // --- Toolbar Left Section (Drawing Tools) ---
    const toolsLeft = document.createElement('div');
    toolsLeft.style.display = 'flex';
    toolsLeft.style.alignItems = 'center';
    toolsLeft.style.gap = '5px';

    // -- HELPER FOR DIAGRAM TOOLS ONLY - Removes the text span --
    const createDiagramToolButton = (icon, label, toolName) => {
        // Use the general createToolButton function, passing an empty string for the text label
        // It's assumed createToolButton correctly uses 'label' for title/aria-label
        // and the second argument ('') for the visible text inside '.Stashy-text'
        const btn = createToolButton(icon, '', label, () => { // Pass '' for the text argument
            diagramTool = toolName;
            updateActiveDiagramTool(btn); // Highlight this button
            // Set appropriate cursor
            if (toolName === 'pen' || toolName === 'rectangle' || toolName === 'circle' || toolName === 'line') {
                 diagramCanvas.style.cursor = 'crosshair';
             } else if (toolName === 'eraser') {
                 diagramCanvas.style.cursor = 'cell'; // Or a custom eraser cursor
             }
        });
        btn.dataset.tool = toolName; // Mark button with its tool name

        // --- Explicitly find and remove the text span ---
        // This part is specific to DIAGRAM tools created with this function.
        const textSpanElement = btn.querySelector('.Stashy-text');
        if (textSpanElement) {
            // Found the span that createToolButton likely added for text content.
            // Remove it entirely for these diagram tool buttons.
            textSpanElement.remove();
        } else {
            // This might happen if createToolButton changes its structure
            // or doesn't add the span when the text parameter is empty.

        }
        return btn;
    };


    // --- Drawing Tool Buttons (Will have span removed by createDiagramToolButton) ---
    const penButton = createDiagramToolButton('✏️', 'Pen Tool', 'pen');
    const eraserButton = createDiagramToolButton('🧹', 'Eraser Tool', 'eraser');
    const rectButton = createDiagramToolButton('⬜', 'Rectangle Tool', 'rectangle');
    const circleButton = createDiagramToolButton('⚪', 'Circle Tool', 'circle');
    const lineButton = createDiagramToolButton('➖', 'Line Tool', 'line');
    const textButton = createDiagramToolButton('T', 'Text Tool', 'text');
    const selectButton = createDiagramToolButton('✊', 'Select Tool', 'select');
    const connectorButton = createDiagramToolButton('↔️', 'Connector Tool', 'connector');
    const panButton = createDiagramToolButton('✋', 'Pan Tool (Middle mouse or Ctrl+Shift+drag)', 'pan');

    // Add a divider
    const toolDivider1 = document.createElement('div');
    toolDivider1.style.width = '1px';
    toolDivider1.style.height = '24px';
    toolDivider1.style.backgroundColor = '#ccc';
    toolDivider1.style.margin = '0 5px';

    // Grid toggle button
    const gridToggleButton = document.createElement('button');
    gridToggleButton.className = 'Stashy-tool-btn';
    gridToggleButton.innerHTML = '📏';
    gridToggleButton.title = isGridVisible ? 'Hide Grid' : 'Show Grid';
    gridToggleButton.setAttribute('aria-label', isGridVisible ? 'Hide Grid' : 'Show Grid');

    // Apply active class if grid is visible
    if (isGridVisible) {
        gridToggleButton.classList.add('active');
    }

    gridToggleButton.addEventListener('click', () => {
        isGridVisible = !isGridVisible;

        // Update button appearance
        if (isGridVisible) {
            gridToggleButton.classList.add('active');
            gridToggleButton.title = 'Hide Grid';
            gridToggleButton.setAttribute('aria-label', 'Hide Grid');
        } else {
            gridToggleButton.classList.remove('active');
            gridToggleButton.title = 'Show Grid';
            gridToggleButton.setAttribute('aria-label', 'Show Grid');
        }

        // Redraw the grid
        drawGrid();
    });

    // Snap to grid toggle button
    const snapToggleButton = document.createElement('button');
    snapToggleButton.className = 'Stashy-tool-btn';
    snapToggleButton.innerHTML = '🧲';
    snapToggleButton.title = snapToGrid ? 'Disable Snap to Grid' : 'Enable Snap to Grid';
    snapToggleButton.setAttribute('aria-label', snapToGrid ? 'Disable Snap to Grid' : 'Enable Snap to Grid');

    // Apply active class if snap to grid is enabled
    if (snapToGrid) {
        snapToggleButton.classList.add('active');
    }

    snapToggleButton.addEventListener('click', () => {
        snapToGrid = !snapToGrid;

        // Update button appearance
        if (snapToGrid) {
            snapToggleButton.classList.add('active');
            snapToggleButton.title = 'Disable Snap to Grid';
            snapToggleButton.setAttribute('aria-label', 'Disable Snap to Grid');
        } else {
            snapToggleButton.classList.remove('active');
            snapToggleButton.title = 'Enable Snap to Grid';
            snapToggleButton.setAttribute('aria-label', 'Enable Snap to Grid');
        }
    });

    toolsLeft.appendChild(penButton);
    toolsLeft.appendChild(eraserButton);
    toolsLeft.appendChild(rectButton);
    toolsLeft.appendChild(circleButton);
    toolsLeft.appendChild(lineButton);
    toolsLeft.appendChild(textButton);
    toolsLeft.appendChild(selectButton);
    toolsLeft.appendChild(connectorButton);
    toolsLeft.appendChild(panButton);
    toolsLeft.appendChild(toolDivider1);
    toolsLeft.appendChild(gridToggleButton);
    toolsLeft.appendChild(snapToggleButton);

    // Add a divider before zoom controls
    const toolDivider2 = document.createElement('div');
    toolDivider2.style.width = '1px';
    toolDivider2.style.height = '24px';
    toolDivider2.style.backgroundColor = '#ccc';
    toolDivider2.style.margin = '0 5px';
    toolsLeft.appendChild(toolDivider2);

    // Zoom controls
    const minZoom = 0.25;
    const maxZoom = 3.0;
    const zoomStep = 0.25;

    const zoomOutButton = createToolButton('🔍-', '', 'Zoom Out', () => {
        if (zoomLevel > minZoom) {
            zoomLevel = Math.max(minZoom, zoomLevel - zoomStep);
            applyZoom();
        }
    });
    zoomOutButton.querySelector('.Stashy-text')?.remove();

    const zoomInButton = createToolButton('🔍+', '', 'Zoom In', () => {
        if (zoomLevel < maxZoom) {
            zoomLevel = Math.min(maxZoom, zoomLevel + zoomStep);
            applyZoom();
        }
    });
    zoomInButton.querySelector('.Stashy-text')?.remove();

    const zoomResetButton = createToolButton('🔍', '', 'Reset Zoom (100%)', () => {
        zoomLevel = 1.0;
        applyZoom();
    });
    zoomResetButton.querySelector('.Stashy-text')?.remove();

    // Zoom level display
    const zoomDisplay = document.createElement('span');
    zoomDisplay.style.fontSize = '12px';
    zoomDisplay.style.color = '#666';
    zoomDisplay.style.margin = '0 5px';
    zoomDisplay.style.minWidth = '40px';
    zoomDisplay.style.textAlign = 'center';
    zoomDisplay.textContent = '100%';

    function applyZoom() {
        const percentage = Math.round(zoomLevel * 100);
        zoomDisplay.textContent = `${percentage}%`;

        // Apply zoom and pan to canvas container
        diagramCanvasContainer.style.transform = `scale(${zoomLevel}) translate(${panOffsetX / zoomLevel}px, ${panOffsetY / zoomLevel}px)`;
        diagramCanvasContainer.style.transformOrigin = 'top left';

        // Update button states
        zoomOutButton.disabled = zoomLevel <= minZoom;
        zoomInButton.disabled = zoomLevel >= maxZoom;
    }

    toolsLeft.appendChild(zoomOutButton);
    toolsLeft.appendChild(zoomDisplay);
    toolsLeft.appendChild(zoomInButton);
    toolsLeft.appendChild(zoomResetButton);

    // Add another divider after zoom controls
    const toolDivider3 = document.createElement('div');
    toolDivider3.style.width = '1px';
    toolDivider3.style.height = '24px';
    toolDivider3.style.backgroundColor = '#ccc';
    toolDivider3.style.margin = '0 5px';
    toolsLeft.appendChild(toolDivider3);

    // Color Picker
    const colorPicker = document.createElement('input');
    colorPicker.type = 'color';
    colorPicker.value = diagramBrushColor;
    colorPicker.title = "Brush Color";
    colorPicker.setAttribute('aria-label', 'Brush Color');
    colorPicker.classList.add('Stashy-color-picker'); // CSS class
    colorPicker.style.marginLeft = '10px'; // Add some space before it
    colorPicker.addEventListener('input', (e) => { diagramBrushColor = e.target.value; });
    toolsLeft.appendChild(colorPicker);

    // Brush Size Slider
    const brushSizeSlider = document.createElement('input');
    brushSizeSlider.type = 'range';
    brushSizeSlider.min = 1;
    brushSizeSlider.max = 30; // Increased max size
    brushSizeSlider.value = diagramBrushSize;
    brushSizeSlider.title = `Brush Size: ${diagramBrushSize}`;
    brushSizeSlider.setAttribute('aria-label', 'Brush Size');
    brushSizeSlider.classList.add('Stashy-brush-slider'); // CSS class
    brushSizeSlider.style.width = '60px'; // Give it a specific width
    brushSizeSlider.style.marginLeft = '10px'; // Add some space before it
    brushSizeSlider.addEventListener('input', (e) => {
        diagramBrushSize = e.target.value;
        brushSizeSlider.title = `Brush Size: ${diagramBrushSize}`; // Update tooltip
    });
    toolsLeft.appendChild(brushSizeSlider);

    // --- Toolbar Right Section (Actions - NOW ICON ONLY) ---
    const toolsRight = document.createElement('div');
    toolsRight.style.display = 'flex';
    toolsRight.style.alignItems = 'center';
    toolsRight.style.gap = '5px';

    // --- Action Buttons (Icon Only + Span Removal) ---
    // These call createToolButton DIRECTLY. We provide an empty string ('')
    // as the second argument to hide the visible text label.
    // We ALSO explicitly remove the text span afterwards, just in case.
    // The third argument (tooltip/aria-label) is kept for usability.

    const undoButton = createToolButton('↺', '', 'Undo last action', diagramUndo);
    undoButton.querySelector('.Stashy-text')?.remove(); // Explicitly remove text span

    const redoButton = createToolButton('↻', '', 'Redo last undone action', diagramRedo);
    redoButton.querySelector('.Stashy-text')?.remove(); // Explicitly remove text span

    const clearButton = createToolButton('🗑️', '', 'Clear Canvas', () => {
        if (confirm("Clear the entire canvas? This cannot be undone easily.")) {
            // Clear the canvas
            diagramCtx.clearRect(0, 0, diagramCanvas.width, diagramCanvas.height);
            diagramCtx.fillStyle="#ffffff"; // Reset background fill explicitly
            diagramCtx.fillRect(0,0,diagramCanvas.width, diagramCanvas.height);

            // Clear all shapes and connectors
            shapes = [];
            connectors = [];
            selectedShape = null;

            // Reset history
            diagramHistory = [];
            diagramHistoryIndex = -1;

            // Draw grid if visible
            drawGrid();

            // Save the new blank state
            saveDiagramCanvasState();
        }
    });
    clearButton.querySelector('.Stashy-text')?.remove(); // Explicitly remove text span

    const saveButton = createToolButton('💾', '', 'Insert Diagram into Note', () => {
        // Assumes isCanvasEmpty is a global utility function
        if (typeof isCanvasEmpty !== 'function' || !isCanvasEmpty(diagramCanvas)) {
            try {
                const dataURL = diagramCanvas.toDataURL('image/png');
                const img = document.createElement('img');
                img.src = dataURL;
                img.alt = 'Diagram created in editor'; // Add alt text
                img.classList.add('Stashy-inserted-image');
                img.contentEditable = 'false'; // Important for contentEditable divs
                img.style.maxWidth = '55%';
                img.style.maxHeight = '250px';
                img.style.height = 'auto';
                img.style.display = 'block';
                img.style.margin = '5px auto';

                if (typeof insertNodeAtCursor === 'function') {
                     insertNodeAtCursor(img);
                    diagramDiv.remove(); // Close editor after inserting
                } else {
                    console.error("Stashy: 'insertNodeAtCursor' function not found.");
                    alert("Error: Could not insert image into the note.");
                }

            } catch (error) {
                console.error("Stashy: Error saving or inserting diagram:", error);
                alert("An error occurred while saving the diagram.");
            }
        } else {
            alert("Canvas is empty. Draw something before inserting.");
        }
    });
    saveButton.querySelector('.Stashy-text')?.remove(); // Explicitly remove text span

    const closeButton = createToolButton('×', '', 'Close Diagram Editor', () => { diagramDiv.remove(); });
    closeButton.querySelector('.Stashy-text')?.remove(); // Explicitly remove text span

    // Add action buttons to the right toolbar
    toolsRight.appendChild(undoButton);
    toolsRight.appendChild(redoButton);
    toolsRight.appendChild(clearButton);
    toolsRight.appendChild(saveButton);
    toolsRight.appendChild(closeButton);

    // --- Assemble Toolbar ---
    toolbar.appendChild(toolsLeft);
    toolbar.appendChild(toolsRight);

    // --- Assemble Editor ---
    diagramDiv.appendChild(toolbar);

    // Add shapes library and canvas to main content container
    mainContentContainer.appendChild(shapesLibraryPanel);
    mainContentContainer.appendChild(diagramCanvasContainer);

    // Add main content container to editor
    diagramDiv.appendChild(mainContentContainer);

    document.body.appendChild(diagramDiv); // Append to body last

    // --- Post-Append Setup ---
    updateActiveDiagramTool(penButton); // Set initial active tool highlight
    updateDiagramUndoRedoButtons(); // Set initial undo/redo state

    // Defer focusing slightly if needed, helps ensure element is fully rendered
    // setTimeout(() => diagramCanvas.focus(), 0); // Optional deferral

    // --- Make Editor Draggable by Title Bar ---
    let diagramDragActive = false, diaStartX, diaStartY, diaInitialLeft, diaInitialTop;

    // Make the entire title bar draggable
    titleBar.addEventListener('mousedown', (e) => {
        // Prevent drag from starting on buttons inside the title bar
        if (e.target.closest('.Stashy-diagram-control-btn')) return;

        diagramDragActive = true;
        diaStartX = e.clientX;
        diaStartY = e.clientY;

        // Get the current position
        const rect = diagramDiv.getBoundingClientRect();

        // If this is the first drag (still has the centering transform),
        // we need to convert to absolute positioning
        if (diagramDiv.style.transform && diagramDiv.style.transform.includes('translate')) {
            // Set the position based on the current visual position
            diagramDiv.style.left = rect.left + 'px';
            diagramDiv.style.top = rect.top + 'px';
            diagramDiv.style.transform = 'none';
        }

        // Now get the updated position values
        let currentLeft = parseFloat(diagramDiv.style.left) || rect.left;
        let currentTop = parseFloat(diagramDiv.style.top) || rect.top;
        diaInitialLeft = currentLeft;
        diaInitialTop = currentTop;

        // Change cursor to indicate dragging
        diagramDiv.style.cursor = 'grabbing';
        titleBar.style.cursor = 'grabbing';
        document.body.style.userSelect = 'none';
        e.preventDefault();
    });

    const onDragMove = (e) => {
        if (!diagramDragActive) return;
        const dx = e.clientX - diaStartX;
        const dy = e.clientY - diaStartY;

        let newLeft = diaInitialLeft + dx;
        let newTop = diaInitialTop + dy;

        // Ensure the editor stays within the viewport
        // Keep at least 150px visible on each side
        const minVisible = 150;
        newLeft = Math.max(minVisible - diagramDiv.offsetWidth, newLeft);
        newTop = Math.max(minVisible - diagramDiv.offsetHeight, newTop);
        newLeft = Math.min(window.innerWidth - minVisible, newLeft);
        newTop = Math.min(window.innerHeight - minVisible, newTop);

        // Apply the new position and clear the centering transform
        diagramDiv.style.left = newLeft + 'px';
        diagramDiv.style.top = newTop + 'px';
        diagramDiv.style.transform = 'none'; // Clear the centering transform
    };

    const onDragEnd = () => {
        if (diagramDragActive) {
            diagramDragActive = false;
            diagramDiv.style.cursor = '';
            titleBar.style.cursor = 'move';
            document.body.style.userSelect = '';
        }
    };

    document.addEventListener('mousemove', onDragMove, { capture: true });
    document.addEventListener('mouseup', onDragEnd, { capture: true });
    window.addEventListener('blur', onDragEnd); // Stop drag if window loses focus

    // Cleanup listeners (basic example - consider more robust approach if needed)
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            mutation.removedNodes.forEach(node => {
                if (node === diagramDiv) {
                    document.removeEventListener('mousemove', onDragMove, { capture: true });
                    document.removeEventListener('mouseup', onDragEnd, { capture: true });
                    window.removeEventListener('blur', onDragEnd);
                    observer.disconnect(); // Stop observing once removed

                }
            });
        });
    });
    if (diagramDiv.parentNode) { // Start observing if parent exists (usually body)
       observer.observe(diagramDiv.parentNode, { childList: true });
    }


    console.log("Stashy: Diagram Editor opened (Icon Only Buttons).");
}


/**
 * Updates the visual state (highlight) of the active diagram tool button.
 * Assumes an 'active' CSS class is defined.
 * @param {HTMLButtonElement} activeButton - The button that should be marked as active.
 */
function updateActiveDiagramTool(activeButton) {
    if (!activeButton || !activeButton.closest) return;
    const toolbar = activeButton.closest('.Stashy-diagram-toolbar');
    if (toolbar) {
        toolbar.querySelectorAll('button[data-tool]').forEach(btn => {
            btn.classList.remove('active');
        });
        activeButton.classList.add('active');
    } else {
        console.warn("Stashy: Could not find parent toolbar for active tool update.");
    }
}

// Ensure necessary global functions are defined elsewhere or imported/passed:
// - createToolButton(icon, text, tooltip, onClick) -> HTMLButtonElement
// - isCanvasEmpty(canvas) -> boolean
// - insertNodeAtCursor(node)

// --- Global Keyboard Shortcuts ---
document.addEventListener('keydown', (e) => {
    // Only process if diagram editor is open
    const diagramEditor = document.querySelector('.Stashy-diagram-editor');
    if (!diagramEditor) return;

    // Ctrl+Z for undo
    if (e.ctrlKey && e.key === 'z') {
        e.preventDefault();
        diagramUndo();
    }

    // Ctrl+Y for redo
    if (e.ctrlKey && e.key === 'y') {
        e.preventDefault();
        diagramRedo();
    }

    // G to toggle grid
    if (e.key === 'g' && !e.ctrlKey && !e.altKey && !e.shiftKey) {
        e.preventDefault();
        const gridButton = diagramEditor.querySelector('button[title="Show Grid"], button[title="Hide Grid"]');
        if (gridButton) {
            gridButton.click();
        }
    }

    // S to toggle snap to grid
    if (e.key === 's' && !e.ctrlKey && !e.altKey && !e.shiftKey) {
        e.preventDefault();
        const snapButton = diagramEditor.querySelector('button[title="Enable Snap to Grid"], button[title="Disable Snap to Grid"]');
        if (snapButton) {
            snapButton.click();
        }
    }

    // Zoom shortcuts
    if (e.key === '=' && e.ctrlKey) { // Ctrl+= for zoom in
        e.preventDefault();
        const zoomInButton = diagramEditor.querySelector('button[title="Zoom In"]');
        if (zoomInButton) {
            zoomInButton.click();
        }
    }

    if (e.key === '-' && e.ctrlKey) { // Ctrl+- for zoom out
        e.preventDefault();
        const zoomOutButton = diagramEditor.querySelector('button[title="Zoom Out"]');
        if (zoomOutButton) {
            zoomOutButton.click();
        }
    }

    if (e.key === '0' && e.ctrlKey) { // Ctrl+0 for reset zoom
        e.preventDefault();
        const zoomResetButton = diagramEditor.querySelector('button[title="Reset Zoom (100%)"]');
        if (zoomResetButton) {
            zoomResetButton.click();
        }
    }

    // Delete to delete selected shape
    if (e.key === 'Delete') {
        e.preventDefault();

        // Get the current selected shape from the global variable
        // This is more reliable than using a class selector
        if (selectedShape) {
            // Remove any connectors connected to this shape
            connectors = connectors.filter(connector =>
                connector.startShape !== selectedShape && connector.endShape !== selectedShape);

            // Remove the shape
            const index = shapes.indexOf(selectedShape);
            if (index !== -1) {
                shapes.splice(index, 1);
                selectedShape = null;

                // Redraw canvas
                redrawCanvas();

                // Save state
                saveDiagramCanvasState();
            }
        }
    }

    // Escape to cancel current operation
    if (e.key === 'Escape') {
        // If text input is active, it will be handled by the text input's event listener
        const selectedShape = diagramEditor.querySelector('.Stashy-shape-selected');
        if (selectedShape) {
            selectedShape.classList.remove('Stashy-shape-selected');
        }
    }
});

console.log("Stashy: Diagram Logic Loaded");