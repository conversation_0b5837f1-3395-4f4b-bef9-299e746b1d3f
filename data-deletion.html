<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stashy - Data Deletion Guide</title>
    <link rel="stylesheet" href="data-deletion.css">
</head>
<body>
    <h1>🗑️ Complete Data Deletion Guide</h1>

    <div class="note">
        <strong>Your Data, Your Control:</strong> Stashy is designed to give you complete control over your data. This comprehensive guide shows you how to delete any and all data associated with Stashy, ensuring nothing remains if you choose to remove it.
    </div>

    <p>This detailed guide explains how to completely remove all data associated with <PERSON>ash<PERSON> from every possible location. We believe in data ownership and provide multiple methods to ensure you can delete everything if desired.</p>

    <h2>🏠 Local Browser Data Deletion</h2>

    <div class="section">
        <h3>📝 Individual Note & Highlight Deletion</h3>
        <p>Delete specific notes and highlights without affecting other data:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Single Note Deletion:</strong> Open any note and click the "🗑️ Delete" button in the note toolbar
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Highlight Deletion:</strong> Right-click any highlight and select "Delete Highlight" from the context menu
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Page-Specific Deletion:</strong> In the Stashy popup, click "Clear Page Data" to remove all notes and highlights from the current webpage
            </div>
        </div>
    </div>

    <div class="section">
        <h3>📊 Dashboard Bulk Deletion</h3>
        <p>Use the dashboard for efficient bulk deletion operations:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Access Dashboard:</strong> Click "📊 View Dashboard" in the Stashy popup
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Select Items:</strong> Use checkboxes to select multiple notes or highlights
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Bulk Delete:</strong> Click "Delete Selected" and confirm the deletion
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Notebook Deletion:</strong> Right-click any notebook in the sidebar and select "Delete Notebook"
            </div>
        </div>

        <div class="note">
            <strong>Tip:</strong> Use the search and filter options to find specific data before deletion. You can filter by date, tags, or content to target specific items.
        </div>
    </div>

    <div class="section">
        <h3>🔧 Complete Local Data Wipe</h3>
        <p>Remove all local Stashy data from your browser:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Extension Management:</strong> Navigate to <code>chrome://extensions</code> in your browser
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Find Stashy:</strong> Locate Stashy in the extension list and click "Details"
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Clear All Data:</strong> Scroll down and click "Clear Data" button
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Confirm Deletion:</strong> Confirm when prompted - this removes all notes, highlights, settings, and preferences
            </div>
        </div>

        <div class="note">
            <strong>Warning:</strong> This action is permanent and cannot be undone. All local data will be permanently deleted unless you have cloud backups enabled.
        </div>
    </div>

    <h2>☁️ Cloud Data Deletion</h2>

    <div class="section">
        <h3>📁 Google Drive Data Removal</h3>
        <p>If you've enabled Google Drive sync, follow these steps to remove cloud data:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Disconnect Sync:</strong> In Stashy popup, go to Settings → Google Integration and click "Disconnect Google Drive"
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Access Google Drive:</strong> Go to <a href="https://drive.google.com" target="_blank">Google Drive</a> in your browser
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Locate Stashy Folder:</strong> Find the "Stashy" folder (or your custom folder name) in your Drive
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Delete Folder:</strong> Right-click the folder and select "Move to trash"
            </div>
            <div class="step">
                <span class="step-number">5.</span> <strong>Empty Trash:</strong> Go to Google Drive trash and permanently delete the folder
            </div>
        </div>

        <div class="note">
            <strong>Auto-Delete Feature:</strong> If you enabled auto-delete for screenshots, they will be automatically removed after your specified time period. You can check this in Settings → Screenshot Settings.
        </div>
    </div>

    <div class="section">
        <h3>📅 Google Calendar Events Removal</h3>
        <p>Remove reminder events created by Stashy in Google Calendar:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Open Google Calendar:</strong> Go to <a href="https://calendar.google.com" target="_blank">Google Calendar</a>
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Search for Stashy Events:</strong> Use the search box to find "Stashy Reminder" events
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Delete Individual Events:</strong> Click each event and select "Delete" or "Move to trash"
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Bulk Deletion:</strong> Select multiple events using Ctrl+Click (Cmd+Click on Mac) and delete together
            </div>
        </div>
    </div>

    <div class="section">
        <h3>📄 Google Docs Exports Removal</h3>
        <p>Remove any documents exported to Google Docs:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Access Google Docs:</strong> Go to <a href="https://docs.google.com" target="_blank">Google Docs</a>
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Find Stashy Exports:</strong> Look for documents with "Stashy Export" in the title or your custom export names
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Delete Documents:</strong> Right-click each document and select "Move to trash"
            </div>
        </div>
    </div>

    <h2>🔐 Secure Data Deletion</h2>

    <div class="section">
        <h3>🔑 API Keys & Credentials Removal</h3>
        <p>Securely remove all stored API keys and authentication data:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>AI API Keys:</strong> Go to Settings → AI Features and click "Clear API Key" to remove encrypted AI provider keys
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Voice API Keys:</strong> Go to Settings → Voice Settings and delete any speech recognition API keys
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Google Authentication:</strong> Revoke Stashy's access in your <a href="https://myaccount.google.com/permissions" target="_blank">Google Account permissions</a>
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Verify Removal:</strong> Check that all API key fields show "Not configured" status
            </div>
        </div>

        <div class="note">
            <strong>Security Note:</strong> API keys are encrypted before storage. Clearing them from Stashy ensures they cannot be recovered, but you should also consider rotating keys on the provider side for maximum security.
        </div>
    </div>

    <div class="section">
        <h3>⚙️ Settings & Preferences Reset</h3>
        <p>Reset all customizations and preferences to default state:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>UI Customization Reset:</strong> Go to Settings → UI Customization and click "Reset to Defaults"
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Note Settings Reset:</strong> Go to Settings → Note Settings and click "Reset to Defaults"
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Screenshot Settings Reset:</strong> Go to Settings → Screenshot Settings and click "Reset to Defaults"
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Voice Settings Reset:</strong> Go to Settings → Voice Settings and click "Reset to Defaults"
            </div>
        </div>
    </div>

    <h2>🚨 Complete Extension Removal</h2>

    <div class="section">
        <h3>🗂️ Pre-Uninstall Data Export (Optional)</h3>
        <p>Before complete removal, you may want to export your data:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Export All Data:</strong> In the dashboard, select all items and click "Export Selected"
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Choose Format:</strong> Select your preferred format (TXT, MD, HTML, or PDF)
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Save Backup:</strong> Download and save the export file to your computer
            </div>
        </div>
    </div>

    <div class="section">
        <h3>❌ Complete Extension Uninstallation</h3>
        <p>Remove Stashy entirely from your browser:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Right-Click Method:</strong> Right-click the Stashy icon in your browser toolbar and select "Remove from Chrome"
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Extension Manager Method:</strong> Go to <code>chrome://extensions</code>, find Stashy, and click "Remove"
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Confirm Removal:</strong> Confirm the uninstallation when prompted
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Restart Browser:</strong> Restart your browser to ensure complete removal
            </div>
        </div>

        <div class="note">
            <strong>Important:</strong> Uninstalling the extension automatically removes all local data. However, cloud data (Google Drive, Calendar, Docs) must be deleted separately using the steps above.
        </div>
    </div>

    <h2>✅ Verification & Cleanup</h2>

    <div class="section">
        <h3>🔍 Verify Complete Deletion</h3>
        <p>Confirm all data has been successfully removed:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Browser Storage:</strong> Check that Stashy no longer appears in <code>chrome://extensions</code>
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Google Drive:</strong> Verify the Stashy folder is not in your Drive or trash
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Google Calendar:</strong> Search for "Stashy" to ensure no reminder events remain
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Google Docs:</strong> Check that no Stashy export documents remain
            </div>
            <div class="step">
                <span class="step-number">5.</span> <strong>Google Permissions:</strong> Verify Stashy is not listed in your <a href="https://myaccount.google.com/permissions" target="_blank">Google Account permissions</a>
            </div>
        </div>
    </div>

    <h2>❓ Frequently Asked Questions</h2>

    <div class="section">
        <h3>🤔 Common Deletion Questions</h3>

        <div class="steps">
            <div class="step">
                <span class="step-number">Q:</span> <strong>Will uninstalling Stashy delete my Google Drive data?</strong>
                <br><strong>A:</strong> No, uninstalling only removes local data. You must manually delete the Stashy folder from Google Drive.
            </div>
            <div class="step">
                <span class="step-number">Q:</span> <strong>Can I recover data after deletion?</strong>
                <br><strong>A:</strong> Local data deletion is permanent. Cloud data may be recoverable from Google's trash for a limited time.
            </div>
            <div class="step">
                <span class="step-number">Q:</span> <strong>How do I delete data from just one device?</strong>
                <br><strong>A:</strong> Use the "Clear Data" option in extension settings to remove local data while keeping cloud sync intact.
            </div>
            <div class="step">
                <span class="step-number">Q:</span> <strong>What happens to my API keys?</strong>
                <br><strong>A:</strong> API keys are encrypted and stored locally. They're automatically deleted when you clear data or uninstall.
            </div>
        </div>
    </div>

    <div class="section">
        <h3>📞 Need Help with Deletion?</h3>
        <p>If you encounter any issues with data deletion or need assistance:</p>

        <ul>
            <li><strong>Email Support:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
            <li><strong>Privacy Inquiries:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></li>
            <li><strong>Response Time:</strong> We typically respond within 24 hours</li>
            <li><strong>Documentation:</strong> See our <a href="privacy-policy.html">Privacy Policy</a> for more information</li>
        </ul>
    </div>

    <p class="last-updated">
        Last updated: January 2025 | Version: 1.0
        <br>This data deletion guide applies to Stashy version 1.0 and later
        <br>We are committed to your right to data deletion and will assist with any questions
    </p>
</body>
</html>
