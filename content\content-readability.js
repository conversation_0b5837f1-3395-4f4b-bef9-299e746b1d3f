// --- START OF FILE content-readability.js ---

/**
 * Enhanced Content Extraction Module using Mozilla's Readability.js
 * 
 * This module provides robust article content extraction using Mozilla's Readability.js
 * library as the primary method, with intelligent fallbacks for when Readability fails.
 * 
 * Features:
 * - Primary extraction using Mozilla's Readability.js
 * - Intelligent fallback mechanisms
 * - Content quality assessment
 * - Site-specific optimizations when needed
 * - Comprehensive error handling
 */

(function() {
    'use strict';

    // Configuration for Readability extraction
    const READABILITY_CONFIG = {
        // Minimum content length to consider extraction successful
        minContentLength: 100,
        
        // Readability.js options
        options: {
            debug: false,
            charThreshold: 500,
            classesToPreserve: ['highlight', 'equation', 'code'],
            keepClasses: false,
            serializer: function(el) {
                return el.innerHTML;
            },
            disableJSONLD: false,
            linkDensityModifier: 0
        },

        // Quality thresholds
        quality: {
            excellent: 0.8,
            good: 0.6,
            acceptable: 0.4,
            poor: 0.2
        }
    };

    /**
     * Enhanced content extraction using Readability.js with intelligent fallbacks
     * @returns {Object} Extraction result with content, metadata, and quality info
     */
    function extractPageContentWithReadability() {
        console.log('Stashy Readability: Starting enhanced content extraction');
        
        const result = {
            content: '',
            title: '',
            byline: '',
            siteName: '',
            excerpt: '',
            length: 0,
            extractionMethod: '',
            quality: {
                score: 0,
                level: 'poor',
                issues: []
            },
            success: false,
            error: null
        };

        try {
            // Method 1: Try Readability.js extraction
            const readabilityResult = attemptReadabilityExtraction();
            
            if (readabilityResult.success) {
                Object.assign(result, readabilityResult);
                result.extractionMethod = 'Mozilla Readability.js';
                console.log(`Stashy Readability: Successfully extracted ${result.length} characters using Readability.js`);
                return result;
            }

            console.log('Stashy Readability: Readability.js failed, trying enhanced fallback methods');

            // Method 2: Enhanced semantic extraction
            const semanticResult = attemptEnhancedSemanticExtraction();
            
            if (semanticResult.success) {
                Object.assign(result, semanticResult);
                result.extractionMethod = 'Enhanced Semantic Extraction';
                console.log(`Stashy Readability: Successfully extracted ${result.length} characters using semantic extraction`);
                return result;
            }

            // Method 3: Site-specific extraction (existing logic)
            const siteSpecificResult = attemptSiteSpecificExtraction();
            
            if (siteSpecificResult.success) {
                Object.assign(result, siteSpecificResult);
                result.extractionMethod = 'Site-Specific Extraction';
                console.log(`Stashy Readability: Successfully extracted ${result.length} characters using site-specific extraction`);
                return result;
            }

            // Method 4: Last resort - filtered body content
            const bodyResult = attemptFilteredBodyExtraction();
            Object.assign(result, bodyResult);
            result.extractionMethod = 'Filtered Body Content (Last Resort)';
            console.log(`Stashy Readability: Using filtered body content as last resort: ${result.length} characters`);

        } catch (error) {
            console.error('Stashy Readability: Critical error during content extraction:', error);
            result.error = error.message;
            result.content = document.body.textContent || '';
            result.extractionMethod = 'Emergency Fallback';
        }

        return result;
    }

    /**
     * Attempts content extraction using Mozilla's Readability.js
     * @returns {Object} Extraction result
     */
    function attemptReadabilityExtraction() {
        const result = {
            content: '',
            title: '',
            byline: '',
            siteName: '',
            excerpt: '',
            length: 0,
            success: false,
            quality: { score: 0, level: 'poor', issues: [] }
        };

        try {
            // Check if Readability is available
            if (typeof Readability === 'undefined') {
                console.warn('Stashy Readability: Readability.js not loaded, skipping');
                return result;
            }

            // Clone the document to avoid modifying the original
            const documentClone = document.cloneNode(true);
            
            // Remove Stashy UI elements from the clone
            removeStashyElements(documentClone);

            // Create Readability instance
            const reader = new Readability(documentClone, READABILITY_CONFIG.options);
            
            // Parse the document
            const article = reader.parse();

            if (article && article.textContent && article.textContent.length >= READABILITY_CONFIG.minContentLength) {
                result.content = article.textContent;
                result.title = article.title || '';
                result.byline = article.byline || '';
                result.siteName = article.siteName || '';
                result.excerpt = article.excerpt || '';
                result.length = article.length || article.textContent.length;
                result.success = true;

                // Assess content quality
                result.quality = assessContentQuality(result.content, 'readability');

                console.log('Stashy Readability: Readability.js extraction successful');
                return result;
            } else {
                console.log('Stashy Readability: Readability.js returned insufficient content');
                result.quality.issues.push('Insufficient content from Readability.js');
            }

        } catch (error) {
            console.warn('Stashy Readability: Readability.js extraction failed:', error);
            result.quality.issues.push(`Readability.js error: ${error.message}`);
        }

        return result;
    }

    /**
     * Enhanced semantic extraction using modern HTML5 semantic elements
     * @returns {Object} Extraction result
     */
    function attemptEnhancedSemanticExtraction() {
        const result = {
            content: '',
            title: '',
            length: 0,
            success: false,
            quality: { score: 0, level: 'poor', issues: [] }
        };

        try {
            // Priority order for semantic elements
            const semanticSelectors = [
                'main article',
                'main',
                'article',
                '[role="main"] article',
                '[role="main"]',
                '[role="article"]',
                '.main-content article',
                '.content article',
                '.post-content',
                '.entry-content',
                '.article-content'
            ];

            for (const selector of semanticSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    const extractedContent = extractCleanContent(element);
                    
                    if (extractedContent.length >= READABILITY_CONFIG.minContentLength) {
                        result.content = extractedContent;
                        result.length = extractedContent.length;
                        result.success = true;
                        result.quality = assessContentQuality(extractedContent, 'semantic');
                        
                        // Try to extract title from nearby headings
                        result.title = extractNearbyTitle(element);
                        
                        console.log(`Stashy Readability: Semantic extraction successful with selector: ${selector}`);
                        return result;
                    }
                }
            }

            result.quality.issues.push('No suitable semantic elements found');

        } catch (error) {
            console.warn('Stashy Readability: Semantic extraction failed:', error);
            result.quality.issues.push(`Semantic extraction error: ${error.message}`);
        }

        return result;
    }

    /**
     * Site-specific extraction for known websites
     * @returns {Object} Extraction result
     */
    function attemptSiteSpecificExtraction() {
        const result = {
            content: '',
            title: '',
            length: 0,
            success: false,
            quality: { score: 0, level: 'poor', issues: [] }
        };

        try {
            const hostname = window.location.hostname.toLowerCase();
            let extractedContent = '';

            // Wikipedia
            if (hostname.includes('wikipedia.org')) {
                extractedContent = extractWikipediaContent();
            }
            // Medium
            else if (hostname.includes('medium.com')) {
                extractedContent = extractMediumContent();
            }
            // Reddit
            else if (hostname.includes('reddit.com')) {
                extractedContent = extractRedditContent();
            }
            // Stack Overflow
            else if (hostname.includes('stackoverflow.com') || hostname.includes('stackexchange.com')) {
                extractedContent = extractStackOverflowContent();
            }
            // GitHub
            else if (hostname.includes('github.com')) {
                extractedContent = extractGitHubContent();
            }

            if (extractedContent && extractedContent.length >= READABILITY_CONFIG.minContentLength) {
                result.content = extractedContent;
                result.length = extractedContent.length;
                result.success = true;
                result.quality = assessContentQuality(extractedContent, 'site-specific');
                result.title = document.title || '';
                
                console.log(`Stashy Readability: Site-specific extraction successful for ${hostname}`);
                return result;
            }

            result.quality.issues.push('Site-specific extraction yielded insufficient content');

        } catch (error) {
            console.warn('Stashy Readability: Site-specific extraction failed:', error);
            result.quality.issues.push(`Site-specific extraction error: ${error.message}`);
        }

        return result;
    }

    /**
     * Last resort: filtered body content extraction
     * @returns {Object} Extraction result
     */
    function attemptFilteredBodyExtraction() {
        const result = {
            content: '',
            title: document.title || '',
            length: 0,
            success: true, // Always succeeds as last resort
            quality: { score: 0, level: 'poor', issues: ['Using body content as last resort'] }
        };

        try {
            const bodyClone = document.body.cloneNode(true);
            
            // Remove unwanted elements
            const unwantedSelectors = [
                'script', 'style', 'noscript', 'iframe', 'nav', 'header', 'footer',
                '.nav', '.navigation', '.menu', '.header', '.footer', '.sidebar',
                '.ad', '.ads', '.advertisement', '.banner', '.social', '.share',
                '.comments', '.comment', '.related', '.recommended'
            ];

            unwantedSelectors.forEach(selector => {
                const elements = bodyClone.querySelectorAll(selector);
                elements.forEach(el => el.remove());
            });

            // Remove Stashy elements
            removeStashyElements(bodyClone);

            result.content = bodyClone.textContent || bodyClone.innerText || '';
            result.length = result.content.length;
            result.quality = assessContentQuality(result.content, 'body-filtered');

            console.log('Stashy Readability: Using filtered body content as fallback');

        } catch (error) {
            console.error('Stashy Readability: Even body extraction failed:', error);
            result.content = document.body.textContent || '';
            result.length = result.content.length;
        }

        return result;
    }

    /**
     * Remove Stashy UI elements from a document or element
     * @param {Element} element - Element to clean
     */
    function removeStashyElements(element) {
        const stashySelectors = [
            '#Stashy-note-container',
            '.Stashy-diagram-editor',
            '#Stashy-flashcard-modal',
            '[id*="Stashy"]',
            '[class*="Stashy"]'
        ];

        stashySelectors.forEach(selector => {
            const elements = element.querySelectorAll(selector);
            elements.forEach(el => el.remove());
        });
    }

    /**
     * Extract clean content from an element
     * @param {Element} element - Element to extract from
     * @returns {string} Clean text content
     */
    function extractCleanContent(element) {
        const clone = element.cloneNode(true);
        
        // Remove unwanted elements
        const unwantedSelectors = [
            'script', 'style', 'noscript', 'iframe', 'nav', 'header', 'footer',
            '.nav', '.navigation', '.menu', '.ad', '.ads', '.advertisement',
            '.social', '.share', '.comments', '.comment'
        ];

        unwantedSelectors.forEach(selector => {
            const elements = clone.querySelectorAll(selector);
            elements.forEach(el => el.remove());
        });

        removeStashyElements(clone);

        return clone.textContent || clone.innerText || '';
    }

    /**
     * Extract title from nearby heading elements
     * @param {Element} contentElement - Content element
     * @returns {string} Extracted title
     */
    function extractNearbyTitle(contentElement) {
        // Look for h1 elements in the content or nearby
        const h1Elements = contentElement.querySelectorAll('h1');
        if (h1Elements.length > 0) {
            return h1Elements[0].textContent.trim();
        }

        // Look for h1 elements in the document
        const documentH1 = document.querySelector('h1');
        if (documentH1) {
            return documentH1.textContent.trim();
        }

        return document.title || '';
    }

    /**
     * Assess the quality of extracted content
     * @param {string} content - Content to assess
     * @param {string} method - Extraction method used
     * @returns {Object} Quality assessment
     */
    function assessContentQuality(content, method) {
        const quality = {
            score: 0,
            level: 'poor',
            issues: []
        };

        if (!content || typeof content !== 'string') {
            quality.issues.push('No content provided');
            return quality;
        }

        const length = content.length;
        const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 10);
        const words = content.split(/\s+/).filter(w => w.length > 0);
        
        // Length scoring
        if (length > 2000) quality.score += 30;
        else if (length > 1000) quality.score += 20;
        else if (length > 500) quality.score += 10;
        else if (length < 100) quality.issues.push('Content too short');

        // Sentence structure scoring
        if (sentences.length > 10) quality.score += 20;
        else if (sentences.length > 5) quality.score += 10;
        else if (sentences.length < 3) quality.issues.push('Very few complete sentences');

        // Word count scoring
        if (words.length > 300) quality.score += 20;
        else if (words.length > 150) quality.score += 10;
        else if (words.length < 50) quality.issues.push('Very few words');

        // Method bonus
        if (method === 'readability') quality.score += 20;
        else if (method === 'semantic') quality.score += 15;
        else if (method === 'site-specific') quality.score += 10;

        // Navigation artifacts penalty
        const navKeywords = ['home', 'about', 'contact', 'menu', 'search', 'login', 'register', 'subscribe'];
        const navCount = navKeywords.reduce((count, keyword) => {
            return count + (content.toLowerCase().split(keyword).length - 1);
        }, 0);

        if (navCount > length / 100) {
            quality.score -= 15;
            quality.issues.push('High navigation content detected');
        }

        // Determine quality level
        quality.score = Math.max(0, Math.min(100, quality.score));
        
        if (quality.score >= 80) quality.level = 'excellent';
        else if (quality.score >= 60) quality.level = 'good';
        else if (quality.score >= 40) quality.level = 'acceptable';
        else quality.level = 'poor';

        return quality;
    }

    /**
     * Site-specific extraction functions
     */

    function extractWikipediaContent() {
        const selectors = [
            '.mw-parser-output',
            '#mw-content-text .mw-parser-output',
            '#bodyContent .mw-parser-output'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                return extractCleanContent(element);
            }
        }
        return '';
    }

    function extractMediumContent() {
        const selectors = [
            'article[data-testid="storyContent"]',
            '.postArticle-content',
            '[data-testid="storyContent"]',
            'article section'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                return extractCleanContent(element);
            }
        }
        return '';
    }

    function extractRedditContent() {
        let content = '';
        const parts = [];

        // Extract post title
        const titleSelectors = [
            '[data-testid="post-content"] h1',
            '.Post h1',
            'h1[data-testid="post-title"]'
        ];

        for (const selector of titleSelectors) {
            const titleElement = document.querySelector(selector);
            if (titleElement) {
                parts.push('Title: ' + titleElement.textContent.trim());
                break;
            }
        }

        // Extract post content
        const postSelectors = [
            '[data-testid="post-content"] [data-click-id="text"]',
            '.Post-body',
            '[data-testid="post-text-container"]'
        ];

        for (const selector of postSelectors) {
            const postElement = document.querySelector(selector);
            if (postElement) {
                parts.push('Post: ' + postElement.textContent.trim());
                break;
            }
        }

        return parts.join('\n\n');
    }

    function extractStackOverflowContent() {
        const parts = [];

        // Extract question title
        const titleElement = document.querySelector('.question-hyperlink, h1[itemprop="name"], .fs-headline1');
        if (titleElement) {
            parts.push('Question: ' + titleElement.textContent.trim());
        }

        // Extract question body
        const questionSelectors = [
            '.question .s-prose',
            '.question .post-text',
            '.question .js-post-body'
        ];

        for (const selector of questionSelectors) {
            const questionBody = document.querySelector(selector);
            if (questionBody) {
                parts.push('Question Details: ' + questionBody.textContent.trim());
                break;
            }
        }

        // Extract accepted answer
        const answerSelectors = [
            '.answer.accepted-answer .s-prose',
            '.answer.accepted-answer .post-text'
        ];

        for (const selector of answerSelectors) {
            const acceptedAnswer = document.querySelector(selector);
            if (acceptedAnswer) {
                parts.push('Accepted Answer: ' + acceptedAnswer.textContent.trim());
                break;
            }
        }

        return parts.join('\n\n');
    }

    function extractGitHubContent() {
        const selectors = [
            '.markdown-body',
            '.readme .Box-body',
            '.repository-content .Box-body',
            'article.markdown-body'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                return extractCleanContent(element);
            }
        }
        return '';
    }

    // Make the main function available globally
    window.extractPageContentWithReadability = extractPageContentWithReadability;

    // Add a test function for debugging
    window.testReadabilityExtraction = function() {
        console.log('Stashy Readability: Running test extraction...');
        const result = extractPageContentWithReadability();
        console.log('Stashy Readability: Test result:', {
            success: result.success,
            method: result.extractionMethod,
            contentLength: result.length,
            quality: result.quality.level,
            score: result.quality.score,
            issues: result.quality.issues
        });
        return result;
    };

    console.log('Stashy Readability: Enhanced content extraction module loaded');
    console.log('Stashy Readability: Available functions:', {
        extractPageContentWithReadability: typeof window.extractPageContentWithReadability,
        testReadabilityExtraction: typeof window.testReadabilityExtraction,
        Readability: typeof window.Readability
    });

})();

// --- END OF FILE content-readability.js ---
