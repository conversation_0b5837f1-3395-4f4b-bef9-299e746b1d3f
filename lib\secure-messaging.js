// --- START OF FILE secure-messaging.js ---

/**
 * Simplified Messaging System for Stashy
 *
 * This module provides basic messaging functionality without complex encryption
 * that was causing performance issues and compatibility problems.
 */

// Create a namespace to avoid global pollution
window.StashySecureMessaging = (function() {

    /**
     * Initializes the messaging system (simplified)
     * @returns {Promise<void>}
     */
    async function initialize() {
        console.log("Stashy: Simplified Messaging System initialized");
    }

    /**
     * Sends a message to the background script (simplified)
     * @param {Object} message - The message to send
     * @returns {Promise<Object>} The response
     */
    async function sendToBackground(message) {
        return new Promise((resolve) => {
            try {
                chrome.runtime.sendMessage(message, response => {
                    const error = chrome.runtime.lastError;
                    if (error) {
                        console.log("Stashy: Error in messaging:", error);
                        resolve(null);
                    } else {
                        resolve(response);
                    }
                });
            } catch (e) {
                console.log("Stashy: Exception in messaging:", e);
                resolve(null);
            }
        });
    }

    /**
     * Sends a message to a tab (simplified)
     * @param {number} tabId - The ID of the tab to send to
     * @param {Object} message - The message to send
     * @returns {Promise<Object>} The response
     */
    async function sendToTab(tabId, message) {
        return new Promise((resolve) => {
            try {
                chrome.tabs.sendMessage(tabId, message, response => {
                    const error = chrome.runtime.lastError;
                    if (error) {
                        console.log(`Stashy: Error in messaging to tab ${tabId}:`, error);
                        resolve(null);
                    } else {
                        resolve(response);
                    }
                });
            } catch (e) {
                console.log(`Stashy: Exception in messaging to tab ${tabId}:`, e);
                resolve(null);
            }
        });
    }

    /**
     * Sets up a simple message listener
     * @param {Function} callback - The callback function(message, sender, sendResponse)
     */
    function setupMessageListener(callback) {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            try {
                const response = callback(message, sender);
                if (response !== undefined) {
                    sendResponse(response);
                }
                return true; // Indicate we'll send a response asynchronously
            } catch (e) {
                console.log("Stashy: Error in message callback:", e);
                sendResponse({ error: 'Error processing message' });
                return true;
            }
        });
    }

    // Return the public API
    return {
        initialize,
        sendToBackground,
        sendToTab,
        setupMessageListener
    };
})();

// Initialize the simplified messaging system
window.StashySecureMessaging.initialize();

console.log("Stashy: Simplified Messaging System Loaded");
// --- END OF FILE secure-messaging.js ---
