/**
 * Stashy Web Worker Manager
 * Provides a centralized system for creating and managing web workers
 * for background processing tasks.
 */

// Ensure we don't initialize multiple times
if (typeof window.StashyWorkerManager === 'undefined') {

    // Create the worker manager as an IIFE to encapsulate private variables
    window.StashyWorkerManager = (function() {
        // Private variables
        const workers = {};
        const taskQueue = {};
        const callbacks = {};
        let workerCounter = 0;

        // Configuration
        const config = {
            maxWorkers: 4, // Maximum number of workers to create
            taskTimeout: 30000, // Default timeout for tasks (30 seconds)
            debug: false // Debug mode disabled
        };

        /**
         * Log messages when debug mode is enabled
         * @param {...any} args - Arguments to log
         */
        function debugLog(...args) {
            if (config.debug) {
                console.log('[StashyWorkerManager]', ...args);
            }
        }

        /**
         * Creates an inline worker that executes specific worker functions directly
         * @param {string} workerId - The ID of the worker
         * @param {string} workerName - The name of the worker script
         * @returns {Object} A mock worker object that executes worker functions
         */
        function createInlineWorker(workerId, workerName) {
            debugLog(`Creating inline worker ${workerId} for ${workerName}`);

            // Create the mock worker object with direct function execution
            const mockWorker = {
                postMessage: function(data) {
                    debugLog(`Inline worker ${workerId} received message:`, data);

                    // Execute worker functions directly based on worker name and action
                    try {
                        const { taskId, action, data: taskData } = data;

                        // Handle different worker types and actions
                        if (workerName === 'image-worker') {
                            handleImageWorkerTask(taskId, action, taskData, mockWorker);
                        } else if (workerName === 'storage-worker') {
                            handleStorageWorkerTask(taskId, action, taskData, mockWorker);
                        } else if (workerName === 'data-worker') {
                            handleDataWorkerTask(taskId, action, taskData, mockWorker);
                        } else {
                            // Fallback for unknown workers
                            setTimeout(() => {
                                if (mockWorker.onmessage) {
                                    mockWorker.onmessage({
                                        data: {
                                            taskId: taskId,
                                            error: `Unknown worker type: ${workerName}`
                                        }
                                    });
                                }
                            }, 10);
                        }

                    } catch (error) {
                        debugLog(`Inline worker ${workerId} error:`, error);
                        if (mockWorker.onerror) {
                            mockWorker.onerror({ message: error.message });
                        }
                    }
                },
                terminate: function() {
                    debugLog(`Terminating inline worker ${workerId}`);
                    this.onmessage = null;
                    this.onerror = null;
                },
                onmessage: null,
                onerror: null
            };

            // Send ready status
            setTimeout(() => {
                if (mockWorker.onmessage) {
                    mockWorker.onmessage({ data: { status: 'ready' } });
                }
            }, 10);

            return mockWorker;
        }

        /**
         * Handles image worker tasks directly in the main thread
         * @param {string} taskId - The task ID
         * @param {string} action - The action to perform
         * @param {Object} taskData - The task data
         * @param {Object} mockWorker - The mock worker object
         */
        async function handleImageWorkerTask(taskId, action, taskData, mockWorker) {
            debugLog(`Handling image worker task: ${action}`);

            try {
                if (action === 'generateThumbnail') {
                    const result = await generateThumbnailInMainThread(taskData);

                    setTimeout(() => {
                        if (mockWorker.onmessage) {
                            mockWorker.onmessage({
                                data: {
                                    taskId: taskId,
                                    result: result
                                }
                            });
                        }
                    }, 10);
                } else {
                    // Unknown action
                    setTimeout(() => {
                        if (mockWorker.onmessage) {
                            mockWorker.onmessage({
                                data: {
                                    taskId: taskId,
                                    error: `Unknown image worker action: ${action}`
                                }
                            });
                        }
                    }, 10);
                }
            } catch (error) {
                debugLog(`Image worker task error:`, error);
                setTimeout(() => {
                    if (mockWorker.onmessage) {
                        mockWorker.onmessage({
                            data: {
                                taskId: taskId,
                                error: `Image worker error: ${error.message}`
                            }
                        });
                    }
                }, 10);
            }
        }

        /**
         * Generates a thumbnail in the main thread (CSP-compliant)
         * @param {Object} data - The thumbnail generation data
         * @returns {Promise<Object>} The thumbnail result
         */
        async function generateThumbnailInMainThread(data) {
            const { imageDataUrl, maxWidth, maxHeight, quality, format } = data;

            return new Promise((resolve, reject) => {
                // Create an image element to load the source
                const img = new Image();

                img.onload = function() {
                    try {
                        // Calculate thumbnail dimensions while maintaining aspect ratio
                        let thumbnailWidth = img.width;
                        let thumbnailHeight = img.height;

                        if (thumbnailWidth > maxWidth || thumbnailHeight > maxHeight) {
                            const aspectRatio = thumbnailWidth / thumbnailHeight;

                            if (thumbnailWidth > thumbnailHeight) {
                                thumbnailWidth = maxWidth;
                                thumbnailHeight = maxWidth / aspectRatio;
                            } else {
                                thumbnailHeight = maxHeight;
                                thumbnailWidth = maxHeight * aspectRatio;
                            }

                            // Ensure we don't exceed either dimension
                            if (thumbnailWidth > maxWidth) {
                                thumbnailWidth = maxWidth;
                                thumbnailHeight = maxWidth / aspectRatio;
                            }
                            if (thumbnailHeight > maxHeight) {
                                thumbnailHeight = maxHeight;
                                thumbnailWidth = maxHeight * aspectRatio;
                            }
                        }

                        // Create a canvas for the thumbnail
                        const canvas = document.createElement('canvas');
                        canvas.width = Math.round(thumbnailWidth);
                        canvas.height = Math.round(thumbnailHeight);

                        const ctx = canvas.getContext('2d', { willReadFrequently: true });

                        // Enable high-quality scaling
                        ctx.imageSmoothingEnabled = true;
                        ctx.imageSmoothingQuality = 'high';

                        // Draw the resized image
                        ctx.drawImage(img, 0, 0, Math.round(thumbnailWidth), Math.round(thumbnailHeight));

                        // Convert to the specified format with compression
                        const thumbnailDataUrl = canvas.toDataURL(
                            format || 'image/jpeg',
                            quality || 0.85
                        );

                        resolve({
                            thumbnailDataUrl,
                            originalWidth: img.width,
                            originalHeight: img.height,
                            thumbnailWidth: Math.round(thumbnailWidth),
                            thumbnailHeight: Math.round(thumbnailHeight),
                            success: true
                        });

                    } catch (error) {
                        reject(new Error(`Thumbnail generation error: ${error.message}`));
                    }
                };

                img.onerror = function() {
                    reject(new Error('Failed to load image for thumbnail generation'));
                };

                img.src = imageDataUrl;
            });
        }

        /**
         * Handles storage worker tasks directly in the main thread
         * @param {string} taskId - The task ID
         * @param {string} action - The action to perform
         * @param {Object} taskData - The task data
         * @param {Object} mockWorker - The mock worker object
         */
        async function handleStorageWorkerTask(taskId, action, taskData, mockWorker) {
            debugLog(`Handling storage worker task: ${action}`);

            try {
                let result;

                switch (action) {
                    case 'processNoteData':
                        result = await processNoteDataInMainThread(taskData);
                        break;

                    case 'processHighlightData':
                        result = await processHighlightDataInMainThread(taskData);
                        break;

                    case 'compressData':
                        result = await compressDataInMainThread(taskData);
                        break;

                    case 'decompressData':
                        result = await decompressDataInMainThread(taskData);
                        break;

                    case 'batchProcess':
                        result = await batchProcessInMainThread(taskData);
                        break;

                    default:
                        throw new Error(`Unknown storage worker action: ${action}`);
                }

                setTimeout(() => {
                    if (mockWorker.onmessage) {
                        mockWorker.onmessage({
                            data: {
                                taskId: taskId,
                                result: result
                            }
                        });
                    }
                }, 10);

            } catch (error) {
                debugLog(`Storage worker task error:`, error);
                setTimeout(() => {
                    if (mockWorker.onmessage) {
                        mockWorker.onmessage({
                            data: {
                                taskId: taskId,
                                error: `Storage worker error: ${error.message}`
                            }
                        });
                    }
                }, 10);
            }
        }

        /**
         * Processes note data in the main thread (CSP-compliant)
         * @param {Object} data - The note data to process
         * @returns {Promise<Object>} The processing result
         */
        async function processNoteDataInMainThread(data) {
            const { noteData, options } = data;

            // Clone the note data to avoid modifying the original
            const processedData = JSON.parse(JSON.stringify(noteData));

            // Add timestamp if not present
            if (!processedData.timestamp) {
                processedData.timestamp = Date.now();
            }

            // Update last modified timestamp
            processedData.lastModified = Date.now();

            // Generate a word count if text is present
            if (processedData.text) {
                processedData.wordCount = processedData.text.split(/\s+/).filter(Boolean).length;
            }

            return {
                processedData,
                success: true
            };
        }

        /**
         * Processes highlight data in the main thread (CSP-compliant)
         * @param {Object} data - The highlight data to process
         * @returns {Promise<Object>} The processing result
         */
        async function processHighlightDataInMainThread(data) {
            const { highlightData, options } = data;

            // Clone the highlight data to avoid modifying the original
            const processedData = JSON.parse(JSON.stringify(highlightData));

            // Add timestamp if not present
            if (!processedData.timestamp) {
                processedData.timestamp = Date.now();
            }

            // Update last modified timestamp
            processedData.lastModified = Date.now();

            return {
                processedData,
                success: true
            };
        }

        /**
         * Compresses data in the main thread (CSP-compliant)
         * @param {Object} data - The data to compress
         * @returns {Promise<Object>} The compression result
         */
        async function compressDataInMainThread(data) {
            const { input, algorithm, options } = data;

            // Simple compression simulation (in real implementation, use proper compression)
            const compressed = input; // For now, just return the input

            return {
                compressed,
                success: true
            };
        }

        /**
         * Decompresses data in the main thread (CSP-compliant)
         * @param {Object} data - The data to decompress
         * @returns {Promise<Object>} The decompression result
         */
        async function decompressDataInMainThread(data) {
            const { input, algorithm, options } = data;

            // Simple decompression simulation (in real implementation, use proper decompression)
            const decompressed = input; // For now, just return the input

            return {
                decompressed,
                success: true
            };
        }

        /**
         * Processes a batch of items in the main thread (CSP-compliant)
         * @param {Object} data - The batch data to process
         * @returns {Promise<Object>} The batch processing result
         */
        async function batchProcessInMainThread(data) {
            const { items, processType, options } = data;

            const results = items.map(item => {
                const processedItem = JSON.parse(JSON.stringify(item));

                // Process based on type
                switch (processType) {
                    case 'note':
                        if (!processedItem.timestamp) {
                            processedItem.timestamp = Date.now();
                        }
                        processedItem.lastModified = Date.now();

                        if (processedItem.text) {
                            processedItem.wordCount = processedItem.text.split(/\s+/).filter(Boolean).length;
                        }
                        break;

                    case 'highlight':
                        if (!processedItem.timestamp) {
                            processedItem.timestamp = Date.now();
                        }
                        processedItem.lastModified = Date.now();
                        break;

                    default:
                        processedItem.processed = true;
                        processedItem.processingTimestamp = Date.now();
                        break;
                }

                return processedItem;
            });

            return {
                results,
                count: results.length,
                success: true
            };
        }

        /**
         * Handles data worker tasks (placeholder)
         * @param {string} taskId - The task ID
         * @param {string} action - The action to perform
         * @param {Object} taskData - The task data
         * @param {Object} mockWorker - The mock worker object
         */
        function handleDataWorkerTask(taskId, action, taskData, mockWorker) {
            // Placeholder for data worker tasks
            setTimeout(() => {
                if (mockWorker.onmessage) {
                    mockWorker.onmessage({
                        data: {
                            taskId: taskId,
                            error: 'Data worker not implemented'
                        }
                    });
                }
            }, 10);
        }

        /**
         * Creates a simple fallback worker that just returns empty results for CSP compliance
         * @param {string} workerId - The worker ID
         * @returns {Object} Mock worker object
         */
        function createFallbackWorker(workerId) {
            debugLog(`Creating fallback worker for ${workerId}`);

            const mockWorker = {
                onmessage: null,
                onerror: null,
                postMessage: function(data) {
                    // Simple fallback - just return success with empty data
                    setTimeout(() => {
                        try {
                            if (mockWorker.onmessage) {
                                const { taskId, action } = data;

                                // Return appropriate fallback response based on action
                                let result;
                                switch (action) {
                                    case 'processNoteData':
                                        // Match the expected structure from storage-worker.js
                                        const noteData = data.data?.noteData || data.data || {};
                                        result = {
                                            success: true,
                                            processedData: {
                                                ...noteData,
                                                lastModified: Date.now(),
                                                wordCount: noteData.text ? noteData.text.split(/\s+/).filter(Boolean).length : 0
                                            }
                                        };
                                        break;
                                    case 'processHighlightData':
                                        // Match the expected structure for highlight processing
                                        const highlightData = data.data?.highlightData || data.data || {};
                                        result = {
                                            success: true,
                                            processedData: {
                                                ...highlightData,
                                                lastModified: Date.now()
                                            }
                                        };
                                        break;
                                    case 'compressData':
                                        result = { success: true, compressed: data.data?.input || data.data || '' };
                                        break;
                                    case 'decompressData':
                                        result = { success: true, decompressed: data.data?.input || data.data || '' };
                                        break;
                                    case 'encryptData':
                                        result = { success: true, encrypted: data.data?.input || data.data || '' };
                                        break;
                                    case 'decryptData':
                                        result = { success: true, decrypted: data.data?.input || data.data || '' };
                                        break;
                                    case 'batchProcess':
                                        result = { success: true, processedItems: data.data?.items || [] };
                                        break;
                                    case 'convertFormat':
                                        // For image format conversion, just return the original image
                                        result = {
                                            success: true,
                                            convertedDataUrl: data.data?.imageDataUrl || data.data?.input || null,
                                            format: data.data?.format || 'image/png'
                                        };
                                        break;
                                    case 'processImage':
                                        // For image processing, just return the original image
                                        result = {
                                            success: true,
                                            processedDataUrl: data.data?.imageDataUrl || data.data?.input || null,
                                            width: data.data?.width || 0,
                                            height: data.data?.height || 0
                                        };
                                        break;
                                    case 'resizeImage':
                                        // For image resizing, just return the original image
                                        result = {
                                            success: true,
                                            resizedDataUrl: data.data?.imageDataUrl || data.data?.input || null,
                                            width: data.data?.width || 0,
                                            height: data.data?.height || 0
                                        };
                                        break;
                                    case 'applyFilter':
                                        // For image filtering, just return the original image
                                        result = {
                                            success: true,
                                            filteredDataUrl: data.data?.imageDataUrl || data.data?.input || null,
                                            filter: data.data?.filter || 'none'
                                        };
                                        break;
                                    case 'addOverlays':
                                        // For overlay addition, just return the original image
                                        result = {
                                            success: true,
                                            overlaidDataUrl: data.data?.imageDataUrl || data.data?.input || null
                                        };
                                        break;
                                    default:
                                        result = { success: true, data: null };
                                }

                                mockWorker.onmessage({
                                    data: {
                                        taskId: taskId,
                                        result: result,
                                        error: null
                                    }
                                });
                            }
                        } catch (error) {
                            debugLog(`Fallback worker error: ${error.message}`);
                            if (mockWorker.onerror) {
                                mockWorker.onerror({ message: error.message });
                            }
                        }
                    }, 10); // Small delay to simulate async processing
                },
                terminate: function() {
                    debugLog(`Terminating fallback worker ${workerId}`);
                    this.onmessage = null;
                    this.onerror = null;
                }
            };

            return mockWorker;
        }

        /**
         * Creates a new worker with the specified script
         * @param {string} scriptPath - Path to the worker script
         * @param {string} workerId - Optional ID for the worker
         * @returns {string} The worker ID
         */
        function createWorker(scriptPath, workerId = null) {


            // Generate a worker ID if not provided
            const id = workerId || `worker-${++workerCounter}`;

            // Check if we already have a worker with this ID
            if (workers[id]) {
                debugLog(`Worker ${id} already exists`);
                return id;
            }

            try {
                // Create a new worker with proper URL handling
                // First try to fetch the worker script and create a blob URL
                debugLog(`Attempting to create worker for script: ${scriptPath}`);

                // Try different methods to create a worker
                let worker;

                // SECURITY FIX: Only use chrome.runtime.getURL for local extension resources
                try {
                    // Validate that the script path is a local extension resource
                    if (!scriptPath.startsWith('workers/') && !scriptPath.startsWith('lib/')) {
                        throw new Error(`Security: Only local extension workers are allowed. Path: ${scriptPath}`);
                    }

                    const extensionUrl = chrome.runtime.getURL(scriptPath);
                    debugLog(`Creating worker with secure extension URL: ${extensionUrl}`);
                    worker = new Worker(extensionUrl);
                } catch (e) {
                    debugLog(`Failed to create worker with extension URL: ${e.message}`);

                    // SECURITY FIX: Use only inline worker scripts (no blob URLs)
                    try {
                        // Extract the worker name from the path
                        const workerName = scriptPath.split('/').pop().replace('.js', '');

                        // Check if we have an inline worker script for this worker
                        if (window.StashyInlineWorkers && window.StashyInlineWorkers[workerName]) {
                            debugLog(`Using secure inline worker script for ${workerName}`);

                            // For CSP compliance, we'll simulate worker behavior instead of creating actual workers
                            debugLog(`CSP detected, falling back to secure main thread execution for ${workerName}`);

                            // Create a specialized inline worker that executes the inline script
                            worker = createInlineWorker(id, workerName);

                            // Set up message handler
                            worker.onmessage = function(event) {
                                handleWorkerMessage(id, event);
                            };

                            // Set up error handler
                            worker.onerror = function(error) {
                                // Reduce noise for CSP-related errors
                                if (error.message && error.message.includes('Content Security Policy')) {
                                    debugLog(`CSP-related worker error in ${id}, using fallback`);
                                } else {
                                    console.error(`Error in worker ${id}:`, error);
                                }

                                // Find all pending tasks for this worker and reject them
                                const pendingTasks = taskQueue[id] || [];
                                pendingTasks.forEach(taskId => {
                                    if (callbacks[taskId]) {
                                        callbacks[taskId].reject(new Error(`Worker error: ${error.message || 'Unknown error'}`));
                                        delete callbacks[taskId];
                                    }
                                });

                                // Clean up (no blobUrl for mock workers)
                                taskQueue[id] = [];
                                terminateWorker(id);
                            };

                            // Store the worker
                            workers[id] = {
                                instance: worker,
                                scriptPath: scriptPath,
                                isInline: true,
                                isMock: true,
                                busy: false,
                                lastUsed: Date.now()
                            };

                            debugLog(`Created worker ${id} with inline script`);
                            return id;
                        } else {
                            // Method 3: Fallback - create a simple fallback worker for unsupported scripts
                            debugLog(`No inline worker script found for ${workerName}, creating fallback worker`);

                            worker = createFallbackWorker(id);

                            // Set up message handler
                            worker.onmessage = function(event) {
                                handleWorkerMessage(id, event);
                            };

                            // Set up error handler
                            worker.onerror = function(error) {
                                // Reduce noise for CSP-related errors
                                if (error.message && error.message.includes('Content Security Policy')) {
                                    debugLog(`CSP-related worker error in ${id}, using fallback`);
                                } else {
                                    console.error(`Error in worker ${id}:`, error);
                                }

                                // Find all pending tasks for this worker and reject them
                                const pendingTasks = taskQueue[id] || [];
                                pendingTasks.forEach(taskId => {
                                    if (callbacks[taskId]) {
                                        callbacks[taskId].reject(new Error(`Worker error: ${error.message || 'Unknown error'}`));
                                        delete callbacks[taskId];
                                    }
                                });

                                // Clean up
                                taskQueue[id] = [];
                                terminateWorker(id);
                            };

                            // Store the worker
                            workers[id] = {
                                instance: worker,
                                scriptPath: scriptPath,
                                isMock: true,
                                busy: false,
                                lastUsed: Date.now()
                            };

                            debugLog(`Created fallback mock worker ${id}`);
                            return id;
                        }
                    } catch (error) {
                        console.error(`Failed to create worker ${id} with fallback methods:`, error);
                        throw new Error(`Could not create worker: ${error.message}`);
                    }
                }

                // If we got here, Method 1 worked and we have a worker
                // Initialize task queue for this worker
                taskQueue[id] = [];

                // Set up message handler for Method 1
                worker.onmessage = function(event) {
                    handleWorkerMessage(id, event);
                };

                // Set up error handler for Method 1
                worker.onerror = function(error) {
                    // Reduce noise for CSP-related errors
                    if (error.message && error.message.includes('Content Security Policy')) {
                        debugLog(`CSP-related worker error in ${id}, using fallback`);
                    } else {
                        console.error(`Error in worker ${id}:`, error);
                    }

                    // Find all pending tasks for this worker and reject them
                    const pendingTasks = taskQueue[id] || [];
                    pendingTasks.forEach(taskId => {
                        if (callbacks[taskId]) {
                            callbacks[taskId].reject(new Error(`Worker error: ${error.message || 'Unknown error'}`));
                            delete callbacks[taskId];
                        }
                    });

                    // Clear the task queue
                    taskQueue[id] = [];

                    // Terminate and recreate the worker
                    terminateWorker(id);
                    createWorker(scriptPath, id);
                };

                // Store the worker for Method 1
                workers[id] = {
                    instance: worker,
                    scriptPath: scriptPath,
                    busy: false,
                    lastUsed: Date.now()
                };

                debugLog(`Created worker ${id} with script ${scriptPath}`);
                return id;
            } catch (error) {
                console.error(`Failed to create worker ${id}:`, error);
                throw error;
            }
        }

        /**
         * Handles messages from workers
         * @param {string} workerId - The ID of the worker
         * @param {MessageEvent} event - The message event
         */
        function handleWorkerMessage(workerId, event) {
            const { taskId, result, error, status } = event.data;

            // Check if this is a status update
            if (status === 'ready') {
                debugLog(`Worker ${workerId} is ready`);
                processNextTask(workerId);
                return;
            }

            // Find the callback for this task
            if (callbacks[taskId]) {
                if (error) {
                    callbacks[taskId].reject(new Error(error));
                } else {
                    callbacks[taskId].resolve(result);
                }

                // Remove the callback
                delete callbacks[taskId];

                // Remove the task from the queue if it exists
                if (taskQueue[workerId]) {
                    const index = taskQueue[workerId].indexOf(taskId);
                    if (index !== -1) {
                        taskQueue[workerId].splice(index, 1);
                    }
                }

                // Mark the worker as not busy
                if (workers[workerId]) {
                    workers[workerId].busy = false;
                    workers[workerId].lastUsed = Date.now();
                }

                // Process the next task in the queue
                processNextTask(workerId);
            }
        }

        /**
         * Processes the next task in the queue for a worker
         * @param {string} workerId - The ID of the worker
         */
        function processNextTask(workerId) {
            // Check if the worker exists and is not busy
            if (!workers[workerId] || workers[workerId].busy) {
                return;
            }

            // Check if there are tasks in the queue
            if (taskQueue[workerId] && taskQueue[workerId].length > 0) {
                const taskId = taskQueue[workerId][0];

                // Find the task data
                if (callbacks[taskId] && callbacks[taskId].taskData) {
                    // Mark the worker as busy
                    workers[workerId].busy = true;

                    // Send the task to the worker
                    workers[workerId].instance.postMessage({
                        taskId: taskId,
                        action: callbacks[taskId].taskData.action,
                        data: callbacks[taskId].taskData.data
                    });

                    debugLog(`Sent task ${taskId} to worker ${workerId}`);

                    // Set up a timeout for the task
                    const timeout = callbacks[taskId].taskData.timeout || config.taskTimeout;
                    callbacks[taskId].timeoutId = setTimeout(() => {
                        if (callbacks[taskId]) {
                            callbacks[taskId].reject(new Error(`Task ${taskId} timed out after ${timeout}ms`));
                            delete callbacks[taskId];

                            // Remove the task from the queue if it exists
                            if (taskQueue[workerId]) {
                                const index = taskQueue[workerId].indexOf(taskId);
                                if (index !== -1) {
                                    taskQueue[workerId].splice(index, 1);
                                }
                            }

                            // Mark the worker as not busy
                            if (workers[workerId]) {
                                workers[workerId].busy = false;
                                workers[workerId].lastUsed = Date.now();
                            }

                            // Process the next task in the queue
                            processNextTask(workerId);
                        }
                    }, timeout);
                }
            }
        }

        /**
         * Finds an available worker or creates a new one if needed
         * @param {string} scriptPath - Path to the worker script
         * @returns {string|null} The worker ID or null if creation failed
         */
        function getAvailableWorker(scriptPath) {
            // First, try to find an existing worker with the same script that's not busy
            for (const id in workers) {
                if (workers[id].scriptPath === scriptPath && !workers[id].busy) {
                    return id;
                }
            }

            // If we have fewer than maxWorkers, create a new one
            if (Object.keys(workers).length < config.maxWorkers) {
                try {
                    return createWorker(scriptPath);
                } catch (error) {
                    console.error(`Failed to create new worker:`, error);
                    return null;
                }
            }

            // Otherwise, find the least recently used worker
            let oldestWorkerId = null;
            let oldestTime = Infinity;

            for (const id in workers) {
                if (workers[id].lastUsed < oldestTime) {
                    oldestWorkerId = id;
                    oldestTime = workers[id].lastUsed;
                }
            }

            // Terminate the oldest worker and create a new one
            if (oldestWorkerId) {
                terminateWorker(oldestWorkerId);
                try {
                    return createWorker(scriptPath);
                } catch (error) {
                    console.error(`Failed to create replacement worker:`, error);
                    return null;
                }
            }

            // If we couldn't find a worker to replace, return null
            console.warn('Could not create or find available worker');
            return null;
        }

        /**
         * Terminates a worker
         * @param {string} workerId - The ID of the worker to terminate
         */
        function terminateWorker(workerId) {
            if (workers[workerId]) {
                debugLog(`Terminating worker ${workerId}`);

                // Terminate the worker
                workers[workerId].instance.terminate();

                // Clean up blob URL if it exists
                if (workers[workerId].blobUrl) {
                    debugLog(`Revoking blob URL for worker ${workerId}`);
                    URL.revokeObjectURL(workers[workerId].blobUrl);
                }

                // Remove the worker
                delete workers[workerId];

                // Clear the task queue
                delete taskQueue[workerId];
            }
        }

        /**
         * Runs a task in a worker
         * @param {string} scriptPath - Path to the worker script
         * @param {string} action - The action to perform
         * @param {any} data - The data to pass to the worker
         * @param {Object} options - Options for the task
         * @returns {Promise<any>} A promise that resolves with the result of the task
         */
        function runTask(scriptPath, action, data, options = {}) {
            return new Promise((resolve, reject) => {
                try {
                    // Generate a task ID
                    const taskId = `task-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

                    // Get an available worker
                    const workerId = getAvailableWorker(scriptPath);

                    // Check if worker creation failed (e.g., due to CSP restrictions)
                    if (!workerId) {
                        debugLog('Worker creation failed, falling back to main thread execution');
                        // Fallback to main thread execution
                        setTimeout(() => {
                            try {
                                // Simple fallback - just return the data as-is
                                resolve({ success: true, data: data });
                            } catch (error) {
                                reject(error);
                            }
                        }, 0);
                        return;
                    }

                    // Store the callback
                    callbacks[taskId] = {
                        resolve,
                        reject,
                        taskData: {
                            action,
                            data,
                            timeout: options.timeout || config.taskTimeout
                        }
                    };

                    // Ensure the task queue exists before pushing to it
                    if (!taskQueue[workerId]) {
                        taskQueue[workerId] = [];
                    }

                    // Add the task to the queue
                    taskQueue[workerId].push(taskId);

                    // Process the task if the worker is not busy
                    if (!workers[workerId].busy) {
                        processNextTask(workerId);
                    }
                } catch (error) {
                    reject(error);
                }
            });
        }

        /**
         * Terminates all workers
         */
        function terminateAll() {
            for (const id in workers) {
                terminateWorker(id);
            }
        }

        /**
         * Updates the configuration
         * @param {Object} newConfig - The new configuration
         */
        function updateConfig(newConfig) {
            Object.assign(config, newConfig);
        }

        // Set up cleanup on page unload
        window.addEventListener('beforeunload', terminateAll);

        // Return the public API
        return {
            createWorker,
            runTask,
            terminateWorker,
            terminateAll,
            updateConfig,
            getConfig: () => ({ ...config }),
            getWorkerCount: () => Object.keys(workers).length,
            enableDebug: () => { config.debug = true; },
            disableDebug: () => { config.debug = false; }
        };
    })();

    console.log("Stashy: Worker Manager Loaded");
}
