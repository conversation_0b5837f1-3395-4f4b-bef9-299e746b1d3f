/**
 * Stashy Screenshot Settings
 * 
 * This script handles the screenshot settings UI and storage.
 */

document.addEventListener('DOMContentLoaded', () => {
    // Get UI elements
    const storageLocationSelect = document.getElementById('storage-location');
    const driveSettingsDiv = document.getElementById('drive-settings');
    const driveFolderNameInput = document.getElementById('drive-folder-name');
    const useAppDataFolderCheckbox = document.getElementById('use-app-data-folder');
    const autoDeleteAfterInput = document.getElementById('auto-delete-after');
    const askBeforeUploadCheckbox = document.getElementById('ask-before-upload');
    const saveBtn = document.getElementById('save-btn');
    const cancelBtn = document.getElementById('cancel-btn');
    
    // Load saved settings
    loadSettings();
    
    // Add event listeners
    storageLocationSelect.addEventListener('change', toggleDriveSettings);
    saveBtn.addEventListener('click', saveSettings);
    cancelBtn.addEventListener('click', () => window.close());
    
    /**
     * Toggles the visibility of Drive settings based on storage location
     */
    function toggleDriveSettings() {
        const showDriveSettings = storageLocationSelect.value === 'drive' || storageLocationSelect.value === 'both';
        driveSettingsDiv.classList.toggle('hidden', !showDriveSettings);
    }
    
    /**
     * Loads saved settings from storage
     */
    function loadSettings() {
        chrome.storage.local.get(['screenshotStoragePrefs'], result => {
            const defaultPrefs = {
                storageLocation: 'local',
                driveFolderName: 'Stashy Screenshots',
                useAppDataFolder: true,
                autoDeleteAfter: 0,
                askBeforeUpload: true
            };
            
            const prefs = result.screenshotStoragePrefs || defaultPrefs;
            
            // Set UI values
            storageLocationSelect.value = prefs.storageLocation;
            driveFolderNameInput.value = prefs.driveFolderName;
            useAppDataFolderCheckbox.checked = prefs.useAppDataFolder;
            autoDeleteAfterInput.value = prefs.autoDeleteAfter;
            askBeforeUploadCheckbox.checked = prefs.askBeforeUpload;
            
            // Toggle Drive settings visibility
            toggleDriveSettings();
        });
    }
    
    /**
     * Saves settings to storage
     */
    function saveSettings() {
        const prefs = {
            storageLocation: storageLocationSelect.value,
            driveFolderName: driveFolderNameInput.value.trim() || 'Stashy Screenshots',
            useAppDataFolder: useAppDataFolderCheckbox.checked,
            autoDeleteAfter: parseInt(autoDeleteAfterInput.value) || 0,
            askBeforeUpload: askBeforeUploadCheckbox.checked
        };
        
        // Validate settings
        if (prefs.driveFolderName.length === 0) {
            alert('Drive folder name cannot be empty');
            driveFolderNameInput.focus();
            return;
        }
        
        if (prefs.autoDeleteAfter < 0) {
            alert('Auto-delete days must be 0 or greater');
            autoDeleteAfterInput.focus();
            return;
        }
        
        // Save settings
        chrome.storage.local.set({ screenshotStoragePrefs: prefs }, () => {
            // Show success message
            const saveBtn = document.getElementById('save-btn');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = 'Saved!';
            saveBtn.disabled = true;
            
            setTimeout(() => {
                saveBtn.textContent = originalText;
                saveBtn.disabled = false;
                window.close();
            }, 1500);
        });
    }
});
