/**
 * Stashy Data Encryption
 * Provides encryption for sensitive data stored in IndexedDB
 * using the Web Crypto API
 */

// Create a namespace to avoid global pollution
window.StashyEncryption = (function() {
    // Encryption algorithms
    const ALGORITHMS = {
        AES_GCM: 'AES-GCM',       // AES in Galois/Counter Mode
        AES_CBC: 'AES-CBC',       // AES in Cipher Block Chaining Mode
        RSA: 'RSA-OAEP'           // RSA with OAEP padding
    };

    // Key types
    const KEY_TYPES = {
        SYMMETRIC: 'symmetric',    // Symmetric encryption (same key for encrypt/decrypt)
        ASYMMETRIC: 'asymmetric'   // Asymmetric encryption (public/private key pair)
    };

    // Configuration
    const config = {
        defaultAlgorithm: ALGORITHMS.AES_GCM,  // Default encryption algorithm
        keySize: 256,                          // Key size in bits
        saltSize: 16,                          // Salt size in bytes
        ivSize: 12,                            // Initialization vector size in bytes
        iterationCount: 100000,                // PBKDF2 iteration count
        storagePrefix: 'Stashy_encrypted_',  // Prefix for encrypted items
        autoEncrypt: false,                    // Automatically encrypt sensitive data
        sensitiveFields: [                     // Fields that should be encrypted
            'password', 'token', 'apiKey', 'secret', 'credentials', 'creditCard'
        ],
        debug: false                           // Debug mode
    };

    // Private variables
    let encryptionKey = null;
    let isInitialized = false;
    let pendingOperations = [];
    let keyPromise = null;

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StashyEncryption]', ...args);
        }
    }

    /**
     * Initializes the encryption module
     * @param {string} [password] - Optional password for key derivation
     * @returns {Promise<boolean>} A promise that resolves with true if initialization was successful
     */
    async function init(password) {
        try {
            // Check if Web Crypto API is available
            if (!window.crypto || !window.crypto.subtle) {
                console.error('Stashy: Web Crypto API not available');
                return false;
            }

            // Generate or retrieve encryption key
            if (password) {
                // Derive key from password
                encryptionKey = await deriveKeyFromPassword(password);
            } else {
                // Get or generate a stored key
                encryptionKey = await getOrGenerateKey();
            }

            isInitialized = true;
            debugLog('Encryption module initialized');

            // Process any pending operations
            processPendingOperations();

            return true;
        } catch (error) {
            console.error('Stashy: Error initializing encryption module:', error);
            return false;
        }
    }

    /**
     * Processes pending encryption/decryption operations
     */
    function processPendingOperations() {
        const operations = [...pendingOperations];
        pendingOperations = [];

        for (const op of operations) {
            if (op.type === 'encrypt') {
                op.resolve(encrypt(op.data, op.options));
            } else if (op.type === 'decrypt') {
                op.resolve(decrypt(op.data, op.options));
            } else {
                op.reject(new Error(`Unknown operation type: ${op.type}`));
            }
        }
    }

    /**
     * Derives an encryption key from a password
     * @param {string} password - The password
     * @returns {Promise<CryptoKey>} A promise that resolves with the derived key
     */
    async function deriveKeyFromPassword(password) {
        try {
            // Convert password to buffer
            const encoder = new TextEncoder();
            const passwordBuffer = encoder.encode(password);

            // Generate a salt
            const salt = window.crypto.getRandomValues(new Uint8Array(config.saltSize));

            // Import the password as a key
            const baseKey = await window.crypto.subtle.importKey(
                'raw',
                passwordBuffer,
                { name: 'PBKDF2' },
                false,
                ['deriveKey']
            );

            // Derive the actual encryption key
            const derivedKey = await window.crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: salt,
                    iterations: config.iterationCount,
                    hash: 'SHA-256'
                },
                baseKey,
                {
                    name: config.defaultAlgorithm,
                    length: config.keySize
                },
                true, // extractable
                ['encrypt', 'decrypt']
            );

            // Store the salt for future use
            await storeSalt(salt);

            return derivedKey;
        } catch (error) {
            console.error('Stashy: Error deriving key from password:', error);
            throw error;
        }
    }

    /**
     * Stores the salt for password-based key derivation
     * @param {Uint8Array} salt - The salt to store
     * @returns {Promise<void>} A promise that resolves when the salt is stored
     */
    async function storeSalt(salt) {
        try {
            // Convert salt to hex string
            const saltHex = Array.from(salt)
                .map(b => b.toString(16).padStart(2, '0'))
                .join('');

            // Store in local storage
            localStorage.setItem('Stashy_encryption_salt', saltHex);
        } catch (error) {
            console.error('Stashy: Error storing salt:', error);
            throw error;
        }
    }

    /**
     * Retrieves the stored salt
     * @returns {Promise<Uint8Array|null>} A promise that resolves with the salt or null
     */
    async function getSalt() {
        try {
            // Get from local storage
            const saltHex = localStorage.getItem('Stashy_encryption_salt');

            if (!saltHex) {
                return null;
            }

            // Convert hex string to Uint8Array
            const salt = new Uint8Array(saltHex.match(/.{1,2}/g).map(byte => parseInt(byte, 16)));

            return salt;
        } catch (error) {
            console.error('Stashy: Error retrieving salt:', error);
            return null;
        }
    }

    /**
     * Gets or generates an encryption key
     * @returns {Promise<CryptoKey>} A promise that resolves with the encryption key
     */
    async function getOrGenerateKey() {
        // Return existing promise if one is in progress
        if (keyPromise) {
            return keyPromise;
        }

        keyPromise = (async () => {
            try {
                // Try to retrieve the key from storage
                const storedKey = await getStoredKey();

                if (storedKey) {
                    debugLog('Using stored encryption key');
                    return storedKey;
                }

                // Generate a new key
                debugLog('Generating new encryption key');
                const key = await window.crypto.subtle.generateKey(
                    {
                        name: config.defaultAlgorithm,
                        length: config.keySize
                    },
                    true, // extractable
                    ['encrypt', 'decrypt']
                );

                // Store the key
                await storeKey(key);

                return key;
            } catch (error) {
                console.error('Stashy: Error getting/generating encryption key:', error);
                throw error;
            } finally {
                keyPromise = null;
            }
        })();

        return keyPromise;
    }

    /**
     * Retrieves the stored encryption key
     * @returns {Promise<CryptoKey|null>} A promise that resolves with the key or null
     */
    async function getStoredKey() {
        try {
            // Check if we have a stored key
            const keyData = localStorage.getItem('Stashy_encryption_key');

            if (!keyData) {
                return null;
            }

            // Convert from base64
            const keyBuffer = base64ToArrayBuffer(keyData);

            // Import the key
            const key = await window.crypto.subtle.importKey(
                'raw',
                keyBuffer,
                {
                    name: config.defaultAlgorithm,
                    length: config.keySize
                },
                true, // extractable
                ['encrypt', 'decrypt']
            );

            return key;
        } catch (error) {
            console.error('Stashy: Error retrieving encryption key:', error);
            return null;
        }
    }

    /**
     * Stores the encryption key
     * @param {CryptoKey} key - The key to store
     * @returns {Promise<void>} A promise that resolves when the key is stored
     */
    async function storeKey(key) {
        try {
            // Export the key
            const keyBuffer = await window.crypto.subtle.exportKey('raw', key);

            // Convert to base64
            const keyBase64 = arrayBufferToBase64(keyBuffer);

            // Store in local storage
            localStorage.setItem('Stashy_encryption_key', keyBase64);
        } catch (error) {
            console.error('Stashy: Error storing encryption key:', error);
            throw error;
        }
    }

    /**
     * Encrypts data
     * @param {any} data - The data to encrypt
     * @param {Object} [options] - Encryption options
     * @returns {Promise<Object>} A promise that resolves with the encrypted data
     */
    async function encrypt(data, options = {}) {
        // If not initialized, queue the operation
        if (!isInitialized) {
            return new Promise((resolve, reject) => {
                pendingOperations.push({
                    type: 'encrypt',
                    data,
                    options,
                    resolve,
                    reject
                });
            });
        }

        try {
            // Convert data to string if it's not already
            const dataString = typeof data === 'string' ? data : JSON.stringify(data);
            
            // Convert to ArrayBuffer
            const encoder = new TextEncoder();
            const dataBuffer = encoder.encode(dataString);
            
            // Generate initialization vector
            const iv = window.crypto.getRandomValues(new Uint8Array(config.ivSize));
            
            // Encrypt the data
            const encryptedBuffer = await window.crypto.subtle.encrypt(
                {
                    name: config.defaultAlgorithm,
                    iv: iv
                },
                encryptionKey,
                dataBuffer
            );
            
            // Convert to base64 for storage
            const encryptedBase64 = arrayBufferToBase64(encryptedBuffer);
            const ivBase64 = arrayBufferToBase64(iv);
            
            // Return encrypted data object
            return {
                encrypted: true,
                data: encryptedBase64,
                iv: ivBase64,
                algorithm: config.defaultAlgorithm,
                timestamp: Date.now()
            };
        } catch (error) {
            console.error('Stashy: Error encrypting data:', error);
            throw error;
        }
    }

    /**
     * Decrypts data
     * @param {Object} encryptedData - The encrypted data object
     * @param {Object} [options] - Decryption options
     * @returns {Promise<any>} A promise that resolves with the decrypted data
     */
    async function decrypt(encryptedData, options = {}) {
        // If not initialized, queue the operation
        if (!isInitialized) {
            return new Promise((resolve, reject) => {
                pendingOperations.push({
                    type: 'decrypt',
                    data: encryptedData,
                    options,
                    resolve,
                    reject
                });
            });
        }

        try {
            // Check if data is actually encrypted
            if (!encryptedData || !encryptedData.encrypted) {
                return encryptedData;
            }
            
            // Get encrypted data and IV
            const encryptedBuffer = base64ToArrayBuffer(encryptedData.data);
            const iv = base64ToArrayBuffer(encryptedData.iv);
            
            // Decrypt the data
            const decryptedBuffer = await window.crypto.subtle.decrypt(
                {
                    name: encryptedData.algorithm || config.defaultAlgorithm,
                    iv: iv
                },
                encryptionKey,
                encryptedBuffer
            );
            
            // Convert to string
            const decoder = new TextDecoder();
            const decryptedString = decoder.decode(decryptedBuffer);
            
            // Parse JSON if possible
            try {
                return JSON.parse(decryptedString);
            } catch (e) {
                // Return as string if not valid JSON
                return decryptedString;
            }
        } catch (error) {
            console.error('Stashy: Error decrypting data:', error);
            throw error;
        }
    }

    /**
     * Converts an ArrayBuffer to a Base64 string
     * @param {ArrayBuffer} buffer - The buffer to convert
     * @returns {string} The Base64 string
     */
    function arrayBufferToBase64(buffer) {
        const bytes = new Uint8Array(buffer);
        let binary = '';
        for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return btoa(binary);
    }

    /**
     * Converts a Base64 string to an ArrayBuffer
     * @param {string} base64 - The Base64 string to convert
     * @returns {ArrayBuffer} The ArrayBuffer
     */
    function base64ToArrayBuffer(base64) {
        const binaryString = atob(base64);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes.buffer;
    }

    // Return the public API
    return {
        // Core encryption operations
        init,
        encrypt,
        decrypt,
        
        // Constants
        ALGORITHMS,
        KEY_TYPES,
        
        // Status information
        isInitialized: () => isInitialized,
        
        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stashy: Data Encryption Module Loaded");
