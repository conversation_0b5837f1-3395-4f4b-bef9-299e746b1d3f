<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stashy Dashboard - Manage Your Notes</title>
    <meta name="description" content="Organize, search, and manage all your Stashy notes and highlights across different webpages in one centralized dashboard.">
    <link rel="stylesheet" href="dashboard.css"> <!-- Link CSS -->
    <link rel="stylesheet" href="ui/google-docs-export.css"> <!-- Google Docs CSS -->
    <script src="lib/favicon-utils.js"></script> <!-- Favicon utilities -->
</head>
<body>
    <!-- Main Wrapper for Flexbox -->
    <div class="dashboard-wrapper" role="main">

        <!-- Notebook Sidebar -->
        <nav class="notebook-sidebar" role="navigation" aria-label="Notebook navigation">
            <header class="sidebar-header">
                <h2>📚 Notebooks</h2>
                <div class="notebook-actions">
                    <button id="add-notebook-btn"
                            class="add-notebook-btn"
                            title="Create a new notebook to organize your notes"
                            aria-label="Add new notebook">
                        <span class="btn-icon">➕</span>
                        <span class="btn-text">New Notebook</span>
                    </button>
                </div>
            </header>
            <ul id="notebook-list" role="list" aria-label="Available notebooks">
                <!-- Default Static Items -->
                <li role="listitem">
                    <button class="notebook-button active"
                            data-notebook-id="ALL"
                            aria-pressed="true"
                            aria-describedby="all-notes-desc">
                        <span class="notebook-icon">📋</span>
                        <span class="notebook-name">All Notes</span>
                    </button>
                    <span id="all-notes-desc" class="sr-only">View all notes from every notebook</span>
                </li>
                <li role="listitem">
                    <button class="notebook-button"
                            data-notebook-id="UNGROUPED"
                            aria-pressed="false"
                            aria-describedby="ungrouped-desc">
                        <span class="notebook-icon">📄</span>
                        <span class="notebook-name">Ungrouped</span>
                    </button>
                    <span id="ungrouped-desc" class="sr-only">View notes not assigned to any notebook</span>
                </li>
                <!-- Dynamic Notebooks will be added here by JS -->
            </ul>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content" role="main">
            <div class="container"> <!-- Keep original container for styling -->
                <header class="dashboard-header">
                    <div class="header-content">
                        <h1>
                            <span class="dashboard-icon">🗂️</span>
                            Stashy Dashboard
                        </h1>
                        <p class="dashboard-description">
                            View, search, organize, and manage all your notes and highlights across different webpages.
                        </p>
                    </div>
                    <div class="header-stats" id="dashboard-stats">
                        <div class="stat-item">
                            <span class="stat-number" id="total-notes-count">0</span>
                            <span class="stat-label">Notes</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="total-highlights-count">0</span>
                            <span class="stat-label">Highlights</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="total-notebooks-count">0</span>
                            <span class="stat-label">Notebooks</span>
                        </div>
                    </div>
                </header>

                <!-- Tab Navigation -->
                <div class="dashboard-tabs">
                    <button id="notes-tab-btn" class="tab-btn active" data-tab="notes">Notes</button>
                    <button id="highlights-tab-btn" class="tab-btn" data-tab="highlights">Highlights</button>
                </div>

                <!-- Notes Tab Content -->
                <div id="notes-tab-content" class="tab-content active">
                    <div class="controls">
                        <!-- Search/Filter Area -->
                        <div class="search-filter-area">
                            <input type="text" id="dashboard-search-query" placeholder="Search notes in view (content, title, tags, URL)...">
                            <!-- Filter Checkboxes -->
                            <label class="filter-label">
                                <input type="checkbox" id="dashboard-filter-reminder"> With Reminder
                            </label>
                            <label class="filter-label">
                                <input type="checkbox" id="dashboard-filter-global"> Important Notes
                            </label>
                        </div>
                        <!-- View & Sort Area (Combined) -->
                        <div class="sort-area">
                            <div class="filter-group-inline">
                                <label for="dashboard-view-option">View options:</label>
                                <select id="dashboard-view-option" class="filter-select">
                                    <option value="all">All Notes</option>
                                    <option value="recent">Recent (Last 7 Days)</option>
                                    <option value="current">Current Page Only</option>
                                </select>
                            </div>
                            <div class="filter-group-inline">
                                <label for="dashboard-sort-by">Sort by:</label>
                                <select id="dashboard-sort-by" class="filter-select">
                                    <option value="lastSaved_desc">Newest First</option>
                                    <option value="lastSaved_asc">Oldest First</option>
                                    <option value="url_asc">URL (A-Z)</option>
                                    <option value="url_desc">URL (Z-A)</option>
                                </select>
                            </div>
                            <!-- Side-by-Side View Toggle -->
                            <div class="filter-group-inline comparison-view-toggle">
                                <button id="side-by-side-view-btn" class="secondary compact" title="Toggle side-by-side comparison view">
                                    <span class="btn-icon">📊</span> Side-by-Side View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Highlights Tab Content -->
                <div id="highlights-tab-content" class="tab-content">
                    <div class="controls">
                        <!-- Highlights Search/Filter Area -->
                        <div class="search-filter-area">
                            <input type="text" id="highlights-search-query" placeholder="Search highlights by text, notes, or URL...">
                            <div class="highlight-filters">
                                <div class="filter-dropdown">
                                    <label for="highlight-color-filter">Filter by color:</label>
                                    <select id="highlight-color-filter" class="filter-select">
                                        <option value="all">🎨 All Colors</option>
                                        <option value="yellow">🟡 Yellow</option>
                                        <option value="pink">🌸 Pink</option>
                                        <option value="blue">🔵 Blue</option>
                                        <option value="green">🟢 Green</option>
                                        <option value="purple">🟣 Purple</option>
                                    </select>
                                </div>

                                <div class="filter-dropdown">
                                    <label for="highlight-style-filter">Filter by style:</label>
                                    <select id="highlight-style-filter" class="filter-select">
                                        <option value="all">✨ All Styles</option>
                                        <option value="color">🖌️ Color</option>
                                        <option value="underline">📝 Underline</option>
                                        <option value="wavy">〰️ Wavy</option>
                                        <option value="border-thick">🔲 Border</option>
                                        <option value="strikethrough">➜ Strikethrough</option>
                                    </select>
                                </div>

                                <div class="filter-dropdown">
                                    <label for="highlight-notes-filter">Filter by notes:</label>
                                    <select id="highlight-notes-filter" class="filter-select">
                                        <option value="all">📄 All Highlights</option>
                                        <option value="with-notes">📝 With Notes</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <!-- View & Sort Area (Combined) -->
                        <div class="sort-area">
                            <div class="filter-group-inline">
                                <label for="highlights-view-option">View options:</label>
                                <select id="highlights-view-option" class="filter-select">
                                    <option value="all">All Highlights</option>
                                    <option value="recent">Recent (Last 7 Days)</option>
                                    <option value="current">Current Page Only</option>
                                </select>
                            </div>
                            <div class="filter-group-inline">
                                <label for="highlights-sort-by">Sort by:</label>
                                <select id="highlights-sort-by" class="filter-select">
                                    <option value="date-desc">Newest First</option>
                                    <option value="date-asc">Oldest First</option>
                                    <option value="url-asc">URL (A-Z)</option>
                                    <option value="text-asc">Text (A-Z)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shared Bulk Actions Container -->
                <div class="bulk-actions-container">
                    <!-- Bulk Actions Bar (Notes) -->
                    <div class="bulk-actions" id="bulk-actions-bar"> <!-- Style manages visibility -->
                         <span id="selection-count">0 notes selected</span>
                         <select id="bulk-export-format-select" title="Select export format">
                             <option value="txt">Text (.txt)</option>
                             <option value="md">Markdown (.md)</option>
                             <option value="html">HTML (.html)</option>
                             <option value="pdf">PDF (.pdf)</option>
                         </select>
                         <button id="bulk-export-selected" class="secondary">Export Selected</button>
                         <div class="dropdown" id="bulk-move-dropdown">
                            <button id="bulk-move-btn" class="secondary compact">Move To</button>
                            <div class="dropdown-content" id="bulk-move-options">
                                <!-- Options populated by JS -->
                            </div>
                         </div>
                         <button id="bulk-delete-selected" class="danger">Delete Selected</button>
                    </div>

                    <!-- Bulk Actions Bar (Highlights) -->
                    <div class="bulk-actions" id="highlights-bulk-actions-bar">
                         <span id="highlights-selection-count">0 highlights selected</span>
                         <select id="highlights-export-format-select" title="Select export format" class="export-format-select">
                             <option value="pdf">PDF Document (.pdf)</option>
                             <option value="md">Markdown (.md)</option>
                             <option value="html">HTML (.html)</option>
                             <option value="txt">Text (.txt)</option>
                         </select>
                         <button id="bulk-export-highlights" class="secondary">Export Selected</button>
                         <div class="dropdown" id="highlights-move-dropdown">
                            <button id="highlights-move-btn" class="secondary compact">Move To</button>
                            <div class="dropdown-content" id="highlights-move-options">
                                <!-- Options populated by JS -->
                            </div>
                         </div>
                         <button id="bulk-delete-highlights" class="danger">Delete Selected</button>
                    </div>
                </div>

                <!-- Notes Results Area with Drop Overlay -->
                <div class="notes-area-wrapper" id="notes-area">
                    <!-- Select All Checkbox Header -->
                    <div class="select-all-container">
                        <label class="select-all-label">
                            <input type="checkbox" id="select-all-checkbox" title="Select all notes on this page">
                            <span>Select All on This Page</span>
                        </label>
                        <span id="notes-count-display">Showing 0 of 0 notes</span>
                    </div>

                    <div id="dashboard-results">
                        <p class="loading-message">Loading notes...</p>
                    </div>
                    <div id="drop-overlay" class="drop-overlay">Drop here to move to notebook</div>

                    <!-- Pagination Controls -->
                    <div class="pagination-controls">
                        <button id="prev-page-btn" class="pagination-btn" disabled>&laquo; Previous</button>
                        <span id="page-indicator">Page 1 of 1</span>
                        <button id="next-page-btn" class="pagination-btn" disabled>Next &raquo;</button>
                        <select id="page-size-select" title="Notes per page">
                            <option value="15">15 per page</option>
                            <option value="30">30 per page</option>
                            <option value="50">50 per page</option>
                            <option value="100">100 per page</option>
                        </select>
                    </div>
                </div>

                <!-- Highlights Results Area -->
                <div class="highlights-area-wrapper" id="highlights-area">
                    <!-- Select All Checkbox Header -->
                    <div class="select-all-container">
                        <label class="select-all-label">
                            <input type="checkbox" id="select-all-highlights-checkbox" title="Select all highlights on this page">
                            <span>Select All on This Page</span>
                        </label>
                        <span id="highlights-count-display">Showing 0 of 0 highlights</span>
                    </div>

                    <div id="highlights-results">
                        <p class="loading-message">Loading highlights...</p>
                    </div>

                    <!-- Pagination Controls -->
                    <div class="pagination-controls">
                        <button id="highlights-prev-page-btn" class="pagination-btn" disabled>&laquo; Previous</button>
                        <span id="highlights-page-indicator">Page 1 of 1</span>
                        <button id="highlights-next-page-btn" class="pagination-btn" disabled>Next &raquo;</button>
                        <select id="highlights-page-size-select" title="Highlights per page">
                            <option value="15">15 per page</option>
                            <option value="30">30 per page</option>
                            <option value="50">50 per page</option>
                            <option value="100">100 per page</option>
                        </select>
                    </div>
                </div>


                <footer>
                    <div id="dashboard-status-message" role="status" aria-live="polite"></div>
                </footer>
            </div> <!-- End .container -->
        </div> <!-- End .main-content -->

    </div> <!-- End .dashboard-wrapper -->

    <!-- Modal Structure for Notebook Rename/Add -->
     <div id="rename-modal" class="modal" style="display:none;">
         <div class="modal-content">
             <span class="close-button" id="modal-close-btn">×</span>
             <h4 id="modal-title">Rename Notebook</h4>
             <input type="text" id="rename-notebook-input" placeholder="New notebook name">
             <input type="hidden" id="rename-notebook-id">
             <button id="rename-notebook-save">Save</button>
         </div>
    </div>

    <!-- Modal Structure for Viewing Note/Highlight Content -->
    <div id="content-view-modal" class="modal" style="display:none;" aria-modal="true" role="dialog">
        <div class="modal-content content-view-modal-content">
            <!-- Header Section -->
            <div class="content-view-header">
                <div class="content-view-type-indicator">
                    <span id="content-view-type-icon">📝</span>
                </div>
                <h4 id="content-view-title">View Content</h4>
                <span class="close-button" id="content-view-close-btn" aria-label="Close">×</span>
            </div>

            <!-- Metadata Section -->
            <div class="content-view-metadata">
                <div id="content-view-url" class="content-view-url"></div>
                <div id="content-view-details" class="content-view-details"></div>
            </div>

            <!-- Content Section -->
            <div class="content-view-body-container">
                <div class="content-view-body-toolbar">
                    <div class="content-view-zoom-controls">
                        <button id="content-view-zoom-out" class="icon-button" title="Zoom out">−</button>
                        <span id="content-view-zoom-level">100%</span>
                        <button id="content-view-zoom-in" class="icon-button" title="Zoom in">+</button>
                    </div>
                    <button id="content-view-copy-btn" class="icon-button" title="Copy content">📋</button>
                    <button id="content-view-edit-btn" class="icon-button" title="Edit note">✏️</button>
                </div>
                <div id="content-view-body" class="content-view-body"></div>
            </div>

            <!-- Actions Section -->
            <div class="content-view-actions">
                <div class="content-view-navigation">
                    <button id="content-view-prev" class="nav-button" title="Previous item">
                        <span>◀ Previous</span>
                    </button>
                    <button id="content-view-next" class="nav-button" title="Next item">
                        <span>Next ▶</span>
                    </button>
                </div>
                <div class="content-view-main-actions">
                    <button id="content-view-open-page" class="secondary">Open in Page</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Local Security Libraries -->
    <script src="lib/pako.min.js"></script>
    <script src="lib/lz-string-local.js"></script>
    <script src="lib/url-utils.js"></script>
    <script src="lib/security-audit.js"></script>

    <!-- PDF Libraries -->
    <script src="lib/jspdf.umd.min.js"></script>

    <!-- Performance Optimization Libraries -->
    <script src="lib/virtual-data-loader.js"></script>
    <script src="lib/dashboard-performance-optimizer.js"></script>

    <!-- Google Docs Integration -->
    <script src="lib/google-docs-integration.js"></script>
    <script src="ui/google-docs-export.js"></script>
    <script src="ui/dashboard-gdocs-integration.js"></script>

    <script src="dashboard.js"></script>
</body>
</html>