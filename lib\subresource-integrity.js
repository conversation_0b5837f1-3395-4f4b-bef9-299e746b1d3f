// --- START OF FILE subresource-integrity.js ---

/**
 * Subresource Integrity (SRI) Implementation for Stashy
 * 
 * This module provides functions to validate the integrity of external resources
 * loaded by the extension, ensuring they haven't been tampered with.
 */

// Create a namespace to avoid global pollution
window.StashySRI = (function() {
    
    /**
     * Map of resource URLs to their expected integrity hashes
     * Format: { url: { algorithm: 'sha384', hash: 'base64-encoded-hash' } }
     */
    const INTEGRITY_MAP = {
        // KaTeX resources
        'katex.min.js': {
            algorithm: 'sha384',
            hash: 'OMQqOhpIhGDjkKgAkzqT1LW5VEhQYwgY5Qij7uJqGsLyaG+xlZXUW/9XvGRQ=='
        },
        'katex.min.css': {
            algorithm: 'sha384',
            hash: 'YRqJuwPaj1C8aQHN6u1+J+fCwTuB4G0QlYv6jgHQ9GGg+U8tKN4F+CVvfRQ=='
        }
        // Add more resources as needed
    };
    
    /**
     * Validates the integrity of a script or stylesheet
     * @param {string} url - The URL of the resource
     * @param {string} content - The content of the resource
     * @returns {boolean} Whether the integrity check passed
     */
    async function validateIntegrity(url, content) {
        // Extract the filename from the URL
        const filename = url.split('/').pop();
        
        // Check if we have integrity data for this resource
        const integrityData = INTEGRITY_MAP[filename];
        if (!integrityData) {
            console.warn(`Stashy: No integrity data for ${filename}`);
            return true; // Allow resources without integrity data
        }
        
        try {
            // Convert content to ArrayBuffer
            const encoder = new TextEncoder();
            const data = encoder.encode(content);
            
            // Calculate hash using Web Crypto API
            const hashBuffer = await crypto.subtle.digest(integrityData.algorithm.toUpperCase(), data);
            
            // Convert hash to base64
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            const hashBase64 = btoa(String.fromCharCode.apply(null, hashArray));
            
            // Compare with expected hash
            const isValid = hashBase64 === integrityData.hash;
            
            if (!isValid) {
                console.error(`Stashy: Integrity check failed for ${filename}`);
                console.error(`Expected: ${integrityData.hash}`);
                console.error(`Actual: ${hashBase64}`);
            }
            
            return isValid;
        } catch (e) {
            console.error(`Stashy: Error validating integrity for ${filename}:`, e);
            return false;
        }
    }
    
    /**
     * Adds integrity attributes to script and link elements
     */
    function addIntegrityAttributes() {
        // Add integrity attributes to scripts
        document.querySelectorAll('script[src]').forEach(script => {
            const src = script.getAttribute('src');
            const filename = src.split('/').pop();
            
            if (INTEGRITY_MAP[filename]) {
                const integrity = `${INTEGRITY_MAP[filename].algorithm}-${INTEGRITY_MAP[filename].hash}`;
                script.setAttribute('integrity', integrity);
                script.setAttribute('crossorigin', 'anonymous');
            }
        });
        
        // Add integrity attributes to stylesheets
        document.querySelectorAll('link[rel="stylesheet"]').forEach(link => {
            const href = link.getAttribute('href');
            const filename = href.split('/').pop();
            
            if (INTEGRITY_MAP[filename]) {
                const integrity = `${INTEGRITY_MAP[filename].algorithm}-${INTEGRITY_MAP[filename].hash}`;
                link.setAttribute('integrity', integrity);
                link.setAttribute('crossorigin', 'anonymous');
            }
        });
    }
    
    /**
     * Creates a script element with integrity checks
     * @param {string} src - The source URL
     * @param {Function} callback - Callback function when loaded
     * @returns {HTMLScriptElement} The created script element
     */
    function createSecureScript(src, callback) {
        const script = document.createElement('script');
        script.src = src;
        
        const filename = src.split('/').pop();
        if (INTEGRITY_MAP[filename]) {
            const integrity = `${INTEGRITY_MAP[filename].algorithm}-${INTEGRITY_MAP[filename].hash}`;
            script.integrity = integrity;
            script.crossOrigin = 'anonymous';
        }
        
        if (callback) {
            script.onload = callback;
        }
        
        return script;
    }
    
    /**
     * Creates a stylesheet link with integrity checks
     * @param {string} href - The href URL
     * @param {Function} callback - Callback function when loaded
     * @returns {HTMLLinkElement} The created link element
     */
    function createSecureStylesheet(href, callback) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        
        const filename = href.split('/').pop();
        if (INTEGRITY_MAP[filename]) {
            const integrity = `${INTEGRITY_MAP[filename].algorithm}-${INTEGRITY_MAP[filename].hash}`;
            link.integrity = integrity;
            link.crossOrigin = 'anonymous';
        }
        
        if (callback) {
            link.onload = callback;
        }
        
        return link;
    }
    
    /**
     * Intercepts fetch requests to validate resource integrity
     */
    function setupFetchInterceptor() {
        // Store the original fetch function
        const originalFetch = window.fetch;
        
        // Override fetch with our interceptor
        window.fetch = async function(resource, options) {
            // Call the original fetch
            const response = await originalFetch.apply(this, arguments);
            
            // Clone the response to avoid consuming it
            const clonedResponse = response.clone();
            
            // Check if this is a resource we want to validate
            const url = typeof resource === 'string' ? resource : resource.url;
            const filename = url.split('/').pop();
            
            if (INTEGRITY_MAP[filename]) {
                try {
                    // Get the response text
                    const text = await clonedResponse.text();
                    
                    // Validate integrity
                    const isValid = await validateIntegrity(url, text);
                    
                    if (!isValid) {
                        console.error(`Stashy: Integrity check failed for ${url}`);
                        throw new Error(`Integrity check failed for ${url}`);
                    }
                } catch (e) {
                    console.error('Stashy: Error in fetch interceptor:', e);
                    // Allow the request to continue but log the error
                }
            }
            
            return response;
        };
    }
    
    /**
     * Generates integrity hashes for resources
     * @param {string} content - The content to hash
     * @param {string} algorithm - The hashing algorithm (sha256, sha384, sha512)
     * @returns {Promise<string>} The integrity hash
     */
    async function generateIntegrityHash(content, algorithm = 'sha384') {
        try {
            // Convert content to ArrayBuffer
            const encoder = new TextEncoder();
            const data = encoder.encode(content);
            
            // Calculate hash using Web Crypto API
            const hashBuffer = await crypto.subtle.digest(algorithm.toUpperCase(), data);
            
            // Convert hash to base64
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            const hashBase64 = btoa(String.fromCharCode.apply(null, hashArray));
            
            return `${algorithm}-${hashBase64}`;
        } catch (e) {
            console.error('Stashy: Error generating integrity hash:', e);
            return null;
        }
    }
    
    // Return the public API
    return {
        validateIntegrity,
        addIntegrityAttributes,
        createSecureScript,
        createSecureStylesheet,
        setupFetchInterceptor,
        generateIntegrityHash
    };
})();

console.log("Stashy: Subresource Integrity Module Loaded");
// --- END OF FILE subresource-integrity.js ---
