<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stashy - Permissions Explanation</title>
    <link rel="stylesheet" href="permissions-explanation.css">
</head>
<body>
    <h1>🔑 Permissions Explanation</h1>

    <div class="privacy-highlight">
        <h3>Privacy-First Permission Model</h3>
        <p>Stashy uses a minimal permission approach, requesting only what's necessary for core functionality. We prioritize your privacy and security with every permission request.</p>
    </div>

    <p>This comprehensive guide explains every permission Stash<PERSON> requests, why it's needed, and how it protects your privacy. We believe in complete transparency about how our extension accesses your browser and data.</p>

    <h2>🛡️ Core Privacy Permissions</h2>

    <div class="permission">
        <h3><span class="permission-icon">📄</span> activeTab <span class="badge required">Required</span></h3>
        <p>This is our primary privacy protection. The activeTab permission only grants access to the current tab when you explicitly click the extension icon.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Essential for creating notes and highlights on the webpage you're currently viewing. The extension only activates when you choose to use it.</p>
            <p><strong>Privacy benefit:</strong> Unlike broad "tabs" permission, activeTab ensures we never access tabs you haven't explicitly activated the extension on. You maintain complete control over when and where Stashy operates.</p>
            <p><strong>Alternative avoided:</strong> We could request "tabs" permission for broader access, but we choose the privacy-friendly activeTab approach instead.</p>
        </div>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">💾</span> storage & unlimitedStorage <span class="badge required">Required</span></h3>
        <p>These permissions enable Stashy to save your notes, highlights, settings, and preferences securely in your browser's local storage.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Without storage permissions, all your notes and highlights would disappear when you close your browser. This ensures your data persists across browser sessions.</p>
            <p><strong>Security benefit:</strong> Data is stored locally in your browser's secure extension storage, isolated from websites and other extensions.</p>
            <p><strong>unlimitedStorage:</strong> Allows you to create large note collections without browser-imposed storage limits.</p>
        </div>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">📝</span> scripting <span class="badge required">Required</span></h3>
        <p>This permission allows Stashy to inject content scripts into webpages to enable note-taking and highlighting functionality.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Required to add the note interface, highlighting tools, and interactive elements to webpages. Only activates when you use the extension.</p>
            <p><strong>Security measure:</strong> Scripts are only injected when you explicitly activate the extension, never automatically on page load.</p>
            <p><strong>Content Security:</strong> All injected scripts follow strict Content Security Policy (CSP) guidelines.</p>
        </div>
    </div>

    <h2>⚙️ Functional Permissions</h2>

    <div class="permission">
        <h3><span class="permission-icon">⏰</span> alarms <span class="badge required">Required</span></h3>
        <p>Enables Stashy to schedule reminders, periodic sync operations, and background maintenance tasks.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Powers the reminder system for your notes, schedules automatic sync operations, and handles background cleanup tasks.</p>
            <p><strong>Privacy note:</strong> Alarms only trigger internal extension functions, never external network requests or data collection.</p>
        </div>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">🔔</span> notifications <span class="badge required">Required</span></h3>
        <p>Allows Stashy to display desktop notifications for reminders, sync status, and important updates.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Shows reminder alerts for your notes, notifies you of sync completion, and provides feedback for important operations.</p>
            <p><strong>User control:</strong> All notifications can be disabled in browser settings or extension preferences.</p>
        </div>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">🐛</span> debugger <span class="badge required">Required</span></h3>
        <p>Used for advanced webpage interaction and content extraction in complex web applications.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Enables advanced features like video timestamp extraction, dynamic content analysis, and interaction with complex web applications.</p>
            <p><strong>Limited use:</strong> Only activated for specific advanced features, never for general browsing or data collection.</p>
            <p><strong>Security:</strong> Used exclusively for enhancing note-taking capabilities, not for monitoring or tracking.</p>
        </div>
    </div>

    <h2>📁 Export & Download Permissions</h2>

    <div class="permission">
        <h3><span class="permission-icon">⬇️</span> downloads <span class="badge optional-badge">Feature-Based</span></h3>
        <p>Enables exporting your notes, highlights, and screenshots to your computer in various formats.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Required for exporting data in TXT, MD, HTML, and PDF formats. Also used for downloading screenshots and backup files.</p>
            <p><strong>Data ownership:</strong> Ensures you can always export and own your data, preventing vendor lock-in.</p>
            <p><strong>Privacy benefit:</strong> Downloads go directly to your computer, never through external servers.</p>
        </div>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">🖼️</span> offscreen <span class="badge optional-badge">Feature-Based</span></h3>
        <p>Creates invisible background documents for secure processing tasks like PDF generation and encryption.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Enables PDF export generation, image processing for screenshots, and secure API key encryption operations.</p>
            <p><strong>Security benefit:</strong> Provides a secure environment for sensitive operations, isolated from visible web content.</p>
            <p><strong>Performance:</strong> Handles resource-intensive tasks without affecting your browsing experience.</p>
        </div>
    </div>

    <h2>🌐 Optional Cloud Integration</h2>

    <div class="permission">
        <h3><span class="permission-icon">🔑</span> identity <span class="badge optional-badge">Optional</span></h3>
        <p>Enables secure OAuth2 authentication with Google services for optional cloud integration features.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Required for Google Drive sync, Google Docs export, and Google Calendar reminder integration.</p>
            <p><strong>Security standard:</strong> Uses industry-standard OAuth2 authentication - we never see your Google password.</p>
            <p><strong>User control:</strong> Completely optional - Stashy works fully without any Google integration.</p>
            <p><strong>Revocable:</strong> You can disconnect Google services anytime through your Google account settings.</p>
        </div>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">📋</span> contextMenus <span class="badge optional-badge">Feature-Based</span></h3>
        <p>Adds convenient right-click menu options for quick note creation and text highlighting.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Enables right-click shortcuts for creating notes from selected text, quick highlighting, and context-aware actions.</p>
            <p><strong>Convenience feature:</strong> Provides faster access to common functions without opening the main interface.</p>
            <p><strong>Privacy-safe:</strong> Context menus only appear when you right-click, never automatically.</p>
        </div>
    </div>

    <h2>🌍 Host Permissions (Google Services)</h2>

    <div class="permission">
        <h3><span class="permission-icon">🌐</span> *://*.googleapis.com/* <span class="badge optional-badge">Optional</span></h3>
        <p>Allows direct communication with Google APIs for Drive, Docs, and Calendar integration.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Required for Google Drive file operations, Google Docs export, and Google Calendar event creation.</p>
            <p><strong>Direct communication:</strong> Your browser communicates directly with Google - we don't intercept or store your data.</p>
            <p><strong>Limited scope:</strong> Only accesses specific Google services you explicitly authorize.</p>
        </div>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">🔐</span> *://*.google.com/oauth2/* <span class="badge optional-badge">Optional</span></h3>
        <p>Enables the secure OAuth2 authentication flow with Google services.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Required for the secure authentication process when connecting to Google services.</p>
            <p><strong>Security standard:</strong> Uses Google's official OAuth2 endpoints for maximum security.</p>
            <p><strong>No data storage:</strong> We don't store your Google credentials - authentication tokens are managed securely by your browser.</p>
        </div>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">🤖</span> *://*.generativelanguage.googleapis.com/* <span class="badge optional-badge">Optional</span></h3>
        <p>Enables direct communication with Google AI (Gemini) services for AI features.</p>
        <div class="permission-reason">
            <p><strong>Why needed:</strong> Required for Google AI integration when you choose to use Google's AI services with your own API key.</p>
            <p><strong>Direct connection:</strong> Your browser connects directly to Google AI - we don't proxy or store your AI requests.</p>
            <p><strong>User-controlled:</strong> Only used when you provide your own Google AI API key and choose to use AI features.</p>
        </div>
    </div>

    <h2>🚫 Permissions We DON'T Request</h2>

    <div class="privacy-highlight">
        <h3>What We Deliberately Avoid</h3>
        <p>These are permissions we could request but choose not to, demonstrating our commitment to privacy:</p>
    </div>

    <ul>
        <li><strong>tabs:</strong> We use privacy-friendly activeTab instead of broad tab access</li>
        <li><strong>history:</strong> We never access your browsing history</li>
        <li><strong>bookmarks:</strong> We don't access or modify your bookmarks</li>
        <li><strong>webNavigation:</strong> We don't track your navigation patterns</li>
        <li><strong>cookies:</strong> We don't access website cookies or session data</li>
        <li><strong>webRequest:</strong> We don't monitor or intercept web requests</li>
        <li><strong>management:</strong> We don't access information about other extensions</li>
        <li><strong>topSites:</strong> We don't access your most visited sites</li>
        <li><strong>geolocation:</strong> We never request location data</li>
        <li><strong>microphone/camera:</strong> We don't access media devices (voice features use browser APIs)</li>
    </ul>

    <h2>🔒 Security & Privacy Measures</h2>

    <div class="privacy-highlight">
        <h3>How We Protect Your Privacy</h3>
        <p>Beyond minimal permissions, we implement additional security measures:</p>
    </div>

    <ul>
        <li><strong>Local-First Storage:</strong> All data stored locally by default, never on our servers</li>
        <li><strong>Encryption:</strong> Sensitive data like API keys encrypted before storage</li>
        <li><strong>Content Security Policy:</strong> Strict CSP prevents unauthorized code execution</li>
        <li><strong>No Analytics:</strong> We don't use Google Analytics or any tracking services</li>
        <li><strong>No Remote Code:</strong> All extension code is bundled and reviewed, no remote loading</li>
        <li><strong>Sandboxed Execution:</strong> Extension runs in Chrome's secure sandbox environment</li>
        <li><strong>HTTPS Only:</strong> All external communications use encrypted HTTPS connections</li>
        <li><strong>Regular Updates:</strong> Prompt security patches and vulnerability fixes</li>
    </ul>

    <h2>📖 Understanding Permission Types</h2>

    <div class="permission">
        <h3><span class="permission-icon">🔴</span> Required Permissions</h3>
        <p>Essential for core functionality - Stashy cannot operate without these permissions.</p>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">🔵</span> Feature-Based Permissions</h3>
        <p>Only used when you activate specific features like exports, screenshots, or context menus.</p>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">🟢</span> Optional Permissions</h3>
        <p>Completely optional for cloud integration features - Stashy works fully without these.</p>
    </div>

    <h2>❓ Frequently Asked Questions</h2>

    <div class="permission">
        <h3><span class="permission-icon">❓</span> Why does Stashy need any permissions at all?</h3>
        <div class="permission-reason">
            <p><strong>Answer:</strong> Browser extensions need explicit permissions to interact with web pages and store data. Without permissions, Stashy couldn't create notes, save highlights, or remember your settings. We request only the minimum necessary for functionality.</p>
        </div>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">❓</span> Can I use Stashy without Google integration?</h3>
        <div class="permission-reason">
            <p><strong>Answer:</strong> Absolutely! Stashy works completely offline with local storage. Google integration is entirely optional for users who want cloud sync and backup features.</p>
        </div>
    </div>

    <div class="permission">
        <h3><span class="permission-icon">❓</span> How can I verify Stashy isn't collecting my data?</h3>
        <div class="permission-reason">
            <p><strong>Answer:</strong> You can review our open-source code, check browser developer tools for network requests, and see our privacy policy. We're committed to transparency and welcome scrutiny.</p>
        </div>
    </div>

    <p>For more detailed information about data handling and privacy practices, please see our comprehensive <a href="privacy-policy.html">Privacy Policy</a> and <a href="data-deletion.html">Data Deletion Guide</a>.</p>

    <p class="last-updated">
        Last updated: January 2025 | Version: 1.0
        <br>This permissions explanation applies to Stashy version 1.0 and later
    </p>
</body>
</html>
