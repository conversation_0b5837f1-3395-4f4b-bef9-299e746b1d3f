/* Settings Page Styles */
:root {
    /* Light Mode Colors */
    --primary-color: #4caf50;
    --primary-hover: #45a049;
    --secondary-color: #f0f0f0;
    --secondary-hover: #e0e0e0;
    --text-color: #333333;
    --text-light: #666666;
    --border-color: #dddddd;
    --background-color: #ffffff;
    --card-background: #f9f9f9;
    --input-background: #ffffff;
    --shadow-color: rgba(0, 0, 0, 0.1);


}

/* Base Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}



.settings-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

header {
    margin-bottom: 30px;
    text-align: center;
}

h1 {
    font-size: 28px;
    margin-bottom: 5px;
}

h2 {
    font-size: 20px;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-color);
}



p {
    margin: 0 0 15px;
}

.section-description {
    color: var(--text-light);
    font-size: 14px;
    margin-top: -10px;
    margin-bottom: 20px;
}



.settings-section {
    background-color: var(--card-background);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px var(--shadow-color);
}



.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}



.setting-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.setting-label {
    flex: 1;
}

.setting-description {
    font-size: 14px;
    color: var(--text-light);
    margin: 5px 0 0;
}



.setting-control {
    display: flex;
    align-items: center;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--primary-color);
}



input:focus + .toggle-slider {
    box-shadow: 0 0 1px var(--primary-color);
}



input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Input Fields */
input[type="number"] {
    width: 80px;
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--input-background);
    color: var(--text-color);
    font-size: 14px;
}



input[type="number"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}



.unit {
    margin-left: 5px;
    color: var(--text-light);
}



/* Buttons */
footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 30px;
}

button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease, transform 0.1s ease;
}

button:active {
    transform: translateY(1px);
}

.primary-button {
    background-color: var(--primary-color);
    color: white;
}

.primary-button:hover {
    background-color: var(--primary-hover);
}



.secondary-button {
    background-color: var(--secondary-color);
    color: var(--text-color);
}

.secondary-button:hover {
    background-color: var(--secondary-hover);
}


