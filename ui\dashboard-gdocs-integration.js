/**
 * Dashboard Google Docs Integration for Stashy
 *
 * This module integrates Google Docs export functionality with the Stashy dashboard.
 */

// Only run on dashboard pages - check if this looks like a dashboard
const isDashboardPage = () => {
    // Check URL for dashboard indicators
    const url = window.location.href.toLowerCase();
    if (url.includes('dashboard') || url.includes('Stashy')) {
        return true;
    }

    // Check for dashboard-specific elements
    const dashboardIndicators = [
        '.dashboard-wrapper',
        '.Stashy-dashboard-container',
        '.dashboard-container',
        '#dashboard',
        '[class*="dashboard"]'
    ];

    return dashboardIndicators.some(selector => document.querySelector(selector));
};

// Exit early if not on a dashboard page
if (!isDashboardPage()) {
    // Not on dashboard page, skipping
    // Create empty namespace to prevent errors
    window.StashyDashboardGDocsIntegration = {
        init: () => Promise.resolve(false),
        isInitialized: () => false,
        getSelectedItems: () => [],
        updateExportButtonVisibility: () => {},
        testSelection: () => []
    };
} else {
    console.log("Stashy: Dashboard Google Docs Integration loaded on dashboard page");

    // Create a namespace to avoid global pollution
    window.StashyDashboardGDocsIntegration = (function() {
        // Private variables
        let isInitialized = false;
        let initInterval = null;

    /**
     * Initializes the dashboard Google Docs integration
     * @returns {Promise<boolean>} A promise that resolves to true if initialization was successful
     */
    async function init() {
        try {
            // Check if already initialized
            if (isInitialized) {
                return true;
            }

            console.log("Stashy: Initializing Dashboard Google Docs Integration");

            // Start polling for dashboard readiness
            startDashboardPolling();

            // Add direct event listeners for checkbox changes
            addDirectEventListeners();

            isInitialized = true;
            return true;
        } catch (error) {
            console.error('Error initializing dashboard Google Docs integration:', error);
            return false;
        }
    }

    /**
     * Starts polling for dashboard readiness
     */
    function startDashboardPolling() {
        if (initInterval) {
            clearInterval(initInterval);
        }

        initInterval = setInterval(() => {
            // Check if dashboard container exists and dashboard API is available
            const dashboardContainer = document.querySelector('.dashboard-wrapper, .Stashy-dashboard-container');
            const dashboardApiReady = window.StashyDashboard && typeof window.StashyDashboard.getSelectedItems === 'function';

            if (dashboardContainer && dashboardApiReady) {
                console.log('Stashy: Dashboard container and API ready, adding Google Docs buttons');
                addExportButtonToDashboard();

                // Once we've successfully added the button, stop polling
                if (document.getElementById('Stashy-dashboard-gdocs-btn-notes') ||
                    document.getElementById('Stashy-dashboard-gdocs-btn-highlights') ||
                    document.getElementById('Stashy-dashboard-gdocs-btn')) {
                    clearInterval(initInterval);
                    console.log("Stashy: Google Docs export buttons added to dashboard");
                }
            } else {
                console.log('Stashy: Waiting for dashboard...', {
                    containerReady: !!dashboardContainer,
                    apiReady: dashboardApiReady
                });
            }
        }, 1000);

        // Stop polling after 30 seconds to prevent indefinite polling
        setTimeout(() => {
            if (initInterval) {
                clearInterval(initInterval);
                console.log("Stashy: Stopped dashboard polling after timeout");
            }
        }, 30000);
    }

    /**
     * Adds direct event listeners for checkbox changes
     */
    function addDirectEventListeners() {
        // Listen for checkbox changes directly (updated selectors for actual dashboard structure)
        document.addEventListener('change', function(event) {
            // Check if the changed element is a checkbox in the dashboard
            const target = event.target;
            if (target.type === 'checkbox' && (
                target.closest('.note-item, .highlight-item, .Stashy-dashboard-item') ||
                target.id === 'select-all-checkbox' ||
                target.id === 'select-all-highlights-checkbox' ||
                target.id === 'Stashy-select-all' ||
                target.classList.contains('Stashy-item-checkbox') ||
                target.classList.contains('note-select-checkbox') ||
                target.classList.contains('highlight-checkbox')
            )) {
                console.log("Stashy: Dashboard checkbox changed");
                setTimeout(updateExportButtonVisibility, 100);
            }
        });

        // Also check periodically in case the dashboard uses a different selection mechanism
        setInterval(updateExportButtonVisibility, 2000);

        // Add click handler for the export button
        document.addEventListener('click', function(event) {
            if (event.target.id === 'Stashy-dashboard-gdocs-btn' || event.target.closest('#Stashy-dashboard-gdocs-btn')) {
                handleExportButtonClick();
            } else if (event.target.id && event.target.id.startsWith('Stashy-dashboard-gdocs-btn-')) {
                const type = event.target.id.replace('Stashy-dashboard-gdocs-btn-', '');
                handleExportButtonClick(type);
            }
        });
    }

    /**
     * Updates the visibility of the export button based on selected items
     */
    function updateExportButtonVisibility() {
        // Handle the original export button
        const exportButton = document.getElementById('Stashy-dashboard-gdocs-btn');
        if (exportButton) {
            const selectedItems = getSelectedItems();
            if (selectedItems.length > 0) {
                console.log("Stashy: Found selected items, showing export button");
                exportButton.style.display = 'inline-block';
            } else {
                exportButton.style.display = 'none';
            }
        }

        // Handle the new bulk action buttons - they should always be visible when the bulk actions are visible
        // The bulk actions bars handle their own visibility based on selections
        const notesButton = document.getElementById('Stashy-dashboard-gdocs-btn-notes');
        const highlightsButton = document.getElementById('Stashy-dashboard-gdocs-btn-highlights');

        // These buttons should be visible when their parent bulk action bars are visible
        // No need to hide/show them as the bulk action bars already handle this
    }

    /**
     * Gets selected items from the dashboard
     * @returns {Array} An array of selected items
     */
    function getSelectedItems() {
        // Use the dashboard API if available
        if (window.StashyDashboard && typeof window.StashyDashboard.getSelectedItems === 'function') {
            const selectedItems = window.StashyDashboard.getSelectedItems();
            console.log(`Stashy: Got ${selectedItems.length} selected items from dashboard API`, selectedItems);
            return selectedItems;
        }

        console.log('Stashy: Dashboard API not available, using fallback method');
        return [];
    }

    /**
     * Adds the export button to the dashboard
     */
    function addExportButtonToDashboard() {
        // First try to add to existing bulk actions containers
        const bulkActionsNotes = document.getElementById('bulk-actions-bar');
        const bulkActionsHighlights = document.getElementById('highlights-bulk-actions-bar');

        console.log('Stashy: Checking for bulk actions containers:', {
            notesContainer: !!bulkActionsNotes,
            highlightsContainer: !!bulkActionsHighlights
        });

        if (bulkActionsNotes && bulkActionsHighlights) {
            console.log('Stashy: Found both bulk actions containers, adding Google Docs buttons');
            // Add Google Docs button to both bulk action bars
            addGoogleDocsButtonToBulkActions(bulkActionsNotes, 'notes');
            addGoogleDocsButtonToBulkActions(bulkActionsHighlights, 'highlights');
            return;
        } else {
            console.log('Stashy: Bulk actions containers not found, falling back to creating actions container');
        }

        // Fallback: Check for actions container or create one if needed
        let actionsContainer = document.querySelector('.Stashy-dashboard-actions');

        if (!actionsContainer) {
            // Look for alternate container structures
            const dashboardHeader = document.querySelector('.Stashy-dashboard-header, .dashboard-header');
            if (dashboardHeader) {
                actionsContainer = document.createElement('div');
                actionsContainer.className = 'Stashy-dashboard-actions';
                dashboardHeader.appendChild(actionsContainer);
            } else {
                // Create a new actions container and add it to the dashboard
                const dashboardContainer = document.querySelector('.dashboard-wrapper, .Stashy-dashboard-container, .dashboard-container');
                if (dashboardContainer) {
                    actionsContainer = document.createElement('div');
                    actionsContainer.className = 'Stashy-dashboard-actions';
                    actionsContainer.style.padding = '10px';
                    actionsContainer.style.display = 'flex';
                    actionsContainer.style.justifyContent = 'flex-end';
                    actionsContainer.style.borderBottom = '1px solid #eee';
                    dashboardContainer.insertBefore(actionsContainer, dashboardContainer.firstChild);
                } else {
                    console.warn('Stashy: Dashboard container not found, cannot add actions container');
                    return;
                }
            }
        }

        // Check if button already exists
        if (document.getElementById('Stashy-dashboard-gdocs-btn')) {
            return;
        }

        console.log("Stashy: Creating Google Docs export button");

        // Create export button
        const exportButton = document.createElement('button');
        exportButton.id = 'Stashy-dashboard-gdocs-btn';
        exportButton.className = 'Stashy-dashboard-action-btn';
        exportButton.title = 'Export selected items to Google Docs';
        exportButton.innerHTML = '<span class="Stashy-icon">📄</span> Export to Google Docs';

        // Style the button
        exportButton.style.display = 'none'; // Hidden by default, shown when items are selected
        exportButton.style.backgroundColor = '#4285F4'; // Google blue
        exportButton.style.color = 'white';
        exportButton.style.border = 'none';
        exportButton.style.borderRadius = '4px';
        exportButton.style.padding = '8px 12px';
        exportButton.style.margin = '5px';
        exportButton.style.cursor = 'pointer';
        exportButton.style.fontWeight = 'bold';
        exportButton.style.fontSize = '14px';
        exportButton.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        exportButton.style.transition = 'all 0.2s ease';
        exportButton.style.zIndex = '1000'; // Ensure it's above other elements

        // Add hover effect
        exportButton.addEventListener('mouseover', function() {
            this.style.backgroundColor = '#3367D6'; // Darker Google blue
            this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
        });

        exportButton.addEventListener('mouseout', function() {
            this.style.backgroundColor = '#4285F4';
            this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        });

        // Add click handler
        exportButton.addEventListener('click', handleExportButtonClick);

        // Add to dashboard
        actionsContainer.appendChild(exportButton);

        // Check if there are already selected items
        updateExportButtonVisibility();
    }

    /**
     * Adds Google Docs button to a specific bulk actions container
     */
    function addGoogleDocsButtonToBulkActions(container, type) {
        const buttonId = `Stashy-dashboard-gdocs-btn-${type}`;

        // Check if button already exists
        if (document.getElementById(buttonId)) {
            return;
        }

        console.log(`Stashy: Creating Google Docs export button for ${type}`);

        // Create export button
        const exportButton = document.createElement('button');
        exportButton.id = buttonId;
        exportButton.className = 'secondary'; // Match existing button styles
        exportButton.title = `Export selected ${type} to Google Docs`;
        exportButton.innerHTML = '📄 Google Docs';

        // Style the button to match existing buttons and make it more visible
        exportButton.style.marginLeft = '5px';
        exportButton.style.backgroundColor = '#4285F4';
        exportButton.style.color = 'white';
        exportButton.style.border = 'none';

        // Add click handler
        exportButton.addEventListener('click', function() {
            handleExportButtonClick(type);
        });

        // Insert after the existing export button
        const existingExportBtn = container.querySelector('#bulk-export-selected, #bulk-export-highlights');
        if (existingExportBtn) {
            existingExportBtn.parentNode.insertBefore(exportButton, existingExportBtn.nextSibling);
        } else {
            container.appendChild(exportButton);
        }
    }

    /**
     * Handles export button click
     */
    function handleExportButtonClick(type = 'mixed') {
        console.log(`Stashy: Export button clicked for ${type}`);

        // Show export modal if available
        if (window.StashyGoogleDocsExport && typeof window.StashyGoogleDocsExport.showExportModal === 'function') {
            window.StashyGoogleDocsExport.showExportModal();
        } else {
            // If the export UI is not available, create a simple modal
            showSimpleExportModal(type);
        }
    }

    /**
     * Shows a simple export modal as a fallback
     */
    function showSimpleExportModal(type = 'mixed') {
        // Check if modal already exists
        let modal = document.getElementById('Stashy-simple-export-modal');

        if (!modal) {
            // Create modal
            modal = document.createElement('div');
            modal.id = 'Stashy-simple-export-modal';
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            modal.style.display = 'flex';
            modal.style.justifyContent = 'center';
            modal.style.alignItems = 'center';
            modal.style.zIndex = '10000';

            // Create modal content
            const modalContent = document.createElement('div');
            modalContent.style.backgroundColor = 'white';
            modalContent.style.padding = '20px';
            modalContent.style.borderRadius = '8px';
            modalContent.style.maxWidth = '400px';
            modalContent.style.width = '90%';
            modalContent.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';

            const typeText = type === 'notes' ? 'notes' : type === 'highlights' ? 'highlights' : 'items';
            modalContent.innerHTML = `
                <h3 style="margin-top: 0; color: #4285F4;">Export to Google Docs</h3>
                <p>Do you want to export the selected ${typeText} to Google Docs?</p>
                <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
                    <button id="Stashy-simple-export-cancel" style="margin-right: 10px; padding: 8px 16px; background-color: #f1f1f1; border: none; border-radius: 4px; cursor: pointer;">Cancel</button>
                    <button id="Stashy-simple-export-confirm" style="padding: 8px 16px; background-color: #4285F4; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">Export</button>
                </div>
            `;

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Add event listeners
            document.getElementById('Stashy-simple-export-cancel').addEventListener('click', function() {
                modal.style.display = 'none';
            });

            document.getElementById('Stashy-simple-export-confirm').addEventListener('click', function() {
                modal.style.display = 'none';
                alert('This feature is coming soon! The Google Docs export functionality is currently being implemented.');
            });

            // Close modal when clicking outside
            modal.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        } else {
            // Show existing modal
            modal.style.display = 'flex';
        }
    }

        // Return the public API
        return {
            init,
            isInitialized: () => isInitialized,
            getSelectedItems,
            updateExportButtonVisibility,
            // Debug function for testing
            testSelection: function() {
                const items = getSelectedItems();
                console.log('Stashy Debug: Selected items:', items);
                alert(`Selected ${items.length} items. Check console for details.`);
                return items;
            }
        };
    })();

    // Initialize immediately
    console.log("Stashy: Dashboard Google Docs Integration Module Loaded - Starting initialization");
    window.StashyDashboardGDocsIntegration.init();

    // Also initialize when the document is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log("Stashy: DOM Content Loaded - Initializing Google Docs integration");
        window.StashyDashboardGDocsIntegration.init();
    });

    // Also initialize when the window loads
    window.addEventListener('load', function() {
        console.log("Stashy: Window Loaded - Initializing Google Docs integration");
        window.StashyDashboardGDocsIntegration.init();
    });
}

// Dashboard Google Docs Integration Module Loaded
