/**
 * Stashy - Performance Optimizer
 * Provides comprehensive performance optimization utilities
 */

// Make sure we don't redefine functions if the script is injected multiple times
if (typeof window.SB_PERFORMANCE_OPTIMIZER_LOADED === 'undefined') {
    window.SB_PERFORMANCE_OPTIMIZER_LOADED = true;

    console.log("Stashy: Loading Performance Optimizer...");

    // Performance monitoring state
    const performanceState = {
        isMonitoring: false,
        metrics: {
            fps: 120,
            memoryUsage: 0,
            domComplexity: 0,
            imageCount: 0,
            totalImageSize: 0
        },
        thresholds: {
            lowFPS: 30,
            highMemory: 100 * 1024 * 1024, // 100MB
            highComplexity: 50,
            maxImages: 20
        }
    };

    /**
     * Optimized Canvas context creation with willReadFrequently
     * @param {HTMLCanvasElement} canvas - The canvas element
     * @param {Object} options - Context options
     */
    window.createOptimizedCanvasContext = function(canvas, options = {}) {
        const defaultOptions = {
            willReadFrequently: true,
            alpha: false,
            desynchronized: true
        };
        
        const contextOptions = { ...defaultOptions, ...options };
        const ctx = canvas.getContext('2d', contextOptions);
        
        if (ctx) {
            // Apply performance optimizations
            ctx.imageSmoothingEnabled = false;
            ctx.globalCompositeOperation = 'source-over';
            
            console.log("Stashy: Created optimized canvas context with willReadFrequently");
        }
        
        return ctx;
    };

    /**
     * Batch DOM operations for better performance
     * @param {Function[]} operations - Array of DOM operations
     * @param {number} batchSize - Number of operations per batch
     */
    window.batchDOMOperations = function(operations, batchSize = 5) {
        return new Promise((resolve) => {
            let currentBatch = 0;
            const totalBatches = Math.ceil(operations.length / batchSize);
            
            function processBatch() {
                const start = currentBatch * batchSize;
                const end = Math.min(start + batchSize, operations.length);
                
                // Process batch with error handling
                for (let i = start; i < end; i++) {
                    try {
                        operations[i]();
                    } catch (error) {
                        console.warn("Stashy: Error in batched DOM operation:", error);
                    }
                }
                
                currentBatch++;
                
                if (currentBatch < totalBatches) {
                    requestIdleCallback(processBatch, { timeout: 100 });
                } else {
                    resolve();
                }
            }
            
            requestIdleCallback(processBatch, { timeout: 1000 });
        });
    };

    /**
     * Optimize images for better performance
     * @param {HTMLImageElement[]} images - Array of image elements
     */
    window.optimizeImages = function(images) {
        const optimizations = images.map(img => () => {
            // Add performance-enhancing styles
            img.style.willChange = 'transform';
            img.style.backfaceVisibility = 'hidden';
            img.style.perspective = '1000px';
            
            // Use transform3d for hardware acceleration
            if (!img.style.transform.includes('translate3d')) {
                img.style.transform = `${img.style.transform} translate3d(0,0,0)`.trim();
            }
            
            // Add loading optimization
            if (!img.loading) {
                img.loading = 'lazy';
            }
        });
        
        return window.batchDOMOperations(optimizations);
    };

    /**
     * Monitor performance metrics
     */
    window.startPerformanceMonitoring = function() {
        if (performanceState.isMonitoring) return;
        
        performanceState.isMonitoring = true;
        
        // Monitor FPS
        let frameCount = 0;
        let lastTime = performance.now();
        
        function measureFPS() {
            if (!performanceState.isMonitoring) return;
            
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                performanceState.metrics.fps = frameCount;
                frameCount = 0;
                lastTime = currentTime;
                
                // Check if optimization is needed
                if (performanceState.metrics.fps < performanceState.thresholds.lowFPS) {
                    triggerPerformanceOptimization('low-fps');
                }
            }
            
            requestAnimationFrame(measureFPS);
        }
        
        requestAnimationFrame(measureFPS);
        
        // Monitor memory usage (if available)
        if (performance.memory) {
            setInterval(() => {
                if (!performanceState.isMonitoring) return;
                
                performanceState.metrics.memoryUsage = performance.memory.usedJSHeapSize;
                
                if (performanceState.metrics.memoryUsage > performanceState.thresholds.highMemory) {
                    triggerPerformanceOptimization('high-memory');
                }
            }, 5000);
        }
        
        console.log("Stashy: Performance monitoring started");
    };

    /**
     * Stop performance monitoring
     */
    window.stopPerformanceMonitoring = function() {
        performanceState.isMonitoring = false;
        console.log("Stashy: Performance monitoring stopped");
    };

    /**
     * Get current performance metrics
     */
    window.getPerformanceMetrics = function() {
        return { ...performanceState.metrics };
    };

    /**
     * Trigger performance optimization based on detected issues
     * @param {string} reason - The reason for optimization
     */
    function triggerPerformanceOptimization(reason) {
        console.log(`Stashy: Triggering performance optimization due to: ${reason}`);
        
        // Show user-friendly notification
        if (typeof window.showEnhancedStatus === 'function') {
            window.showEnhancedStatus('Optimizing performance for better experience...', 'info');
        }
        
        requestIdleCallback(() => {
            try {
                // Apply optimizations based on reason
                switch (reason) {
                    case 'low-fps':
                        optimizeForLowFPS();
                        break;
                    case 'high-memory':
                        optimizeForHighMemory();
                        break;
                    case 'high-complexity':
                        optimizeForHighComplexity();
                        break;
                }
                
                // Notify completion
                if (typeof window.showEnhancedStatus === 'function') {
                    window.showEnhancedStatus('Performance optimization completed', 'success');
                }
            } catch (error) {
                console.error("Stashy: Error during performance optimization:", error);
                if (typeof window.showEnhancedStatus === 'function') {
                    window.showEnhancedStatus('Performance optimization completed with minor issues', 'warning');
                }
            }
        }, { timeout: 5000 });
    }

    /**
     * Optimize for low FPS
     */
    function optimizeForLowFPS() {
        // Reduce image quality temporarily
        const images = document.querySelectorAll('img, .Stashy-video-screenshot');
        images.forEach((img, index) => {
            if (index > 10) { // Keep first 10 at full quality
                img.style.imageRendering = 'pixelated';
                img.style.filter = 'brightness(0.95)';
            }
        });
        
        // Disable animations temporarily
        document.body.style.setProperty('--animation-duration', '0s', 'important');
        
        console.log("Stashy: Applied low FPS optimizations");
    }

    /**
     * Optimize for high memory usage
     */
    function optimizeForHighMemory() {
        // Convert large images to thumbnails
        const largeImages = document.querySelectorAll('img[src*="data:image"]');
        largeImages.forEach((img, index) => {
            if (index > 5 && img.src.length > 100000) {
                // Create thumbnail version
                const canvas = document.createElement('canvas');
                const ctx = window.createOptimizedCanvasContext(canvas);
                
                canvas.width = Math.min(img.naturalWidth, 200);
                canvas.height = Math.min(img.naturalHeight, 200);
                
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                img.dataset.originalSrc = img.src;
                img.src = canvas.toDataURL('image/jpeg', 0.7);
                img.dataset.optimized = 'true';
            }
        });
        
        // Force garbage collection if available
        if (window.gc) {
            setTimeout(() => window.gc(), 1000);
        }
        
        console.log("Stashy: Applied high memory optimizations");
    }

    /**
     * Optimize for high DOM complexity
     */
    function optimizeForHighComplexity() {
        // Implement virtual scrolling for large lists
        const noteContainer = document.querySelector('[contenteditable="true"]');
        if (noteContainer) {
            const images = noteContainer.querySelectorAll('img');
            if (images.length > 15) {
                // Hide images that are far from viewport
                images.forEach((img, index) => {
                    if (index > 15) {
                        img.style.display = 'none';
                        img.dataset.virtualHidden = 'true';
                    }
                });
            }
        }
        
        console.log("Stashy: Applied high complexity optimizations");
    }

    /**
     * Restore optimizations when performance improves
     */
    window.restorePerformanceOptimizations = function() {
        // Restore image quality
        const optimizedImages = document.querySelectorAll('img[data-optimized="true"]');
        optimizedImages.forEach(img => {
            if (img.dataset.originalSrc) {
                img.src = img.dataset.originalSrc;
                delete img.dataset.originalSrc;
                delete img.dataset.optimized;
            }
        });
        
        // Restore animations
        document.body.style.removeProperty('--animation-duration');
        
        // Restore hidden images
        const hiddenImages = document.querySelectorAll('img[data-virtual-hidden="true"]');
        hiddenImages.forEach(img => {
            img.style.display = '';
            delete img.dataset.virtualHidden;
        });
        
        console.log("Stashy: Restored performance optimizations");
    };

    console.log("Stashy: Performance Optimizer Loaded");
}
