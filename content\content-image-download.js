/**
 * Stashy Image Download Functionality
 * Handles downloading images from notes with various sources (data URLs, external URLs, video screenshots)
 */

// Global variables for image download functionality
let customContextMenu = null;
let currentRightClickedImage = null;

/**
 * Handles image download from context menu
 * @param {string} srcUrl - The source URL of the image
 * @param {string} pageUrl - The URL of the page where the image was found
 */
function handleImageDownload(srcUrl, pageUrl) {
    console.log("Stashy: Handling image download from context menu", { srcUrl, pageUrl });

    if (!srcUrl) {
        console.error("Stashy: No image source URL provided for download");
        showImageDownloadFeedback("❌ No image source found", true);
        return;
    }

    // Generate filename based on image source and current context
    const filename = generateImageFilename(srcUrl, pageUrl);

    // Download the image
    downloadImageFromUrl(srcUrl, filename);
}

/**
 * Handles copying image to clipboard from context menu
 * @param {string} srcUrl - The source URL of the image
 * @param {string} pageUrl - The URL of the page where the image was found
 */
function handleImageCopyToClipboard(srcUrl, pageUrl) {
    console.log("Stashy: Handling image copy to clipboard from context menu", { srcUrl, pageUrl });

    if (!srcUrl) {
        console.error("Stashy: No image source URL provided for clipboard copy");
        showImageDownloadFeedback("❌ No image source found", true);
        return;
    }

    // Copy the image to clipboard
    copyImageToClipboard(srcUrl);
}

/**
 * Downloads an image from a given URL
 * @param {string} imageUrl - The URL or data URL of the image
 * @param {string} filename - The filename to save the image as
 */
function downloadImageFromUrl(imageUrl, filename) {
    console.log("Stashy: Starting image download", { imageUrl: imageUrl.substring(0, 100) + "...", filename });
    
    try {
        // Show download feedback
        showImageDownloadFeedback("⬇️ Downloading image...", false);
        
        // Send message to background script to handle the download
        chrome.runtime.sendMessage({
            action: 'downloadImageFile',
            imageUrl: imageUrl,
            filename: filename
        }, (response) => {
            if (chrome.runtime.lastError) {
                console.error("Stashy: Error sending download message:", chrome.runtime.lastError);
                showImageDownloadFeedback("❌ Download failed", true);
                return;
            }
            
            if (response && response.success) {
                console.log("Stashy: Image download successful", response);
                showImageDownloadFeedback("✅ Image downloaded!", false);
            } else {
                console.error("Stashy: Image download failed", response);
                showImageDownloadFeedback("❌ Download failed", true);
            }
        });
    } catch (error) {
        console.error("Stashy: Error during image download:", error);
        showImageDownloadFeedback("❌ Download error", true);
    }
}

/**
 * Copies an image to the clipboard
 * @param {string} imageUrl - The URL or data URL of the image
 */
async function copyImageToClipboard(imageUrl) {
    console.log("Stashy: Starting image copy to clipboard", { imageUrl: imageUrl.substring(0, 100) + "..." });

    try {
        // Show copy feedback
        showImageDownloadFeedback("📋 Copying image...", false);

        // Check if clipboard API is available
        if (!navigator.clipboard || !navigator.clipboard.write) {
            throw new Error("Clipboard API not available");
        }

        let blob;

        if (imageUrl.startsWith('data:')) {
            // Handle data URLs
            const response = await fetch(imageUrl);
            blob = await response.blob();
        } else {
            // Handle external URLs
            const response = await fetch(imageUrl);
            if (!response.ok) {
                throw new Error(`Failed to fetch image: ${response.status}`);
            }
            blob = await response.blob();
        }

        // Create clipboard item
        const clipboardItem = new ClipboardItem({
            [blob.type]: blob
        });

        // Write to clipboard
        await navigator.clipboard.write([clipboardItem]);

        console.log("Stashy: Image copied to clipboard successfully");
        showImageDownloadFeedback("✅ Image copied to clipboard!", false);

    } catch (error) {
        console.error("Stashy: Error copying image to clipboard:", error);

        // Fallback: copy image URL to clipboard
        try {
            await navigator.clipboard.writeText(imageUrl);
            showImageDownloadFeedback("📋 Image URL copied to clipboard", false);
        } catch (fallbackError) {
            console.error("Stashy: Fallback clipboard copy failed:", fallbackError);
            showImageDownloadFeedback("❌ Copy to clipboard failed", true);
        }
    }
}

/**
 * Generates an appropriate filename for the image download
 * @param {string} srcUrl - The source URL of the image
 * @param {string} pageUrl - The URL of the page
 * @returns {string} Generated filename
 */
function generateImageFilename(srcUrl, pageUrl) {
    const timestamp = new Date().toISOString().replace(/[:\-]/g, '').split('.')[0];
    let filename = `Stashy_Image_${timestamp}`;
    
    try {
        // If it's a data URL, determine the format
        if (srcUrl.startsWith('data:')) {
            const mimeMatch = srcUrl.match(/data:image\/([^;]+)/);
            if (mimeMatch) {
                const format = mimeMatch[1];
                filename += `.${format}`;
            } else {
                filename += '.png'; // Default fallback
            }
        } else {
            // For external URLs, try to extract the extension
            const urlObj = new URL(srcUrl);
            const pathname = urlObj.pathname;
            const extensionMatch = pathname.match(/\.([a-zA-Z0-9]+)$/);
            
            if (extensionMatch) {
                filename += `.${extensionMatch[1]}`;
            } else {
                filename += '.png'; // Default fallback
            }
            
            // Add domain info for external images
            const domain = urlObj.hostname.replace(/^www\./, '');
            filename = `Stashy_Image_${domain}_${timestamp}${extensionMatch ? '.' + extensionMatch[1] : '.png'}`;
        }
    } catch (error) {
        console.warn("Stashy: Error parsing image URL for filename generation:", error);
        filename += '.png'; // Fallback
    }
    
    return filename;
}

/**
 * Shows feedback for image download operations
 * @param {string} message - The feedback message
 * @param {boolean} isError - Whether this is an error message
 */
function showImageDownloadFeedback(message, isError = false) {
    // Use existing feedback system if available, otherwise create a simple one
    if (typeof showCopyFeedback === 'function') {
        showCopyFeedback(message, isError, 'Image download');
    } else {
        // Simple fallback feedback
        console.log(`Stashy Image Download: ${message}`);
        
        // Create a temporary notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${isError ? '#ff4444' : '#4CAF50'};
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            z-index: 10000;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            transition: opacity 0.3s ease;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
}



/**
 * Creates a custom context menu for images
 * @param {number} x - X position for the menu
 * @param {number} y - Y position for the menu
 * @param {HTMLImageElement} imageElement - The image element that was right-clicked
 */
function createCustomImageContextMenu(x, y, imageElement) {
    // Remove any existing custom menu
    removeCustomContextMenu();

    // Create menu container
    const menu = document.createElement('div');
    menu.className = 'Stashy-custom-context-menu';
    menu.style.cssText = `
        position: fixed;
        top: ${y}px;
        left: ${x}px;
        background: white;
        border: 1px solid #ccc;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 999999;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        min-width: 200px;
        overflow: hidden;
        user-select: none;
    `;

    // Create download option
    const downloadOption = document.createElement('div');
    downloadOption.className = 'Stashy-context-menu-item';
    downloadOption.innerHTML = '⬇️ Download Image';
    downloadOption.style.cssText = `
        padding: 12px 16px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
        transition: background-color 0.2s ease;
    `;

    // Create copy option
    const copyOption = document.createElement('div');
    copyOption.className = 'Stashy-context-menu-item';
    copyOption.innerHTML = '📋 Copy to Clipboard';
    copyOption.style.cssText = `
        padding: 12px 16px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    `;

    // Add hover effects
    [downloadOption, copyOption].forEach(option => {
        option.addEventListener('mouseenter', () => {
            option.style.backgroundColor = '#f0f0f0';
        });

        option.addEventListener('mouseleave', () => {
            option.style.backgroundColor = 'transparent';
        });
    });

    // Add click handlers
    downloadOption.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const imageSrc = imageElement.src;
        const filename = generateImageFilename(imageSrc, window.location.href);
        downloadImageFromUrl(imageSrc, filename);

        removeCustomContextMenu();
    });

    copyOption.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const imageSrc = imageElement.src;
        copyImageToClipboard(imageSrc);

        removeCustomContextMenu();
    });

    // Assemble menu
    menu.appendChild(downloadOption);
    menu.appendChild(copyOption);

    // Add to document
    document.body.appendChild(menu);

    // Store reference
    customContextMenu = menu;
    currentRightClickedImage = imageElement;

    // Position menu to stay within viewport
    const rect = menu.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    if (rect.right > viewportWidth) {
        menu.style.left = (x - rect.width) + 'px';
    }

    if (rect.bottom > viewportHeight) {
        menu.style.top = (y - rect.height) + 'px';
    }

    return menu;
}

/**
 * Removes the custom context menu
 */
function removeCustomContextMenu() {
    if (customContextMenu) {
        if (customContextMenu.parentNode) {
            customContextMenu.parentNode.removeChild(customContextMenu);
        }
        customContextMenu = null;
        currentRightClickedImage = null;
    }
}

/**
 * Initializes image download functionality
 */
function initializeImageDownloadFeature() {
    console.log("Stashy: Initializing image download feature (custom context menu)");

    // Add right-click listener to intercept context menu on images
    document.addEventListener('contextmenu', (e) => {
        // Check if the target is an image
        if (e.target.tagName === 'IMG' && e.target.src) {
            e.preventDefault(); // Prevent default context menu
            e.stopPropagation();

            // Create custom context menu
            createCustomImageContextMenu(e.clientX, e.clientY, e.target);
        }
    }, true); // Use capture phase to intercept before other handlers

    // Add click listener to close custom menu when clicking elsewhere
    document.addEventListener('click', (e) => {
        if (customContextMenu && !customContextMenu.contains(e.target)) {
            removeCustomContextMenu();
        }
    });

    // Add escape key listener to close menu
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && customContextMenu) {
            removeCustomContextMenu();
        }
    });
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeImageDownloadFeature);
} else {
    initializeImageDownloadFeature();
}
