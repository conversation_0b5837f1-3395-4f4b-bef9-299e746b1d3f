/**
 * Video Transcript Extraction Module
 * Handles extraction and formatting of video transcripts with clickable timestamps
 */

(function() {
    'use strict';

    /**
     * Main function to extract video transcript
     * @param {boolean} addToNotes - Whether to add transcript to notes (default: true)
     * @returns {Promise<boolean>} Success status
     */
    async function extractVideoTranscript(addToNotes = true) {
        console.log('Stashy: Starting video transcript extraction');
        
        try {
            // First, validate we're on a supported video platform
            const currentPageUrl = window.location.href;
            console.log(`Stashy: Processing transcript for page: ${currentPageUrl}`);

            // Get video information using the global function or fallback
            let videoInfo = null;
            if (typeof window.extractVideoInfo === 'function') {
                videoInfo = window.extractVideoInfo();
            } else if (typeof extractVideoInfo === 'function') {
                videoInfo = extractVideoInfo();
            } else {
                // Fallback: extract basic video info
                videoInfo = extractBasicVideoInfo();
            }

            if (!videoInfo) {
                throw new Error('No video detected on this page');
            }

            // Check if we're on YouTube (primary supported platform)
            const isYouTube = window.location.hostname.includes('youtube.com') ||
                             window.location.hostname.includes('youtu.be');

            if (!isYouTube) {
                // For testing purposes, allow local files and provide a more helpful message
                const isLocalTest = window.location.protocol === 'file:' ||
                                   window.location.hostname === 'localhost' ||
                                   window.location.hostname === '127.0.0.1';

                if (isLocalTest) {
                    console.log('Stashy: Running in test mode - simulating YouTube environment');
                    // Continue with extraction for testing
                } else {
                    throw new Error('Transcript extraction is currently only supported on YouTube videos');
                }
            }

            // Show processing message
            showTranscriptMessage('Extracting video transcript... (If this fails, try manually opening the transcript panel first)', 'info');

            // Check if transcript is available
            const transcriptAvailable = await checkTranscriptAvailability();

            if (!transcriptAvailable) {
                throw new Error('This video does not have available transcripts or captions. Please manually open the transcript panel first if available.');
            }

            // Extract transcript data
            const transcriptData = await extractYouTubeTranscript();
            
            if (!transcriptData || transcriptData.length === 0) {
                throw new Error('Failed to extract transcript data');
            }

            // Store transcript data globally for AI analysis access
            storeTranscriptDataGlobally(transcriptData, videoInfo);

            // Format transcript for display
            const formattedTranscript = formatTranscriptForDisplay(transcriptData, videoInfo);

            // Insert transcript into notes only if requested
            if (addToNotes) {
                console.log('Stashy: About to insert transcript into notes');
                await insertTranscriptIntoNotes(formattedTranscript);
                console.log('Stashy: Transcript insertion completed');
                showTranscriptMessage('Video transcript extracted and added to notes successfully!', 'success');
            } else {
                console.log('Stashy: Transcript extracted but not added to notes (for AI analysis)');
                showTranscriptMessage('Video transcript extracted for analysis!', 'success');
            }
            console.log('Stashy: Video transcript extraction completed successfully');
            return true;

        } catch (error) {
            console.error('Stashy: Error extracting video transcript:', error);
            showTranscriptMessage(`Transcript Error: ${error.message}`, 'error');

            // Try to close panel even if extraction failed
            try {
                await closeTranscriptPanel();
            } catch (closeError) {
                console.warn('Stashy: Error closing panel after failure:', closeError);
            }

            return false;
        }
    }

    /**
     * Checks if transcript/captions are available for the current video
     * @returns {Promise<boolean>} True if transcript is available
     */
    async function checkTranscriptAvailability() {
        console.log('Stashy: Checking transcript availability');

        try {
            // Check if we're in test mode
            const isLocalTest = window.location.protocol === 'file:' ||
                               window.location.hostname === 'localhost' ||
                               window.location.hostname === '127.0.0.1';

            if (isLocalTest) {
                console.log('Stashy: Test mode - simulating transcript availability');
                return true; // Always return true for testing
            }

            // Look for YouTube's transcript button or menu
            const transcriptSelectors = [
                'button[aria-label*="transcript" i]',
                'button[aria-label*="caption" i]',
                '[data-target-id="engagement-panel-transcript"]',
                'ytd-transcript-search-panel-renderer',
                '.ytd-transcript-footer-renderer',
                // More menu selectors
                'button[aria-label="More actions"]',
                'button[title="More actions"]',
                '.ytp-menuitem[aria-label*="transcript" i]'
            ];

            // Check if any transcript-related elements exist
            for (const selector of transcriptSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    console.log(`Stashy: Found transcript element with selector: ${selector}`);
                    return true;
                }
            }

            // Alternative check: look for the three-dot menu and check if transcript option exists
            const moreButton = document.querySelector('button[aria-label="More actions"]') || 
                              document.querySelector('button[title="More actions"]') ||
                              document.querySelector('.ytp-more-button');
            
            if (moreButton) {
                // Temporarily click to check menu contents
                moreButton.click();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                const transcriptMenuItem = document.querySelector('[role="menuitem"]:contains("Transcript")') ||
                                          document.querySelector('.ytp-menuitem:contains("Transcript")');
                
                // Close the menu
                moreButton.click();
                
                if (transcriptMenuItem) {
                    console.log('Stashy: Found transcript option in more menu');
                    return true;
                }
            }

            console.log('Stashy: No transcript availability detected');
            return false;

        } catch (error) {
            console.error('Stashy: Error checking transcript availability:', error);
            return false;
        }
    }

    /**
     * Extracts transcript data from YouTube
     * @returns {Promise<Array>} Array of transcript segments with timestamps
     */
    async function extractYouTubeTranscript() {
        console.log('Stashy: Extracting YouTube transcript data');

        try {
            // Check if we're in test mode
            const isLocalTest = window.location.protocol === 'file:' ||
                               window.location.hostname === 'localhost' ||
                               window.location.hostname === '127.0.0.1';

            if (isLocalTest) {
                console.log('Stashy: Test mode - returning mock transcript data');
                return generateMockTranscriptData();
            }

            // Check if transcript panel is already open
            let transcriptPanel = document.querySelector('ytd-transcript-search-panel-renderer, ytd-engagement-panel-section-list-renderer[target-id="engagement-panel-transcript"]');

            if (!transcriptPanel || transcriptPanel.offsetParent === null) {
                console.log('Stashy: Transcript panel not open, attempting to open it');
                // Try to open the transcript panel
                await openTranscriptPanel();
                // Wait for transcript panel to load
                await new Promise(resolve => setTimeout(resolve, 1500));

                // Check again if panel opened
                transcriptPanel = document.querySelector('ytd-transcript-search-panel-renderer, ytd-engagement-panel-section-list-renderer[target-id="engagement-panel-transcript"]');
            } else {
                console.log('Stashy: Transcript panel already open');
            }

            // Extract transcript segments
            const transcriptSegments = [];
            
            // Look for transcript items in the panel with improved selectors
            const transcriptSelectors = [
                // Modern YouTube transcript selectors
                'ytd-transcript-segment-renderer',
                'ytd-transcript-segment-list-renderer ytd-transcript-segment-renderer',
                '.ytd-transcript-segment-renderer',
                // Alternative selectors
                '[class*="transcript-segment"]',
                'ytd-transcript-search-panel-renderer [role="button"]',
                'ytd-engagement-panel-section-list-renderer [role="button"]',
                // Fallback selectors
                '.transcript-item',
                '.caption-line'
            ];

            let transcriptElements = [];
            let usedSelector = '';

            for (const selector of transcriptSelectors) {
                transcriptElements = document.querySelectorAll(selector);
                if (transcriptElements.length > 0) {
                    usedSelector = selector;
                    console.log(`Stashy: Found ${transcriptElements.length} transcript elements with selector: ${selector}`);
                    break;
                }
            }

            // If no elements found, try to find the transcript container and look inside
            if (transcriptElements.length === 0) {
                const transcriptContainer = document.querySelector('ytd-transcript-search-panel-renderer, ytd-engagement-panel-section-list-renderer[target-id="engagement-panel-transcript"]');
                if (transcriptContainer) {
                    // Look for any clickable elements that might be transcript segments
                    const allButtons = transcriptContainer.querySelectorAll('[role="button"], button, .yt-simple-endpoint');
                    transcriptElements = Array.from(allButtons).filter(btn => {
                        const text = btn.textContent?.trim() || '';
                        // Filter for elements that look like transcript segments (have time and text)
                        return text.length > 10 && /\d+:\d+/.test(text);
                    });

                    if (transcriptElements.length > 0) {
                        usedSelector = 'container-based detection';
                        console.log(`Stashy: Found ${transcriptElements.length} transcript elements using container-based detection`);
                    }
                }
            }
            
            if (transcriptElements.length === 0) {
                throw new Error('Could not find transcript elements in the panel');
            }
            
            // Extract data from each transcript element
            transcriptElements.forEach((element, index) => {
                try {
                    let timestamp = '';
                    let text = '';
                    let startTimeSeconds = 0;

                    // Get the full text content
                    const fullText = element.textContent?.trim() || '';

                    if (!fullText) return;

                    // Method 1: Modern YouTube format - timestamp at start followed by text
                    const timestampMatch = fullText.match(/^(\d{1,2}:\d{2}(?::\d{2})?)\s*(.+)$/s);
                    if (timestampMatch) {
                        timestamp = timestampMatch[1];
                        text = timestampMatch[2].trim();
                        startTimeSeconds = parseTimestampToSeconds(timestamp);
                    } else {
                        // Method 2: Try to extract from data attributes
                        const dataTime = element.getAttribute('data-start-time') ||
                                        element.getAttribute('data-time') ||
                                        element.getAttribute('data-timestamp');

                        if (dataTime) {
                            startTimeSeconds = parseFloat(dataTime);
                            timestamp = formatSecondsToTimestamp(startTimeSeconds);
                            text = fullText;
                        } else {
                            // Method 3: Look for time pattern anywhere in text
                            const timePattern = /(\d{1,2}:\d{2}(?::\d{2})?)/;
                            const timeMatch = fullText.match(timePattern);

                            if (timeMatch) {
                                timestamp = timeMatch[1];
                                startTimeSeconds = parseTimestampToSeconds(timestamp);
                                // Remove timestamp from text
                                text = fullText.replace(timePattern, '').trim();
                            } else {
                                // Skip elements without recognizable timestamps
                                return;
                            }
                        }
                    }

                    // Clean up text
                    if (text) {
                        // Remove any remaining timestamp patterns
                        text = text.replace(/^\d{1,2}:\d{2}(?::\d{2})?\s*/, '').trim();
                        // Remove extra whitespace and normalize
                        text = text.replace(/\s+/g, ' ').trim();
                        // Remove common YouTube transcript artifacts
                        text = text.replace(/^\[.*?\]\s*/, ''); // Remove [MUSIC], [APPLAUSE], etc.
                    }

                    // Only add if we have meaningful content
                    if (text && text.length > 2 && timestamp && startTimeSeconds >= 0) {
                        transcriptSegments.push({
                            timestamp: timestamp,
                            text: text,
                            startTime: startTimeSeconds,
                            index: index
                        });
                    }
                } catch (segmentError) {
                    console.warn(`Stashy: Error processing transcript segment ${index}:`, segmentError);
                }
            });
            
            console.log(`Stashy: Extracted ${transcriptSegments.length} transcript segments`);

            // Close the transcript panel after extraction
            console.log('Stashy: Attempting to close transcript panel after extraction');
            try {
                await closeTranscriptPanel();
            } catch (closeError) {
                console.warn('Stashy: Error closing transcript panel:', closeError);
            }

            return transcriptSegments;

        } catch (error) {
            console.error('Stashy: Error extracting YouTube transcript:', error);
            // Try to close panel even if extraction failed
            await closeTranscriptPanel();
            throw error;
        }
    }

    /**
     * Opens the YouTube transcript panel
     * @returns {Promise<void>}
     */
    async function openTranscriptPanel() {
        console.log('Stashy: Opening transcript panel');

        try {
            // Check if transcript panel is already open
            const existingPanel = document.querySelector('ytd-transcript-search-panel-renderer, ytd-engagement-panel-section-list-renderer[target-id="engagement-panel-transcript"]');
            if (existingPanel && existingPanel.offsetParent !== null) {
                console.log('Stashy: Transcript panel already open');
                return;
            }

            // Method 1: Look for direct transcript button in description area
            const transcriptButtonSelectors = [
                'button[aria-label*="Show transcript" i]',
                'button[aria-label*="transcript" i]',
                '[data-target-id="engagement-panel-transcript"]',
                'button[title*="transcript" i]',
                'ytd-button-renderer button[aria-label*="transcript" i]',
                '#description button[aria-label*="transcript" i]'
            ];

            for (const selector of transcriptButtonSelectors) {
                const transcriptButton = document.querySelector(selector);
                if (transcriptButton && transcriptButton.offsetParent !== null) { // Check if visible
                    console.log(`Stashy: Found transcript button with selector: ${selector}`);
                    transcriptButton.click();
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for panel to open
                    console.log(`Stashy: Clicked transcript button`);
                    return;
                }
            }

            // Method 2: Try the description area more button
            const descriptionMoreButtons = document.querySelectorAll('#description button, #meta button, ytd-video-secondary-info-renderer button');
            for (const button of descriptionMoreButtons) {
                if (button.textContent?.toLowerCase().includes('more') ||
                    button.getAttribute('aria-label')?.toLowerCase().includes('more') ||
                    button.textContent?.toLowerCase().includes('show')) {
                    console.log('Stashy: Trying description more button');
                    button.click();
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // Check if transcript button appeared
                    const transcriptButton = document.querySelector('button[aria-label*="Show transcript" i], button[aria-label*="transcript" i]');
                    if (transcriptButton && transcriptButton.offsetParent !== null) {
                        transcriptButton.click();
                        console.log('Stashy: Found and clicked transcript button after expanding description');
                        return;
                    }
                }
            }

            // Method 3: Try the three-dot menu approach (video player menu)
            const moreButtonSelectors = [
                'button[aria-label="More actions"]',
                'button[title="More actions"]',
                '#menu-button button',
                'ytd-menu-renderer button[aria-label*="More" i]',
                '.ytp-more-button' // Video player more button
            ];

            let moreButton = null;
            for (const selector of moreButtonSelectors) {
                moreButton = document.querySelector(selector);
                if (moreButton && moreButton.offsetParent !== null) {
                    console.log(`Stashy: Found more button with selector: ${selector}`);
                    break;
                }
            }

            if (moreButton) {
                console.log('Stashy: Opening more actions menu');
                moreButton.click();
                await new Promise(resolve => setTimeout(resolve, 800));

                // Look for transcript option in the menu with better detection
                const menuItems = document.querySelectorAll('[role="menuitem"], .ytp-menuitem, ytd-menu-service-item-renderer');
                let transcriptMenuItem = null;

                for (const item of menuItems) {
                    const text = item.textContent?.toLowerCase() || '';
                    const ariaLabel = item.getAttribute('aria-label')?.toLowerCase() || '';

                    if (text.includes('transcript') || text.includes('show transcript') ||
                        ariaLabel.includes('transcript') || ariaLabel.includes('show transcript')) {
                        transcriptMenuItem = item;
                        console.log('Stashy: Found transcript menu item:', text || ariaLabel);
                        break;
                    }
                }

                if (transcriptMenuItem) {
                    transcriptMenuItem.click();
                    console.log('Stashy: Clicked transcript menu item');
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    return;
                } else {
                    // Close menu if transcript not found
                    console.log('Stashy: Transcript option not found in menu, closing menu');
                    document.body.click(); // Click outside to close menu
                }
            }

            // Method 3: Try keyboard shortcut (if available)
            console.log('Stashy: Trying keyboard shortcut approach');
            const videoElement = document.querySelector('video');
            if (videoElement) {
                videoElement.focus();
                // Some YouTube versions support 'c' key for captions/transcript
                const event = new KeyboardEvent('keydown', { key: 'c', code: 'KeyC' });
                videoElement.dispatchEvent(event);
                await new Promise(resolve => setTimeout(resolve, 500));

                // Check if transcript panel appeared
                const transcriptPanel = document.querySelector('ytd-transcript-search-panel-renderer, [data-target-id="engagement-panel-transcript"]');
                if (transcriptPanel) {
                    console.log('Stashy: Transcript panel opened via keyboard shortcut');
                    return;
                }
            }

            throw new Error('Could not find or open transcript panel. This video may not have transcripts available.');

        } catch (error) {
            console.error('Stashy: Error opening transcript panel:', error);
            throw error;
        }
    }

    /**
     * Closes the YouTube transcript panel
     * @returns {Promise<void>}
     */
    async function closeTranscriptPanel() {
        console.log('Stashy: Attempting to close transcript panel');

        try {
            // Wait a moment for any animations to complete
            await new Promise(resolve => setTimeout(resolve, 500));

            // First check if panel is actually open
            const transcriptPanel = document.querySelector('ytd-transcript-search-panel-renderer, ytd-engagement-panel-section-list-renderer[target-id="engagement-panel-transcript"]');

            if (!transcriptPanel || transcriptPanel.offsetParent === null) {
                console.log('Stashy: Transcript panel is not open or not visible');
                return;
            }

            console.log('Stashy: Transcript panel is open, attempting to close');

            // Method 1: Try to find and click the transcript button again to toggle it off
            const transcriptToggleSelectors = [
                'button[aria-label*="Show transcript" i]',
                'button[aria-label*="transcript" i]',
                '[data-target-id="engagement-panel-transcript"]',
                // Additional selectors for different YouTube layouts
                '#description button[aria-label*="transcript" i]',
                'ytd-button-renderer button[aria-label*="transcript" i]',
                '.ytd-menu-renderer button[aria-label*="transcript" i]'
            ];

            for (const selector of transcriptToggleSelectors) {
                const toggleButton = document.querySelector(selector);
                if (toggleButton && toggleButton.offsetParent !== null) {
                    console.log(`Stashy: Found transcript toggle button: ${selector}`);

                    // Click multiple times to ensure it registers
                    toggleButton.click();
                    await new Promise(resolve => setTimeout(resolve, 300));
                    toggleButton.click();
                    await new Promise(resolve => setTimeout(resolve, 800));

                    // Check if panel closed
                    const panelAfterClick = document.querySelector('ytd-transcript-search-panel-renderer, ytd-engagement-panel-section-list-renderer[target-id="engagement-panel-transcript"]');
                    if (!panelAfterClick || panelAfterClick.offsetParent === null) {
                        console.log('Stashy: Transcript panel closed successfully via toggle');
                        return;
                    }
                }
            }

            // Method 2: Look for close buttons within the panel
            const closeSelectors = [
                'ytd-transcript-search-panel-renderer button[aria-label*="close" i]',
                'ytd-engagement-panel-section-list-renderer button[aria-label*="close" i]',
                'ytd-transcript-search-panel-renderer .yt-icon-button',
                '#dismissible button',
                '.ytd-engagement-panel-title-header-renderer button'
            ];

            for (const selector of closeSelectors) {
                const closeButton = document.querySelector(selector);
                if (closeButton && closeButton.offsetParent !== null) {
                    console.log(`Stashy: Found close button: ${selector}`);
                    closeButton.click();
                    await new Promise(resolve => setTimeout(resolve, 500));

                    // Check if panel closed
                    const panelAfterClose = document.querySelector('ytd-transcript-search-panel-renderer, ytd-engagement-panel-section-list-renderer[target-id="engagement-panel-transcript"]');
                    if (!panelAfterClose || panelAfterClose.offsetParent === null) {
                        console.log('Stashy: Transcript panel closed successfully via close button');
                        return;
                    }
                }
            }

            // Method 3: Try clicking outside the panel to close it
            console.log('Stashy: Trying to close panel by clicking outside');
            const mainContent = document.querySelector('#primary, #content, #player-container');
            if (mainContent) {
                mainContent.click();
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Method 4: Try Escape key
            console.log('Stashy: Trying Escape key to close transcript panel');
            const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape', code: 'Escape', bubbles: true });
            document.dispatchEvent(escapeEvent);
            transcriptPanel.dispatchEvent(escapeEvent);

            await new Promise(resolve => setTimeout(resolve, 500));

            // Method 5: Force close by hiding the panel
            console.log('Stashy: Trying to force close by hiding panel');
            const panelToHide = document.querySelector('ytd-transcript-search-panel-renderer, ytd-engagement-panel-section-list-renderer[target-id="engagement-panel-transcript"]');
            if (panelToHide) {
                panelToHide.style.display = 'none';
                console.log('Stashy: Forced panel to hide');
            }

            // Final check
            await new Promise(resolve => setTimeout(resolve, 500));
            const finalPanel = document.querySelector('ytd-transcript-search-panel-renderer, ytd-engagement-panel-section-list-renderer[target-id="engagement-panel-transcript"]');
            if (!finalPanel || finalPanel.offsetParent === null || finalPanel.style.display === 'none') {
                console.log('Stashy: Transcript panel closed successfully');
            } else {
                console.log('Stashy: Could not close transcript panel automatically - panel may still be visible');
                // Try one more time with a different approach
                setTimeout(() => {
                    const persistentPanel = document.querySelector('ytd-transcript-search-panel-renderer, ytd-engagement-panel-section-list-renderer[target-id="engagement-panel-transcript"]');
                    if (persistentPanel) {
                        persistentPanel.style.display = 'none';
                        console.log('Stashy: Applied final force close to transcript panel');
                    }
                }, 2000);
            }

        } catch (error) {
            console.warn('Stashy: Error closing transcript panel:', error);
            // Don't throw error here as it's not critical
        }
    }

    /**
     * Parses timestamp string to seconds
     * @param {string} timestamp - Timestamp in format "MM:SS" or "HH:MM:SS"
     * @returns {number} Time in seconds
     */
    function parseTimestampToSeconds(timestamp) {
        if (!timestamp || typeof timestamp !== 'string') return 0;
        
        const parts = timestamp.split(':').map(part => parseInt(part, 10));
        
        if (parts.length === 2) {
            // MM:SS format
            return parts[0] * 60 + parts[1];
        } else if (parts.length === 3) {
            // HH:MM:SS format
            return parts[0] * 3600 + parts[1] * 60 + parts[2];
        }
        
        return 0;
    }

    /**
     * Formats seconds to timestamp string
     * @param {number} seconds - Time in seconds
     * @returns {string} Formatted timestamp
     */
    function formatSecondsToTimestamp(seconds) {
        if (typeof seconds === 'string') {
            seconds = parseFloat(seconds);
        }
        
        if (isNaN(seconds) || seconds < 0) return '0:00';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    /**
     * Formats transcript data for display in notes
     * @param {Array} transcriptData - Array of transcript segments
     * @param {Object} videoInfo - Video information
     * @returns {string} Formatted HTML transcript
     */
    function formatTranscriptForDisplay(transcriptData, videoInfo) {
        console.log('Stashy: Formatting transcript for display');

        if (!transcriptData || transcriptData.length === 0) {
            return '<p>No transcript data available.</p>';
        }

        // Group transcript segments into logical paragraphs
        const paragraphs = groupTranscriptIntoParagraphs(transcriptData);

        let formattedHtml = `
            <div class="stashy-transcript-container" style="margin: 20px 0; padding: 15px; border: 1px solid #e0e0e0; border-radius: 8px; background: #fafafa; clear: both; display: block; width: 100%;">
                <div class="transcript-header" style="margin-bottom: 8px; padding-bottom: 6px; border-bottom: 1px solid #e0e0e0;">
                    <h3 style="margin: 0 0 2px 0; color: #1976d2; font-size: 16px;">📝 Video Transcript</h3>
                    <div style="font-size: 14px; color: #666;">
                        <strong>${videoInfo.title || 'Video'}</strong>
                        ${videoInfo.channel ? ` • ${videoInfo.channel}` : ''}
                    </div>
                </div>
                <div class="transcript-content" style="line-height: 1.6; clear: both; display: block; width: 100%;">
        `;

        paragraphs.forEach((paragraph) => {
            const timestamp = paragraph.timestamp;
            const text = paragraph.text;
            const startTime = paragraph.startTime;

            formattedHtml += `<div class="transcript-paragraph" style="display: block; margin-bottom: 15px; padding: 10px; background: #f9f9f9; border-radius: 6px; border-left: 3px solid #1976d2; clear: both; width: 100%; box-sizing: border-box;"><div style="margin-bottom: 8px;"><span class="stashy-timestamp-btn" data-seek-time="${startTime}" style="display: inline-block; background: #1976d2; color: white; padding: 6px 10px; border-radius: 4px; font-weight: 600; cursor: pointer; font-size: 14px; min-width: 60px; text-align: center;" title="Click to jump to ${timestamp}">${timestamp}</span></div><div class="transcript-text" style="color: #333; font-size: 15px; line-height: 1.6; display: block; width: 100%;">${escapeHtml(text)}</div></div>`;
        });

        formattedHtml += `
                </div>
            </div>
        `;

        return formattedHtml;
    }

    /**
     * Stores transcript data globally for AI analysis access
     * @param {Array} transcriptData - Array of transcript segments
     * @param {Object} videoInfo - Video information
     */
    function storeTranscriptDataGlobally(transcriptData, videoInfo) {
        console.log('Stashy: Storing transcript data globally for AI analysis');

        // Convert transcript segments to plain text for AI analysis
        const transcriptText = transcriptData.map(segment => segment.text).join(' ');

        // Store in global object for AI features to access
        if (!window.StashyTranscriptData) {
            window.StashyTranscriptData = {};
        }

        const videoKey = videoInfo.url || window.location.href;
        window.StashyTranscriptData[videoKey] = {
            transcriptText: transcriptText,
            transcriptSegments: transcriptData,
            videoInfo: videoInfo,
            extractedAt: Date.now()
        };

        // Also update the videoInfo object with transcript for immediate access
        videoInfo.transcript = transcriptText;

        console.log(`Stashy: Stored transcript data for video: ${videoInfo.title} (${transcriptText.length} chars)`);
    }

    /**
     * Retrieves stored transcript data for AI analysis
     * @param {string} videoUrl - Video URL to retrieve transcript for
     * @returns {Object|null} Stored transcript data or null
     */
    function getStoredTranscriptData(videoUrl) {
        if (!window.StashyTranscriptData) {
            return null;
        }

        const videoKey = videoUrl || window.location.href;
        const storedData = window.StashyTranscriptData[videoKey];

        if (storedData) {
            console.log(`Stashy: Retrieved stored transcript data for: ${storedData.videoInfo.title}`);
            return storedData;
        }

        return null;
    }

    /**
     * Groups transcript segments into logical paragraphs
     * @param {Array} transcriptData - Array of transcript segments
     * @returns {Array} Array of paragraph objects
     */
    function groupTranscriptIntoParagraphs(transcriptData) {
        if (!transcriptData || transcriptData.length === 0) {
            return [];
        }

        const paragraphs = [];
        let currentParagraph = null;
        const PARAGRAPH_TIME_GAP = 30; // seconds - much longer for fewer paragraphs
        const PARAGRAPH_LENGTH_LIMIT = 800; // characters - much longer for substantial paragraphs
        const MIN_PARAGRAPH_LENGTH = 100; // minimum characters before considering a new paragraph

        transcriptData.forEach((segment) => {
            // Clean up the text
            const cleanText = segment.text.trim();
            if (!cleanText) return;

            // More selective paragraph breaking logic
            const shouldStartNewParagraph = !currentParagraph ||
                // Only break on significant time gaps
                (segment.startTime - currentParagraph.startTime > PARAGRAPH_TIME_GAP) ||
                // Only break on very long paragraphs
                (currentParagraph.text.length > PARAGRAPH_LENGTH_LIMIT) ||
                // Break on speaker changes (detected by names in caps)
                (currentParagraph.text.length > MIN_PARAGRAPH_LENGTH &&
                 /^[A-Z][A-Z\s]+:/.test(cleanText) &&
                 !/^[A-Z][A-Z\s]+:/.test(currentParagraph.text)) ||
                // Break on major topic shifts (detected by certain phrases)
                (currentParagraph.text.length > MIN_PARAGRAPH_LENGTH &&
                 /^(Today|Now|Let me|So|But|However|Meanwhile|Next|Finally)/i.test(cleanText));

            if (shouldStartNewParagraph) {
                if (currentParagraph && currentParagraph.text.length > 20) { // Only add substantial paragraphs
                    paragraphs.push(currentParagraph);
                }
                currentParagraph = {
                    timestamp: segment.timestamp,
                    startTime: segment.startTime,
                    text: cleanText
                };
            } else {
                // Append to current paragraph with proper spacing
                const needsSpace = !currentParagraph.text.endsWith(' ') && !cleanText.startsWith(' ');
                const separator = needsSpace ? ' ' : '';
                currentParagraph.text += separator + cleanText;
            }
        });

        // Add the last paragraph
        if (currentParagraph) {
            paragraphs.push(currentParagraph);
        }

        return paragraphs;
    }

    /**
     * Inserts formatted transcript into notes
     * @param {string} formattedTranscript - HTML formatted transcript
     * @param {Object} videoInfo - Video information
     * @returns {Promise<void>}
     */
    async function insertTranscriptIntoNotes(formattedTranscript) {
        console.log('Stashy: Inserting transcript into notes');
        console.log('Stashy: Transcript length:', formattedTranscript.length);

        try {
            // Debug: Check what elements are available
            console.log('Stashy: Checking available note elements...');
            const stashyText = document.querySelector('#Stashy-text');
            const contentEditables = document.querySelectorAll('[contenteditable="true"], [contenteditable]');

            console.log('Stashy: #Stashy-text found:', !!stashyText);
            console.log('Stashy: Contenteditable elements found:', contentEditables.length);

            // Try multiple insertion methods
            let insertionSuccessful = false;

            // Method 1: Try the global insertAiResultWithHtml function
            if (typeof window.insertAiResultWithHtml === 'function') {
                try {
                    console.log('Stashy: Using insertAiResultWithHtml');
                    await window.insertAiResultWithHtml(formattedTranscript, 'Video Transcript');
                    insertionSuccessful = true;
                    console.log('Stashy: insertAiResultWithHtml succeeded');
                } catch (error) {
                    console.warn('Stashy: insertAiResultWithHtml failed:', error);
                }
            } else {
                console.log('Stashy: insertAiResultWithHtml not available');
            }

            // Method 2: Manual insertion if first method failed
            if (!insertionSuccessful) {
                console.log('Stashy: Using manual insertion');
                insertTranscriptManually(formattedTranscript);
                insertionSuccessful = true;
                console.log('Stashy: Manual insertion completed');
            }

            if (!insertionSuccessful) {
                throw new Error('All insertion methods failed');
            }

            // Add the timestamp click functionality
            addTimestampClickHandlers();

        } catch (error) {
            console.error('Stashy: Error inserting transcript into notes:', error);
            throw error;
        }
    }

    /**
     * Adds click handlers to timestamp elements for video seeking using event delegation
     */
    function addTimestampClickHandlers() {
        console.log('Stashy: Setting up timestamp click handlers with event delegation');

        // Remove any existing listeners to prevent duplicates
        document.removeEventListener('click', handleTimestampClick);

        // Add event delegation listener to document
        document.addEventListener('click', handleTimestampClick);

        console.log('Stashy: Timestamp click handlers set up successfully');
    }

    /**
     * Handles timestamp clicks using event delegation
     * @param {Event} event - Click event
     */
    function handleTimestampClick(event) {
        // Check if the clicked element is a timestamp button
        if (event.target.classList.contains('stashy-timestamp-btn')) {
            event.preventDefault();
            event.stopPropagation();

            const seekTime = event.target.getAttribute('data-seek-time');
            const timestampText = event.target.textContent;

            console.log(`Stashy: Timestamp clicked: ${timestampText} (${seekTime} seconds)`);

            if (seekTime) {
                const timeInSeconds = parseFloat(seekTime);
                if (!isNaN(timeInSeconds)) {
                    seekToVideoTime(timeInSeconds);
                } else {
                    console.error('Stashy: Invalid seek time:', seekTime);
                }
            } else {
                console.error('Stashy: No seek time found on timestamp element');
            }
        }
    }

    /**
     * Seeks to a specific time in the video
     * @param {number} timeInSeconds - Time to seek to in seconds
     */
    function seekToVideoTime(timeInSeconds) {
        console.log(`Stashy: Seeking to ${timeInSeconds} seconds`);

        try {
            // Find video element using multiple methods
            let videoElement = null;

            // Method 1: Look for playing videos first
            const videos = document.querySelectorAll('video');
            console.log(`Stashy: Found ${videos.length} video elements`);

            for (const video of videos) {
                try {
                    // Prefer playing videos with valid duration
                    if (!video.paused && video.duration > 0 && video.currentTime >= 0) {
                        const rect = video.getBoundingClientRect();
                        if (rect.width > 0 && rect.height > 0) {
                            videoElement = video;
                            console.log('Stashy: Found playing video with valid dimensions');
                            break;
                        }
                    }
                } catch (e) {
                    console.warn('Stashy: Error checking video:', e);
                }
            }

            // Method 2: YouTube specific selectors
            if (!videoElement) {
                const youtubeSelectors = [
                    '#movie_player video',
                    '.html5-video-player video',
                    '.video-stream',
                    'video[src*="youtube"]',
                    'video[src*="googlevideo"]'
                ];

                for (const selector of youtubeSelectors) {
                    videoElement = document.querySelector(selector);
                    if (videoElement) {
                        console.log(`Stashy: Found video with selector: ${selector}`);
                        break;
                    }
                }
            }

            // Method 3: Any video with valid duration
            if (!videoElement) {
                for (const video of videos) {
                    try {
                        if (video.duration > 0 && video.offsetWidth > 0) {
                            videoElement = video;
                            console.log('Stashy: Found video with valid duration');
                            break;
                        }
                    } catch (e) {
                        console.warn('Stashy: Error checking video duration:', e);
                    }
                }
            }

            // Method 4: Any video as absolute last resort
            if (!videoElement && videos.length > 0) {
                videoElement = videos[0];
                console.log('Stashy: Using first available video as last resort');
            }

            if (videoElement) {
                console.log(`Stashy: Found video element, seeking to ${timeInSeconds} seconds`);
                videoElement.currentTime = timeInSeconds;

                // Show feedback
                const formattedTime = `${Math.floor(timeInSeconds / 60)}:${(timeInSeconds % 60).toString().padStart(2, '0')}`;

                const feedback = document.createElement('div');
                feedback.style.cssText = `
                    position: fixed; top: 20px; right: 20px; background: rgba(25, 118, 210, 0.9);
                    color: white; padding: 8px 12px; border-radius: 4px; font-size: 14px;
                    z-index: 10000; transition: opacity 0.3s ease;
                `;
                feedback.textContent = `⏰ Jumped to ${formattedTime}`;
                document.body.appendChild(feedback);

                setTimeout(() => {
                    feedback.style.opacity = '0';
                    setTimeout(() => feedback.remove(), 300);
                }, 2000);

                // Focus the video element for keyboard controls
                if (videoElement.focus) {
                    videoElement.focus();
                }

                console.log(`Stashy: Successfully seeked to ${timeInSeconds} seconds`);
            } else {
                console.warn('Stashy: Could not find video element for seeking');
                console.log('Available videos:', document.querySelectorAll('video').length);
                showTranscriptMessage('Could not find video player to seek to timestamp', 'error');
            }
        } catch (error) {
            console.error('Stashy: Error seeking to timestamp:', error);
            showTranscriptMessage('Error seeking to timestamp: ' + error.message, 'error');
        }
    }



    /**
     * Manually inserts transcript into notes as fallback
     * @param {string} formattedTranscript - HTML formatted transcript
     */
    function insertTranscriptManually(formattedTranscript) {
        console.log('Stashy: Starting manual transcript insertion');

        // Try multiple selectors for the note text area
        const noteSelectors = [
            '#Stashy-text',
            '.stashy-note-content',
            '[contenteditable="true"]',
            'div[contenteditable]',
            '.note-content',
            '.editor-content'
        ];

        let noteText = null;
        for (const selector of noteSelectors) {
            noteText = document.querySelector(selector);
            if (noteText) {
                console.log(`Stashy: Found note area with selector: ${selector}`);
                break;
            }
        }

        if (!noteText) {
            console.error('Stashy: Could not find note text area');
            // Try to find any contenteditable element
            const editableElements = document.querySelectorAll('[contenteditable="true"], [contenteditable]');
            if (editableElements.length > 0) {
                noteText = editableElements[0];
                console.log('Stashy: Using first contenteditable element found');
            } else {
                throw new Error('Could not find note text area - no contenteditable elements found');
            }
        }

        try {
            console.log('Stashy: Focusing note area and inserting content');
            noteText.focus();

            // Simple and reliable insertion method
            const currentContent = noteText.innerHTML || '';
            const separator = '<hr style="margin: 20px 0; border: none; border-top: 2px solid #1976d2; opacity: 0.3;">';
            const newContent = currentContent + separator + formattedTranscript;

            noteText.innerHTML = newContent;

            console.log('Stashy: Content inserted successfully');

            // Scroll to the bottom to show the new content
            noteText.scrollTop = noteText.scrollHeight;

            // Try to save if function is available
            if (typeof window.scheduleSave === 'function') {
                console.log('Stashy: Calling scheduleSave');
                window.scheduleSave();
            } else if (typeof scheduleSave === 'function') {
                console.log('Stashy: Calling global scheduleSave');
                scheduleSave();
            } else {
                console.log('Stashy: No save function available');
            }

            // Trigger input event to notify of changes
            const inputEvent = new Event('input', { bubbles: true });
            noteText.dispatchEvent(inputEvent);

            console.log('Stashy: Manual insertion completed successfully');

        } catch (error) {
            console.error('Stashy: Error in manual insertion:', error);
            throw new Error('Failed to insert transcript into notes: ' + error.message);
        }
    }

    /**
     * Escapes HTML characters in text
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    function escapeHtml(text) {
        if (!text) return '';

        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Shows a transcript processing message
     * @param {string} message - Message to show
     * @param {string} type - Message type (info, success, warning, error)
     */
    function showTranscriptMessage(message, type = 'info') {
        // Try to use the global showAiMessage function first
        if (typeof window.showAiMessage === 'function') {
            window.showAiMessage(message, type);
            return;
        }

        // Try the global showStatus function
        if (typeof window.showStatus === 'function') {
            window.showStatus(message, type);
            return;
        }

        // Fallback: show message in console and create notification
        const prefix = type.toUpperCase();
        console.log(`Stashy Transcript ${prefix}: ${message}`);

        try {
            // Create a temporary notification element
            const notification = document.createElement('div');
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'error' ? '#F44336' : type === 'success' ? '#4CAF50' : '#2196F3'};
                color: white;
                padding: 12px 16px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 10002;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                max-width: 300px;
            `;

            document.body.appendChild(notification);

            // Auto-remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        } catch (error) {
            console.warn('Could not show transcript notification:', error);
        }
    }

    /**
     * Extracts basic video information as fallback
     * @returns {Object|null} Basic video information
     */
    function extractBasicVideoInfo() {
        console.log('Stashy: Extracting basic video info as fallback');

        try {
            // Find video element
            const videoElement = findVideoElement();
            if (!videoElement) {
                return null;
            }

            // Get basic page information
            const title = document.title || 'Video';
            const url = window.location.href;

            // Determine platform
            let platform = 'Unknown';
            let channel = null;

            if (url.includes('youtube.com')) {
                platform = 'YouTube';
                // Try to get channel name
                const channelSelectors = [
                    '#channel-name .ytd-channel-name a',
                    '#owner-text a',
                    '.ytd-channel-name a'
                ];
                for (const selector of channelSelectors) {
                    const channelElement = document.querySelector(selector);
                    if (channelElement) {
                        channel = channelElement.textContent?.trim();
                        break;
                    }
                }
            } else if (url.includes('vimeo.com')) {
                platform = 'Vimeo';
            }

            return {
                title: title,
                channel: channel,
                description: null,
                url: url,
                platform: platform,
                videoElement: videoElement,
                currentTime: videoElement.currentTime || 0,
                duration: videoElement.duration || 0
            };
        } catch (error) {
            console.error('Stashy: Error extracting basic video info:', error);
            return null;
        }
    }

    /**
     * Finds video element on the page
     * @returns {HTMLVideoElement|null} Video element or null
     */
    function findVideoElement() {
        // Try to use global function first
        if (typeof window.findCurrentlyPlayingVideo === 'function') {
            return window.findCurrentlyPlayingVideo();
        }

        // Fallback: basic video detection
        const videoElements = document.querySelectorAll('video');

        // Look for the main video (largest, playing, or with controls)
        let mainVideo = null;
        let maxArea = 0;

        for (const video of videoElements) {
            // Skip hidden videos
            if (video.offsetWidth === 0 || video.offsetHeight === 0) continue;

            const area = video.offsetWidth * video.offsetHeight;

            // Prefer playing videos
            if (!video.paused) {
                return video;
            }

            // Otherwise, pick the largest video
            if (area > maxArea) {
                maxArea = area;
                mainVideo = video;
            }
        }

        return mainVideo;
    }

    /**
     * Generates mock transcript data for testing
     * @returns {Array} Mock transcript segments
     */
    function generateMockTranscriptData() {
        return [
            {
                timestamp: '0:15',
                text: 'Welcome to this comprehensive tutorial on machine learning and artificial intelligence.',
                startTime: 15,
                index: 0
            },
            {
                timestamp: '0:45',
                text: 'First, let\'s understand what neural networks are and how they process information.',
                startTime: 45,
                index: 1
            },
            {
                timestamp: '1:20',
                text: 'The key components include input layers, hidden layers, and output layers that work together.',
                startTime: 80,
                index: 2
            },
            {
                timestamp: '2:05',
                text: 'Each layer processes information and passes it to the next layer in the network.',
                startTime: 125,
                index: 3
            },
            {
                timestamp: '2:45',
                text: 'Training involves adjusting weights and biases to minimize prediction errors.',
                startTime: 165,
                index: 4
            },
            {
                timestamp: '3:30',
                text: 'Common applications include image recognition, natural language processing, and recommendation systems.',
                startTime: 210,
                index: 5
            }
        ];
    }







    // Make functions available globally
    window.extractVideoTranscript = extractVideoTranscript;
    window.getStoredTranscriptData = getStoredTranscriptData;
    window.storeTranscriptDataGlobally = storeTranscriptDataGlobally;

    console.log('Stashy: Video transcript module loaded');

})();
