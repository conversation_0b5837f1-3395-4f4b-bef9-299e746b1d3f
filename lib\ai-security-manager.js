/**
 * AI Security Manager
 * Provides comprehensive security features for AI API usage
 */

(function() {
    'use strict';

    // Security configuration
    const SECURITY_CONFIG = {
        // Rate limiting
        maxRequestsPerMinute: 15,
        maxRequestsPerHour: 200,
        maxRequestsPerDay: 1000,
        
        // Request size limits
        maxPromptLength: 30000,
        maxTokensPerRequest: 10192, // Increased by 2000
        
        // Retry configuration
        maxRetries: 3,
        baseRetryDelay: 1000,
        maxRetryDelay: 30000,
        
        // Security headers
        requiredHeaders: {
            'User-Agent': 'Stashy-Extension/1.0',
            'X-Requested-With': 'XMLHttpRequest'
        },
        
        // Audit logging
        enableAuditLog: true,
        maxAuditEntries: 1000
    };

    // Rate limiting state
    let requestCounts = {
        minute: { count: 0, resetTime: 0 },
        hour: { count: 0, resetTime: 0 },
        day: { count: 0, resetTime: 0 }
    };

    // Audit log
    let auditLog = [];

    /**
     * Checks if a request is within rate limits
     * @returns {Object} Rate limit status
     */
    function checkRateLimit() {
        const now = Date.now();
        
        // Reset counters if time windows have passed
        if (now > requestCounts.minute.resetTime) {
            requestCounts.minute = { count: 0, resetTime: now + 60000 };
        }
        if (now > requestCounts.hour.resetTime) {
            requestCounts.hour = { count: 0, resetTime: now + 3600000 };
        }
        if (now > requestCounts.day.resetTime) {
            requestCounts.day = { count: 0, resetTime: now + 86400000 };
        }

        // Check limits
        if (requestCounts.minute.count >= SECURITY_CONFIG.maxRequestsPerMinute) {
            return {
                allowed: false,
                reason: 'Rate limit exceeded: too many requests per minute',
                resetTime: requestCounts.minute.resetTime
            };
        }
        
        if (requestCounts.hour.count >= SECURITY_CONFIG.maxRequestsPerHour) {
            return {
                allowed: false,
                reason: 'Rate limit exceeded: too many requests per hour',
                resetTime: requestCounts.hour.resetTime
            };
        }
        
        if (requestCounts.day.count >= SECURITY_CONFIG.maxRequestsPerDay) {
            return {
                allowed: false,
                reason: 'Rate limit exceeded: too many requests per day',
                resetTime: requestCounts.day.resetTime
            };
        }

        return { allowed: true };
    }

    /**
     * Records a request for rate limiting
     */
    function recordRequest() {
        requestCounts.minute.count++;
        requestCounts.hour.count++;
        requestCounts.day.count++;
    }

    /**
     * Validates request parameters
     * @param {Object} request - Request parameters
     * @returns {Object} Validation result
     */
    function validateRequest(request) {
        const errors = [];

        // Validate prompt length
        if (request.prompt && request.prompt.length > SECURITY_CONFIG.maxPromptLength) {
            errors.push(`Prompt too long: ${request.prompt.length} chars (max: ${SECURITY_CONFIG.maxPromptLength})`);
        }

        // Validate token count
        if (request.maxTokens && request.maxTokens > SECURITY_CONFIG.maxTokensPerRequest) {
            errors.push(`Token count too high: ${request.maxTokens} (max: ${SECURITY_CONFIG.maxTokensPerRequest})`);
        }

        // Validate required fields
        if (!request.prompt || typeof request.prompt !== 'string') {
            errors.push('Invalid or missing prompt');
        }

        return {
            valid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * Sanitizes user input to prevent injection attacks
     * @param {string} input - User input to sanitize
     * @returns {string} Sanitized input
     */
    function sanitizeInput(input) {
        if (typeof input !== 'string') {
            return '';
        }

        return input
            // Remove potential script injections
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '')
            
            // Remove potential prompt injections
            .replace(/\b(ignore|forget|disregard)\s+(previous|above|all)\s+(instructions?|prompts?|rules?)/gi, '[FILTERED]')
            .replace(/\b(act|behave|pretend)\s+as\s+(if\s+)?(you\s+are\s+)?(?:a\s+)?(?:different|another|new)/gi, '[FILTERED]')
            
            // Limit excessive repetition
            .replace(/(.)\1{50,}/g, '$1'.repeat(10) + '[TRUNCATED]')
            
            // Trim and normalize whitespace
            .trim()
            .replace(/\s+/g, ' ');
    }

    /**
     * Implements exponential backoff for retries
     * @param {number} attempt - Current attempt number (0-based)
     * @returns {number} Delay in milliseconds
     */
    function calculateRetryDelay(attempt) {
        const delay = SECURITY_CONFIG.baseRetryDelay * Math.pow(2, attempt);
        return Math.min(delay, SECURITY_CONFIG.maxRetryDelay);
    }

    /**
     * Logs security events for audit purposes
     * @param {string} event - Event type
     * @param {Object} details - Event details
     */
    function logSecurityEvent(event, details) {
        if (!SECURITY_CONFIG.enableAuditLog) return;

        const logEntry = {
            timestamp: new Date().toISOString(),
            event: event,
            details: details,
            userAgent: navigator.userAgent,
            url: window.location?.href || 'unknown'
        };

        auditLog.push(logEntry);

        // Limit log size
        if (auditLog.length > SECURITY_CONFIG.maxAuditEntries) {
            auditLog = auditLog.slice(-SECURITY_CONFIG.maxAuditEntries);
        }

        console.log('AI Security: Audit event logged:', event, details);
    }

    /**
     * Handles API errors with user-friendly messages
     * @param {Error} error - The error to handle
     * @param {Object} context - Error context
     * @returns {Object} Formatted error response
     */
    function handleApiError(error, context = {}) {
        let userMessage = 'An unexpected error occurred. Please try again.';
        let shouldRetry = false;
        let retryAfter = 0;

        // Log the error for audit
        logSecurityEvent('api_error', {
            error: error.message,
            context: context,
            stack: error.stack
        });

        // Categorize errors
        if (error.message.includes('rate limit') || error.message.includes('429')) {
            userMessage = 'Rate limit exceeded. Please wait a moment before trying again.';
            shouldRetry = true;
            retryAfter = 60000; // 1 minute
        } else if (error.message.includes('401') || error.message.includes('unauthorized')) {
            userMessage = 'Invalid API key. Please check your API key in settings.';
            shouldRetry = false;
        } else if (error.message.includes('403') || error.message.includes('forbidden')) {
            userMessage = 'Access denied. Please check your API key permissions.';
            shouldRetry = false;
        } else if (error.message.includes('404')) {
            userMessage = 'Service not found. Please try again later.';
            shouldRetry = true;
            retryAfter = 5000;
        } else if (error.message.includes('500') || error.message.includes('502') || error.message.includes('503')) {
            userMessage = 'Service temporarily unavailable. Please try again later.';
            shouldRetry = true;
            retryAfter = 10000;
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
            userMessage = 'Network error. Please check your internet connection.';
            shouldRetry = true;
            retryAfter = 5000;
        } else if (error.message.includes('timeout')) {
            userMessage = 'Request timed out. Please try again.';
            shouldRetry = true;
            retryAfter = 3000;
        }

        return {
            success: false,
            error: userMessage,
            technicalError: error.message,
            shouldRetry: shouldRetry,
            retryAfter: retryAfter,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Gets current rate limit status
     * @returns {Object} Rate limit information
     */
    function getRateLimitStatus() {
        const now = Date.now();
        return {
            minute: {
                used: requestCounts.minute.count,
                limit: SECURITY_CONFIG.maxRequestsPerMinute,
                resetTime: requestCounts.minute.resetTime,
                remaining: Math.max(0, SECURITY_CONFIG.maxRequestsPerMinute - requestCounts.minute.count)
            },
            hour: {
                used: requestCounts.hour.count,
                limit: SECURITY_CONFIG.maxRequestsPerHour,
                resetTime: requestCounts.hour.resetTime,
                remaining: Math.max(0, SECURITY_CONFIG.maxRequestsPerHour - requestCounts.hour.count)
            },
            day: {
                used: requestCounts.day.count,
                limit: SECURITY_CONFIG.maxRequestsPerDay,
                resetTime: requestCounts.day.resetTime,
                remaining: Math.max(0, SECURITY_CONFIG.maxRequestsPerDay - requestCounts.day.count)
            }
        };
    }

    /**
     * Gets audit log entries
     * @param {number} limit - Maximum number of entries to return
     * @returns {Array} Audit log entries
     */
    function getAuditLog(limit = 100) {
        return auditLog.slice(-limit);
    }

    /**
     * Clears the audit log
     */
    function clearAuditLog() {
        auditLog = [];
        logSecurityEvent('audit_log_cleared', { by: 'user_request' });
    }

    // Export the AI security manager interface
    window.aiSecurityManager = {
        checkRateLimit,
        recordRequest,
        validateRequest,
        sanitizeInput,
        calculateRetryDelay,
        handleApiError,
        getRateLimitStatus,
        getAuditLog,
        clearAuditLog,
        logSecurityEvent
    };

    console.log('AI Security Manager: Module loaded with comprehensive security features');

})();
