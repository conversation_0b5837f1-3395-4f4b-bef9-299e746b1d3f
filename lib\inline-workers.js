/**
 * Stashy Inline Worker Scripts
 * SECURITY ENHANCED: This file contains secure inline worker scripts
 * All workers use only local resources and no external dependencies
 */

// Define the secure inline worker scripts
window.StashyInlineWorkers = {
    // Storage worker script
    'storage-worker': `
        /**
         * Stashy Storage Worker
         * SECURITY ENHANCED: Handles background storage operations to prevent UI blocking
         * Uses only local resources and no external dependencies
         */

        // SECURITY: Set up secure worker context
        self.isWorkerContext = true;

        // Send ready status to main thread
        self.postMessage({ status: 'ready' });

        // Set up message handler
        self.onmessage = function(event) {
            const { taskId, action, data } = event.data;

            try {
                // Process the task based on the action
                switch (action) {
                    case 'compressData':
                        handleCompressData(taskId, data);
                        break;

                    case 'decompressData':
                        handleDecompressData(taskId, data);
                        break;

                    case 'encryptData':
                        handleEncryptData(taskId, data);
                        break;

                    case 'decryptData':
                        handleDecryptData(taskId, data);
                        break;

                    case 'processNoteData':
                        handleProcessNoteData(taskId, data);
                        break;

                    case 'processHighlightData':
                        handleProcessHighlightData(taskId, data);
                        break;

                    case 'batchProcess':
                        handleBatchProcess(taskId, data);
                        break;

                    default:
                        throw new Error(\`Unknown action: \${action}\`);
                }
            } catch (error) {
                // Send error back to main thread
                self.postMessage({
                    taskId,
                    error: error.message || 'Unknown error in storage worker'
                });
            }
        };

        /**
         * Processes note data (validation, normalization, etc.)
         * @param {string} taskId - The ID of the task
         * @param {Object} data - The note data to process
         */
        function handleProcessNoteData(taskId, data) {
            try {
                const { noteData, options } = data;

                // Clone the note data to avoid modifying the original
                const processedData = JSON.parse(JSON.stringify(noteData));

                // Add timestamp if not present
                if (!processedData.timestamp) {
                    processedData.timestamp = Date.now();
                }

                // Update last modified timestamp
                processedData.lastModified = Date.now();

                // Generate a word count if text is present
                if (processedData.text) {
                    processedData.wordCount = processedData.text.split(/\\s+/).filter(Boolean).length;
                }

                // Simulate processing work
                for (let i = 0; i < 10000; i++) {
                    // Minimal busy work to simulate CPU-intensive task
                    Math.sqrt(i);
                }

                // Send result back to main thread
                self.postMessage({
                    taskId,
                    result: {
                        processedData,
                        success: true
                    }
                });
            } catch (error) {
                self.postMessage({
                    taskId,
                    error: \`Note processing error: \${error.message}\`
                });
            }
        }

        /**
         * Processes highlight data
         * @param {string} taskId - The ID of the task
         * @param {Object} data - The highlight data to process
         */
        function handleProcessHighlightData(taskId, data) {
            try {
                const { highlightData, options } = data;

                // Clone the highlight data to avoid modifying the original
                const processedData = JSON.parse(JSON.stringify(highlightData));

                // Add timestamp if not present
                if (!processedData.timestamp) {
                    processedData.timestamp = Date.now();
                }

                // Update last modified timestamp
                processedData.lastModified = Date.now();

                // Send result back to main thread
                self.postMessage({
                    taskId,
                    result: {
                        processedData,
                        success: true
                    }
                });
            } catch (error) {
                self.postMessage({
                    taskId,
                    error: \`Highlight processing error: \${error.message}\`
                });
            }
        }

        // Stub implementations for other methods
        function handleCompressData(taskId, data) {
            self.postMessage({
                taskId,
                result: { compressed: data.input, success: true }
            });
        }

        function handleDecompressData(taskId, data) {
            self.postMessage({
                taskId,
                result: { decompressed: data.input, success: true }
            });
        }

        function handleEncryptData(taskId, data) {
            self.postMessage({
                taskId,
                result: { encrypted: data.input, success: true }
            });
        }

        function handleDecryptData(taskId, data) {
            self.postMessage({
                taskId,
                result: { decrypted: data.input, success: true }
            });
        }

        function handleBatchProcess(taskId, data) {
            self.postMessage({
                taskId,
                result: { results: data.items, count: data.items.length, success: true }
            });
        }
    `,

    // Image worker script
    'image-worker': `
        /**
         * Stashy Image Processing Worker
         * Handles background image processing operations to prevent UI blocking
         */

        // Set up worker context
        self.isWorkerContext = true;

        // Send ready status to main thread
        self.postMessage({ status: 'ready' });

        // Set up message handler
        self.onmessage = function(event) {
            const { taskId, action, data } = event.data;

            try {
                // Process the task based on the action
                switch (action) {
                    case 'processImage':
                        // Just return the original image data for the inline version
                        self.postMessage({
                            taskId,
                            result: {
                                processedDataUrl: data.imageDataUrl,
                                success: true
                            }
                        });
                        break;

                    case 'resizeImage':
                        // Just return the original image data for the inline version
                        self.postMessage({
                            taskId,
                            result: {
                                resizedDataUrl: data.imageDataUrl,
                                width: data.width || 100,
                                height: data.height || 100,
                                success: true
                            }
                        });
                        break;

                    case 'convertFormat':
                        // Just return the original image data for the inline version
                        self.postMessage({
                            taskId,
                            result: {
                                convertedDataUrl: data.imageDataUrl,
                                format: data.format || 'image/png',
                                success: true
                            }
                        });
                        break;

                    default:
                        throw new Error(\`Unknown action: \${action}\`);
                }
            } catch (error) {
                // Send error back to main thread
                self.postMessage({
                    taskId,
                    error: error.message || 'Unknown error in image worker'
                });
            }
        };
    `,

    // Data worker script
    'data-worker': `
        /**
         * Stashy Data Processing Worker
         * Handles background data processing operations to prevent UI blocking
         */

        // Set up worker context
        self.isWorkerContext = true;

        // Send ready status to main thread
        self.postMessage({ status: 'ready' });

        // Set up message handler
        self.onmessage = function(event) {
            const { taskId, action, data } = event.data;

            try {
                // Process the task based on the action
                switch (action) {
                    case 'searchNotes':
                        // Simple implementation for inline worker
                        const results = data.notes.filter(note =>
                            note.text && note.text.includes(data.query)
                        );

                        self.postMessage({
                            taskId,
                            result: {
                                results,
                                count: results.length,
                                query: data.query
                            }
                        });
                        break;

                    default:
                        throw new Error(\`Unknown action: \${action}\`);
                }
            } catch (error) {
                // Send error back to main thread
                self.postMessage({
                    taskId,
                    error: error.message || 'Unknown error in data worker'
                });
            }
        };
    `,

    // Image worker script
    'image-worker': `
        /**
         * Stashy Image Worker
         * Handles image processing operations including thumbnail generation
         */

        // Set up worker context
        self.isWorkerContext = true;

        // Send ready status to main thread
        self.postMessage({ status: 'ready' });

        // Helper function to load an image
        function loadImage(dataUrl) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => resolve(img);
                img.onerror = () => reject(new Error('Failed to load image'));
                img.src = dataUrl;
            });
        }

        // Helper function to create a canvas
        function createCanvas(width, height) {
            const canvas = new OffscreenCanvas(width, height);
            const ctx = canvas.getContext('2d', { willReadFrequently: true });
            return { canvas, ctx };
        }

        // Helper function to convert canvas to data URL
        async function canvasToDataUrl(canvas, format, quality) {
            const blob = await canvas.convertToBlob({
                type: format || 'image/png',
                quality: quality || 0.92
            });
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = () => reject(new Error('Failed to convert canvas to data URL'));
                reader.readAsDataURL(blob);
            });
        }

        // Handle thumbnail generation
        async function handleGenerateThumbnail(taskId, data) {
            try {
                const { imageDataUrl, maxWidth, maxHeight, quality, format } = data;

                // Load the original image
                const image = await loadImage(imageDataUrl);

                // Calculate thumbnail dimensions while maintaining aspect ratio
                let thumbnailWidth = image.width;
                let thumbnailHeight = image.height;

                if (thumbnailWidth > maxWidth || thumbnailHeight > maxHeight) {
                    const aspectRatio = thumbnailWidth / thumbnailHeight;

                    if (thumbnailWidth > thumbnailHeight) {
                        thumbnailWidth = maxWidth;
                        thumbnailHeight = maxWidth / aspectRatio;
                    } else {
                        thumbnailHeight = maxHeight;
                        thumbnailWidth = maxHeight * aspectRatio;
                    }

                    // Ensure we don't exceed either dimension
                    if (thumbnailWidth > maxWidth) {
                        thumbnailWidth = maxWidth;
                        thumbnailHeight = maxWidth / aspectRatio;
                    }
                    if (thumbnailHeight > maxHeight) {
                        thumbnailHeight = maxHeight;
                        thumbnailWidth = maxHeight * aspectRatio;
                    }
                }

                // Create a canvas for the thumbnail
                const { canvas, ctx } = createCanvas(Math.round(thumbnailWidth), Math.round(thumbnailHeight));

                // Enable high-quality scaling
                ctx.imageSmoothingEnabled = true;
                ctx.imageSmoothingQuality = 'high';

                // Draw the resized image
                ctx.drawImage(image, 0, 0, Math.round(thumbnailWidth), Math.round(thumbnailHeight));

                // Convert to the specified format with compression
                const thumbnailDataUrl = await canvasToDataUrl(
                    canvas,
                    format || 'image/jpeg',
                    quality || 0.85
                );

                // Send the result back to the main thread
                self.postMessage({
                    taskId,
                    result: {
                        thumbnailDataUrl,
                        originalWidth: image.width,
                        originalHeight: image.height,
                        thumbnailWidth: Math.round(thumbnailWidth),
                        thumbnailHeight: Math.round(thumbnailHeight),
                        success: true
                    }
                });
            } catch (error) {
                self.postMessage({
                    taskId,
                    error: \`Thumbnail generation error: \${error.message}\`
                });
            }
        }

        // Set up message handler
        self.onmessage = function(event) {
            const { taskId, action, data } = event.data;

            try {
                // Process the task based on the action
                switch (action) {
                    case 'generateThumbnail':
                        handleGenerateThumbnail(taskId, data);
                        break;

                    default:
                        throw new Error(\`Unknown action: \${action}\`);
                }
            } catch (error) {
                // Send error back to main thread
                self.postMessage({
                    taskId,
                    error: error.message || 'Unknown error in image worker'
                });
            }
        };
    `
};

console.log("Stashy: Inline Workers Loaded");
