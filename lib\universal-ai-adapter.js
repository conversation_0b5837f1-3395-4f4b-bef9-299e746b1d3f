/**
 * Universal AI Adapter System
 * Provides a common interface for multiple AI providers
 */

(function() {
    'use strict';

    // Current provider configuration
    let currentProvider = null;
    let currentApiKey = null;
    let providerConfig = null;

    // Rate limiting to prevent quota exhaustion
    let lastApiCall = 0;
    const MIN_CALL_INTERVAL = 2000; // 2 seconds between calls

    /**
     * Provider-specific adapters
     */
    const PROVIDER_ADAPTERS = {
        openai: {
            async generateText(prompt, options = {}) {
                const userModel = getUserPreferredModel();
                const {
                    maxTokens = 3000, // Increased by 2000 for better responses
                    temperature = 0.7,
                    model = options.model || userModel || providerConfig.defaultModel || 'gpt-3.5-turbo'
                } = options;

                const requestBody = {
                    model,
                    messages: [{ role: 'user', content: prompt }],
                    max_tokens: Math.min(maxTokens, 4000), // Increased cap for better responses
                    temperature
                };

                return await makeApiRequest('https://api.openai.com/v1/chat/completions', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bear<PERSON> ${currentApiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
            },

            parseResponse(response) {
                if (response.choices && response.choices[0] && response.choices[0].message) {
                    return response.choices[0].message.content;
                }
                throw new Error('Invalid OpenAI response format');
            }
        },

        anthropic: {
            async generateText(prompt, options = {}) {
                const userModel = getUserPreferredModel();
                const {
                    maxTokens = 3000, // Increased by 2000 for better responses
                    temperature = 0.7,
                    model = options.model || userModel || providerConfig.defaultModel || 'claude-3-haiku-20240307'
                } = options;

                const requestBody = {
                    model,
                    max_tokens: Math.min(maxTokens, 4000), // Increased cap for better responses
                    temperature,
                    messages: [{ role: 'user', content: prompt }]
                };

                return await makeApiRequest('https://api.anthropic.com/v1/messages', {
                    method: 'POST',
                    headers: {
                        'x-api-key': currentApiKey,
                        'Content-Type': 'application/json',
                        'anthropic-version': '2023-06-01'
                    },
                    body: JSON.stringify(requestBody)
                });
            },

            parseResponse(response) {
                if (response.content && response.content[0] && response.content[0].text) {
                    return response.content[0].text;
                }
                throw new Error('Invalid Anthropic response format');
            }
        },

        google: {
            async generateText(prompt, options = {}) {
                // Get user's preferred model from saved configuration
                const userModel = getUserPreferredModel();
                const {
                    maxTokens = 8000, // Massively increased for Deep Research and complex features
                    temperature = 0.7,
                    model = options.model || userModel || providerConfig.defaultModel || 'gemini-2.5-flash'
                } = options;

                const requestBody = {
                    contents: [{
                        parts: [{ text: prompt }]
                    }],
                    generationConfig: {
                        maxOutputTokens: Math.min(maxTokens, 12000), // Massively increased for Deep Research
                        temperature
                    }
                };

                const url = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${currentApiKey}`;

                console.log(`Universal AI Adapter: Using Google AI model: ${model}`);
                console.log(`Universal AI Adapter: Request body:`, JSON.stringify(requestBody, null, 2));

                return await makeApiRequest(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
            },

            parseResponse(response) {
                console.log('Universal AI Adapter: Parsing Google AI response:', JSON.stringify(response, null, 2));

                // Enhanced response validation and error handling
                if (!response) {
                    throw new Error('Empty response from Google AI service');
                }

                if (response.error) {
                    throw new Error(`Google AI API Error: ${response.error.message || 'Unknown error'}`);
                }

                if (!response.candidates || !Array.isArray(response.candidates) || response.candidates.length === 0) {
                    console.error('Universal AI Adapter: No candidates in response. Full response:', response);
                    throw new Error('No candidates in Google AI response');
                }

                const candidate = response.candidates[0];
                console.log('Universal AI Adapter: First candidate:', JSON.stringify(candidate, null, 2));

                // Check for safety filtering
                if (candidate.finishReason === 'SAFETY') {
                    throw new Error('Content was filtered due to safety concerns. Please try rephrasing your request.');
                }

                if (candidate.finishReason === 'RECITATION') {
                    throw new Error('Content was filtered due to recitation concerns. Please try rephrasing your request.');
                }

                // Handle different response formats for Gemini 2.5 vs older models
                if (!candidate.content) {
                    console.error('Universal AI Adapter: No content field in candidate:', candidate);
                    throw new Error('No content field in Google AI response candidate');
                }

                if (!candidate.content.parts) {
                    console.warn('Universal AI Adapter: No parts field in candidate content, checking for alternative formats...');

                    // Check if this is a Gemini 2.5 response with different format
                    if (candidate.content.role === 'model' && candidate.finishReason === 'MAX_TOKENS') {
                        console.error('Universal AI Adapter: Gemini 2.5 response hit token limit without generating content');
                        throw new Error('AI response was truncated due to token limit. Please try with shorter content or increase token limit.');
                    }

                    // Check if content is directly in the candidate
                    if (candidate.text) {
                        console.log('Universal AI Adapter: Found text directly in candidate');
                        return candidate.text.trim();
                    }

                    console.error('Universal AI Adapter: No parts field in candidate content:', candidate.content);
                    throw new Error('No parts field in Google AI response candidate content');
                }

                if (candidate.content.parts.length === 0) {
                    console.error('Universal AI Adapter: Empty parts array in candidate content:', candidate.content);
                    throw new Error('Empty parts array in Google AI response candidate content');
                }

                const firstPart = candidate.content.parts[0];
                if (!firstPart.text) {
                    console.error('Universal AI Adapter: No text in first part:', firstPart);
                    throw new Error('No text content in Google AI response part');
                }

                console.log('Universal AI Adapter: Successfully extracted text from Google AI response');
                return firstPart.text.trim();
            }
        },

        cohere: {
            async generateText(prompt, options = {}) {
                const {
                    maxTokens = 500, // Reduced for free tier compatibility
                    temperature = 0.7,
                    model = providerConfig.defaultModel || 'command'
                } = options;

                const requestBody = {
                    model,
                    prompt,
                    max_tokens: Math.min(maxTokens, 500), // Cap at 500 tokens for free tier
                    temperature
                };

                return await makeApiRequest('https://api.cohere.ai/v1/generate', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentApiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
            },

            parseResponse(response) {
                if (response.generations && response.generations[0] && response.generations[0].text) {
                    return response.generations[0].text;
                }
                throw new Error('Invalid Cohere response format');
            }
        },

        huggingface: {
            async generateText(prompt, options = {}) {
                const {
                    maxTokens = 2200, // Increased by 2000 for better responses
                    temperature = 0.7,
                    model = providerConfig.defaultModel || 'microsoft/DialoGPT-large'
                } = options;

                const requestBody = {
                    inputs: prompt,
                    parameters: {
                        max_new_tokens: Math.min(maxTokens, 2200), // Increased cap for better responses
                        temperature,
                        return_full_text: false
                    }
                };

                return await makeApiRequest(`https://api-inference.huggingface.co/models/${model}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${currentApiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
            },

            parseResponse(response) {
                if (Array.isArray(response) && response[0] && response[0].generated_text) {
                    return response[0].generated_text;
                }
                throw new Error('Invalid Hugging Face response format');
            }
        }
    };

    /**
     * Makes an API request through the background script
     * @param {string} url - The API endpoint URL
     * @param {Object} options - Request options
     * @returns {Promise<Object>} API response
     */
    async function makeApiRequest(url, options) {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'universalAiRequest',
                url,
                options
            });

            if (response && response.success) {
                return response.data;
            } else {
                throw new Error(response?.error || 'API request failed');
            }
        } catch (error) {
            console.error('Universal AI Adapter: API request failed:', error);
            throw error;
        }
    }

    /**
     * Gets user's preferred model from saved configuration
     * @returns {string|null} User's preferred model or null
     */
    function getUserPreferredModel() {
        if (providerConfig && providerConfig.userPreferences && providerConfig.userPreferences.model) {
            console.log(`Universal AI Adapter: Using user's preferred model: ${providerConfig.userPreferences.model}`);
            return providerConfig.userPreferences.model;
        }
        return null;
    }

    /**
     * Enhanced content extraction using Readability.js
     * @param {string} url - Optional URL for context
     * @returns {Object} Extracted content with metadata
     */
    function extractPageContent(url = null) {
        console.log('Universal AI Adapter: Starting enhanced content extraction...');

        // Use Readability.js if available
        if (typeof window.extractPageContentWithReadability === 'function') {
            try {
                const result = window.extractPageContentWithReadability();
                console.log(`Universal AI Adapter: Readability.js extraction successful - ${result.length} characters`);

                return {
                    content: result.content,
                    title: result.title || document.title || 'Untitled Page',
                    url: url || window.location.href,
                    siteName: result.siteName || window.location.hostname,
                    byline: result.byline || '',
                    excerpt: result.excerpt || '',
                    length: result.length,
                    quality: result.quality,
                    extractionMethod: result.extractionMethod,
                    success: result.success
                };
            } catch (error) {
                console.warn('Universal AI Adapter: Readability.js extraction failed, using fallback:', error.message);
            }
        }

        // Fallback to basic extraction
        console.log('Universal AI Adapter: Using fallback content extraction');
        const content = document.body ? document.body.innerText : '';

        return {
            content: content.trim(),
            title: document.title || 'Untitled Page',
            url: url || window.location.href,
            siteName: window.location.hostname,
            byline: '',
            excerpt: content.substring(0, 200) + '...',
            length: content.length,
            quality: { level: 'basic', score: 50, issues: ['fallback-extraction'] },
            extractionMethod: 'fallback',
            success: content.length > 0
        };
    }

    /**
     * Initializes the adapter with a specific provider
     * @param {string} providerId - The provider identifier
     * @param {string} apiKey - The API key
     * @param {Object} config - Provider configuration
     */
    async function initialize(providerId, apiKey, config) {
        if (!PROVIDER_ADAPTERS[providerId]) {
            throw new Error(`Unsupported provider: ${providerId}`);
        }

        currentProvider = providerId;
        currentApiKey = apiKey;
        providerConfig = config;

        console.log(`Universal AI Adapter: Initialized with ${config.displayName}`);

        // Log user preferences if available
        if (config.userPreferences && config.userPreferences.model) {
            console.log(`Universal AI Adapter: User preferred model: ${config.userPreferences.model}`);
        }
    }

    /**
     * Generates text using the current provider
     * @param {string} prompt - The text prompt
     * @param {Object} options - Generation options
     * @returns {Promise<string>} Generated text
     */
    async function generateText(prompt, options = {}) {
        if (!currentProvider || !currentApiKey) {
            throw new Error('AI adapter not initialized. Please configure an API key first.');
        }

        // Rate limiting to prevent quota exhaustion
        const now = Date.now();
        const timeSinceLastCall = now - lastApiCall;
        if (timeSinceLastCall < MIN_CALL_INTERVAL) {
            const waitTime = MIN_CALL_INTERVAL - timeSinceLastCall;
            console.log(`Universal AI Adapter: Rate limiting - waiting ${waitTime}ms before next call`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        lastApiCall = Date.now();

        const adapter = PROVIDER_ADAPTERS[currentProvider];

        try {
            console.log(`Universal AI Adapter: Generating text with ${providerConfig.displayName}...`);

            const response = await adapter.generateText(prompt, options);
            const text = adapter.parseResponse(response);

            console.log(`Universal AI Adapter: Text generation successful`);
            return text;

        } catch (error) {
            console.error(`Universal AI Adapter: Text generation failed:`, error);

            // Enhanced error handling for common OpenAI issues
            if (error.message.includes('429') || error.message.includes('quota') || error.message.includes('rate')) {
                throw new Error(`Rate limit or quota exceeded for ${providerConfig.displayName}. Please check your billing and usage limits, then wait before trying again.`);
            }

            if (error.message.includes('404') && error.message.includes('model')) {
                throw new Error(`Model not found or not accessible. Please check your OpenAI plan and model access permissions. Try using 'gpt-3.5-turbo' instead.`);
            }

            if (error.message.includes('insufficient_quota')) {
                throw new Error(`OpenAI quota exceeded. Please check your billing details at https://platform.openai.com/account/billing`);
            }

            throw new Error(`${providerConfig.displayName} API error: ${error.message}`);
        }
    }

    /**
     * Gets the current provider information
     * @returns {Object|null} Current provider info
     */
    function getCurrentProvider() {
        if (!currentProvider) return null;
        
        return {
            id: currentProvider,
            config: providerConfig,
            hasApiKey: !!currentApiKey
        };
    }

    /**
     * Checks if the adapter is ready to use
     * @returns {boolean} True if ready
     */
    function isReady() {
        return !!(currentProvider && currentApiKey && providerConfig);
    }

    /**
     * Gets available models for the current provider
     * @param {boolean} fetchFromApi - Whether to fetch models from API (default: false)
     * @returns {Promise<Array>|Array} Array of available models
     */
    function getAvailableModels(fetchFromApi = false) {
        if (!providerConfig) return [];

        // If not fetching from API, return static models
        if (!fetchFromApi) {
            return Object.keys(providerConfig.models);
        }

        // For Google AI, fetch models dynamically from API
        if (currentProvider === 'google' && currentApiKey) {
            return fetchGoogleModels();
        }

        // For other providers, return static models for now
        return Object.keys(providerConfig.models);
    }

    /**
     * Fetches available models from Google AI API
     * @returns {Promise<Array>} Array of available model IDs
     */
    async function fetchGoogleModels() {
        try {
            const response = await makeApiRequest('https://generativelanguage.googleapis.com/v1beta/models?key=' + currentApiKey, {
                method: 'GET'
            });

            if (response && response.models) {
                const geminiModels = response.models
                    .filter(model => model.name.includes('gemini'))
                    .map(model => {
                        // Extract model ID from full name (e.g., "models/gemini-1.5-flash" -> "gemini-1.5-flash")
                        const modelId = model.name.replace('models/', '');
                        return modelId;
                    });

                console.log('Universal AI Adapter: Fetched Google models from API:', geminiModels);
                return geminiModels;
            }
        } catch (error) {
            console.warn('Universal AI Adapter: Failed to fetch Google models from API:', error);
        }

        // Fallback to static models
        return Object.keys(providerConfig.models);
    }

    /**
     * Gets model information
     * @param {string} modelId - The model identifier
     * @returns {Object|null} Model information
     */
    function getModelInfo(modelId) {
        if (!providerConfig || !providerConfig.models[modelId]) return null;
        return providerConfig.models[modelId];
    }

    /**
     * Resets the adapter
     */
    function reset() {
        currentProvider = null;
        currentApiKey = null;
        providerConfig = null;
        console.log('Universal AI Adapter: Reset completed');
    }

    /**
     * Enhanced content processing with AI analysis
     * @param {string} prompt - The AI prompt
     * @param {Object} options - Generation options
     * @param {boolean} options.usePageContent - Whether to include page content
     * @param {string} options.contentPrompt - Custom prompt for content processing
     * @returns {Promise<string>} AI response
     */
    async function generateTextWithContent(prompt, options = {}) {
        const {
            usePageContent = false,
            contentPrompt = null,
            ...generateOptions
        } = options;

        if (!usePageContent) {
            return await generateText(prompt, generateOptions);
        }

        // Extract page content using Readability.js
        const pageData = extractPageContent();

        if (!pageData.success || !pageData.content.trim()) {
            console.warn('Universal AI Adapter: No meaningful content found, proceeding without page content');
            return await generateText(prompt, generateOptions);
        }

        // Create enhanced prompt with page content
        const enhancedPrompt = contentPrompt || `
Analyze the following webpage content and respond to the user's request:

**Page Information:**
- Title: ${pageData.title}
- URL: ${pageData.url}
- Site: ${pageData.siteName}
- Content Length: ${pageData.length} characters
- Extraction Quality: ${pageData.quality.level} (score: ${pageData.quality.score})

**Page Content:**
${pageData.content.substring(0, 8000)} ${pageData.content.length > 8000 ? '...' : ''}

**User Request:**
${prompt}

Please provide a comprehensive response based on the page content above.`;

        console.log(`Universal AI Adapter: Enhanced prompt created with ${pageData.length} characters of content`);

        return await generateText(enhancedPrompt, generateOptions);
    }

    // Public API
    window.universalAiAdapter = {
        initialize,
        generateText,
        generateTextWithContent,
        extractPageContent,
        getCurrentProvider,
        isReady,
        getAvailableModels,
        getModelInfo,
        reset,

        // Legacy compatibility methods for existing AI features
        init: initialize,
        isAvailable: isReady,
        summarizeText: (text, options) => generateText(`Please summarize the following text:\n\n${text}`, options),
        enhanceText: (text, options) => generateText(`Please enhance and improve the following text:\n\n${text}`, options),
        correctGrammarAndStyle: (text, options) => generateText(`Please correct grammar and improve style of the following text:\n\n${text}`, options)
    };

    console.log('Universal AI Adapter: Module loaded');

})();
