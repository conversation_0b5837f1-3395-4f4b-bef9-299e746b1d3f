/**
 * Stashy Data Compression
 * Provides compression and decompression for large data objects
 */

// Create a namespace to avoid global pollution
window.StashyCompression = (function() {
    // Compression algorithms
    const ALGORITHMS = {
        LZ_STRING: 'lz-string',     // LZ-based string compression
        PAKO: 'pako',               // DEFLATE/zlib compression
        SIMPLE: 'simple',           // Simple RLE compression
        NONE: 'none'                // No compression
    };

    // Configuration
    const config = {
        defaultAlgorithm: ALGORITHMS.LZ_STRING,  // Default compression algorithm
        compressionThreshold: 1024,              // Minimum size in bytes to compress
        useWorker: true,                         // Use web worker for compression
        compressMetadata: false,                 // Whether to compress metadata
        debug: false                             // Debug mode disabled
    };

    // Private variables
    let lzString = null;                         // LZ-String library
    let pako = null;                             // Pako library

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StashyCompression]', ...args);
        }
    }

    /**
     * Initializes the compression module
     * @returns {Promise<boolean>} A promise that resolves with true if initialization was successful
     */
    async function init() {
        try {
            // Try to load LZ-String if available
            if (window.LZString) {
                lzString = window.LZString;
                debugLog('LZ-String library loaded');
            } else {
                // SECURITY FIX: Removed external CDN loading
                // Use fallback compression instead of loading external scripts
                console.warn('Stashy: LZ-String library not available, using fallback compression');
            }

            // Try to load Pako if available (local only)
            if (window.pako) {
                pako = window.pako;
                debugLog('Pako library loaded from local file');
            } else {
                // SECURITY FIX: Only use local libraries, no external loading
                console.warn('Stashy: Pako library not available locally - ensure lib/pako.min.js is loaded');
            }

            return true;
        } catch (error) {
            console.error('Stashy: Error initializing compression module:', error);
            return false;
        }
    }

    /**
     * Compresses data using the specified algorithm
     * @param {any} data - The data to compress
     * @param {Object} options - Compression options
     * @returns {Promise<Object>} A promise that resolves with the compressed data
     */
    async function compress(data, options = {}) {
        const {
            algorithm = config.defaultAlgorithm,
            useWorker = config.useWorker,
            metadata = {}
        } = options;

        // Convert data to string if it's not already
        const dataString = typeof data === 'string' ? data : JSON.stringify(data);

        // Check if data is large enough to compress
        if (dataString.length < config.compressionThreshold) {
            debugLog(`Data too small to compress (${dataString.length} bytes)`);
            return {
                compressed: false,
                data: data,
                algorithm: ALGORITHMS.NONE,
                originalSize: dataString.length,
                compressedSize: dataString.length,
                metadata
            };
        }

        // Try to use web worker if enabled
        if (useWorker && window.StashyWorkerManager) {
            try {
                const result = await window.StashyWorkerManager.runTask(
                    'workers/storage-worker.js',
                    'compressData',
                    { input: dataString, algorithm, options }
                );

                if (result && result.compressed) {
                    debugLog(`Compressed data with ${algorithm} using worker (${result.originalSize} → ${result.compressedSize} bytes)`);
                    return {
                        compressed: true,
                        data: result.compressed,
                        algorithm,
                        originalSize: result.originalSize,
                        compressedSize: result.compressedSize,
                        compressionRatio: result.compressionRatio,
                        metadata
                    };
                }
            } catch (error) {
                console.warn('Stashy: Error compressing data with worker:', error);
                // Fall back to in-thread compression
            }
        }

        // Compress in the main thread
        try {
            let compressed;
            let compressedSize;

            switch (algorithm) {
                case ALGORITHMS.LZ_STRING:
                    if (!lzString) {
                        throw new Error('LZ-String library not available');
                    }
                    compressed = lzString.compressToUTF16(dataString);
                    compressedSize = compressed.length * 2; // UTF-16 uses 2 bytes per character
                    break;

                case ALGORITHMS.PAKO:
                    if (!pako) {
                        throw new Error('Pako library not available');
                    }
                    const uint8Array = pako.deflate(dataString);
                    compressed = uint8ArrayToBase64(uint8Array);
                    compressedSize = compressed.length;
                    break;

                case ALGORITHMS.SIMPLE:
                    compressed = simpleCompress(dataString);
                    compressedSize = compressed.length;
                    break;

                default:
                    throw new Error(`Unknown compression algorithm: ${algorithm}`);
            }

            const originalSize = dataString.length;
            const compressionRatio = originalSize / compressedSize;

            debugLog(`Compressed data with ${algorithm} (${originalSize} → ${compressedSize} bytes, ratio: ${compressionRatio.toFixed(2)})`);

            return {
                compressed: true,
                data: compressed,
                algorithm,
                originalSize,
                compressedSize,
                compressionRatio,
                metadata
            };
        } catch (error) {
            console.error(`Stashy: Error compressing data with ${algorithm}:`, error);

            // Return uncompressed data as fallback
            return {
                compressed: false,
                data: data,
                algorithm: ALGORITHMS.NONE,
                originalSize: dataString.length,
                compressedSize: dataString.length,
                error: error.message,
                metadata
            };
        }
    }

    /**
     * Decompresses data using the specified algorithm
     * @param {Object} compressedData - The compressed data object
     * @returns {Promise<any>} A promise that resolves with the decompressed data
     */
    async function decompress(compressedData) {
        // If data is not compressed, return as is
        if (!compressedData.compressed) {
            return compressedData.data;
        }

        const { data, algorithm } = compressedData;

        // Try to use web worker if enabled
        if (config.useWorker && window.StashyWorkerManager) {
            try {
                const result = await window.StashyWorkerManager.runTask(
                    'workers/storage-worker.js',
                    'decompressData',
                    { input: data, algorithm }
                );

                if (result && result.decompressed) {
                    debugLog(`Decompressed data with ${algorithm} using worker`);

                    // Parse JSON if the result is a string that looks like JSON
                    if (typeof result.decompressed === 'string' &&
                        result.decompressed.startsWith('{') ||
                        result.decompressed.startsWith('[')) {
                        try {
                            return JSON.parse(result.decompressed);
                        } catch (e) {
                            // If parsing fails, return the string
                            return result.decompressed;
                        }
                    }

                    return result.decompressed;
                }
            } catch (error) {
                console.warn('Stashy: Error decompressing data with worker:', error);
                // Fall back to in-thread decompression
            }
        }

        // Decompress in the main thread
        try {
            let decompressed;

            switch (algorithm) {
                case ALGORITHMS.LZ_STRING:
                    if (!lzString) {
                        throw new Error('LZ-String library not available');
                    }
                    decompressed = lzString.decompressFromUTF16(data);
                    break;

                case ALGORITHMS.PAKO:
                    if (!pako) {
                        throw new Error('Pako library not available');
                    }
                    const uint8Array = base64ToUint8Array(data);
                    decompressed = pako.inflate(uint8Array, { to: 'string' });
                    break;

                case ALGORITHMS.SIMPLE:
                    decompressed = simpleDecompress(data);
                    break;

                default:
                    throw new Error(`Unknown compression algorithm: ${algorithm}`);
            }

            debugLog(`Decompressed data with ${algorithm}`);

            // Parse JSON if the result is a string that looks like JSON
            if (typeof decompressed === 'string' &&
                (decompressed.startsWith('{') || decompressed.startsWith('['))) {
                try {
                    return JSON.parse(decompressed);
                } catch (e) {
                    // If parsing fails, return the string
                    return decompressed;
                }
            }

            return decompressed;
        } catch (error) {
            console.error(`Stashy: Error decompressing data with ${algorithm}:`, error);

            // Return compressed data as fallback
            return compressedData.data;
        }
    }

    /**
     * Simple RLE compression algorithm
     * @param {string} str - The string to compress
     * @returns {string} The compressed string
     */
    function simpleCompress(str) {
        let result = '';
        let count = 1;
        let char = str[0];

        for (let i = 1; i < str.length; i++) {
            if (str[i] === char) {
                count++;
            } else {
                result += (count > 3 ? count + char : char.repeat(count));
                char = str[i];
                count = 1;
            }
        }

        result += (count > 3 ? count + char : char.repeat(count));
        return result;
    }

    /**
     * Simple RLE decompression algorithm
     * @param {string} str - The compressed string
     * @returns {string} The decompressed string
     */
    function simpleDecompress(str) {
        let result = '';
        let i = 0;

        while (i < str.length) {
            // Check if the current character is a digit
            let countStr = '';
            while (i < str.length && /\d/.test(str[i])) {
                countStr += str[i++];
            }

            // If we found digits, repeat the next character that many times
            if (countStr) {
                const count = parseInt(countStr, 10);
                const char = str[i++];
                result += char.repeat(count);
            } else {
                // Otherwise, just add the current character
                result += str[i++];
            }
        }

        return result;
    }

    /**
     * Converts a Uint8Array to a Base64 string
     * @param {Uint8Array} uint8Array - The Uint8Array to convert
     * @returns {string} The Base64 string
     */
    function uint8ArrayToBase64(uint8Array) {
        let binary = '';
        const len = uint8Array.length;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(uint8Array[i]);
        }
        return btoa(binary);
    }

    /**
     * Converts a Base64 string to a Uint8Array
     * @param {string} base64 - The Base64 string to convert
     * @returns {Uint8Array} The Uint8Array
     */
    function base64ToUint8Array(base64) {
        const binary = atob(base64);
        const len = binary.length;
        const bytes = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
            bytes[i] = binary.charCodeAt(i);
        }
        return bytes;
    }

    /**
     * Determines if data should be compressed
     * @param {any} data - The data to check
     * @param {string} key - The data key
     * @returns {boolean} True if the data should be compressed
     */
    function shouldCompress(data, key) {
        // Skip compression for small data
        if (typeof data === 'string' && data.length < config.compressionThreshold) {
            return false;
        }

        // Skip compression for objects that are likely to be small
        if (typeof data === 'object' && data !== null) {
            const json = JSON.stringify(data);
            if (json.length < config.compressionThreshold) {
                return false;
            }
        }

        // Skip compression for metadata unless explicitly enabled
        if (key.includes('metadata') && !config.compressMetadata) {
            return false;
        }

        return true;
    }

    // Initialize the compression module when loaded
    init().catch(error => {
        console.error('Stashy: Failed to initialize compression module:', error);
    });

    // Return the public API
    return {
        // Core compression operations
        compress,
        decompress,
        shouldCompress,

        // Constants
        ALGORITHMS,

        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stashy: Data Compression Module Loaded");
