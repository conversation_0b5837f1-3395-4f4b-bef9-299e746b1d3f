/**
 * Stashy Cache Manager
 * Provides a unified interface for caching operations with tiered storage
 * and intelligent cache invalidation strategies
 */

// Create a namespace to avoid global pollution
window.StashyCacheManager = (function() {
    // Constants
    const CACHE_KEYS = {
        DRIVE_FOLDER_ID: 'drive_folder_id_',
        SCREENSHOTS_FOLDER_ID: 'screenshots_folder_id',
        NOTEBOOKS: 'notebooks',
        DRIVE_START_PAGE_TOKEN: 'drive_start_page_token'
    };

    // Default TTL values (in milliseconds)
    const TTL = {
        SHORT: 5 * 60 * 1000,        // 5 minutes
        MEDIUM: 30 * 60 * 1000,      // 30 minutes
        LONG: 24 * 60 * 60 * 1000,   // 24 hours
        VERY_LONG: 7 * 24 * 60 * 60 * 1000 // 7 days
    };

    // Configuration
    const config = {
        useIndexedDB: true,          // Use IndexedDB when available
        useMemoryCache: true,        // Use in-memory cache for frequently accessed items
        useLocalStorage: true,       // Use localStorage as fallback
        debug: false                 // Debug mode disabled
    };

    // Private variables
    let hasIndexedDB = false;

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StashyCacheManager]', ...args);
        }
    }

    /**
     * Initializes the cache manager
     * @returns {Promise<boolean>} A promise that resolves with true if initialization was successful
     */
    async function init() {
        try {
            // Check if IndexedDB is available
            if (config.useIndexedDB && window.StashyIndexedDB) {
                try {
                    await window.StashyIndexedDB.init();
                    hasIndexedDB = true;
                    debugLog('IndexedDB initialized successfully');

                    // Schedule cleanup of expired cache items
                    setTimeout(() => {
                        window.StashyIndexedDB.clearExpiredCache()
                            .then(count => debugLog(`Cleared ${count} expired cache items`))
                            .catch(error => console.error('Error clearing expired cache:', error));
                    }, 5000); // 5 seconds after initialization
                } catch (error) {
                    console.error('Stashy: Failed to initialize IndexedDB:', error);
                    hasIndexedDB = false;
                }
            }

            return true;
        } catch (error) {
            console.error('Stashy: Error initializing cache manager:', error);
            return false;
        }
    }

    /**
     * Sets a cache item with the specified TTL
     * @param {string} key - The cache key
     * @param {any} data - The data to cache
     * @param {number} [ttl=TTL.MEDIUM] - Time to live in milliseconds
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function set(key, data, ttl = TTL.MEDIUM) {
        try {
            // Try IndexedDB first if available
            if (hasIndexedDB && config.useIndexedDB) {
                await window.StashyIndexedDB.setCacheItem(key, data, ttl);
                debugLog(`Set cache item in IndexedDB: ${key}`);
                return true;
            }

            // Fall back to chrome.storage.local
            if (window.StashySecureStorage && typeof window.StashySecureStorage.set === 'function') {
                const cacheItem = {
                    data: data,
                    timestamp: Date.now(),
                    expiry: Date.now() + ttl
                };

                await new Promise((resolve, reject) => {
                    window.StashySecureStorage.set(`cache_${key}`, cacheItem, (error) => {
                        if (error) {
                            reject(error);
                        } else {
                            resolve();
                        }
                    });
                });

                debugLog(`Set cache item in chrome.storage.local: ${key}`);
                return true;
            }

            // Last resort: localStorage
            if (config.useLocalStorage && window.StashySecureStorage &&
                typeof window.StashySecureStorage.localSet === 'function') {
                const cacheItem = {
                    data: data,
                    timestamp: Date.now(),
                    expiry: Date.now() + ttl
                };

                const success = window.StashySecureStorage.localSet(`cache_${key}`, cacheItem);
                if (success) {
                    debugLog(`Set cache item in localStorage: ${key}`);
                    return true;
                }
            }

            console.warn(`Stashy: Failed to set cache item: ${key}`);
            return false;
        } catch (error) {
            console.error(`Stashy: Error setting cache item ${key}:`, error);
            return false;
        }
    }

    /**
     * Gets a cache item if it exists and is not expired
     * @param {string} key - The cache key
     * @returns {Promise<any|null>} A promise that resolves with the cached data or null if not found or expired
     */
    async function get(key) {
        try {
            // Try IndexedDB first if available
            if (hasIndexedDB && config.useIndexedDB) {
                const data = await window.StashyIndexedDB.getCacheItem(key);
                if (data !== null) {
                    debugLog(`Got cache item from IndexedDB: ${key}`);
                    return data;
                }
            }

            // Fall back to chrome.storage.local
            if (window.StashySecureStorage && typeof window.StashySecureStorage.get === 'function') {
                const result = await new Promise((resolve) => {
                    window.StashySecureStorage.get(`cache_${key}`, (data, error) => {
                        if (error || !data) {
                            resolve(null);
                        } else {
                            resolve(data);
                        }
                    });
                });

                if (result) {
                    // Check if the item has expired
                    if (Date.now() > result.expiry) {
                        // Remove expired item
                        window.StashySecureStorage.remove(`cache_${key}`);
                        return null;
                    }

                    debugLog(`Got cache item from chrome.storage.local: ${key}`);
                    return result.data;
                }
            }

            // Last resort: localStorage
            if (config.useLocalStorage && window.StashySecureStorage &&
                typeof window.StashySecureStorage.localGet === 'function') {
                const result = window.StashySecureStorage.localGet(`cache_${key}`);

                if (result) {
                    // Check if the item has expired
                    if (Date.now() > result.expiry) {
                        // Remove expired item
                        window.StashySecureStorage.localRemove(`cache_${key}`);
                        return null;
                    }

                    debugLog(`Got cache item from localStorage: ${key}`);
                    return result.data;
                }
            }

            return null;
        } catch (error) {
            console.error(`Stashy: Error getting cache item ${key}:`, error);
            return null;
        }
    }

    /**
     * Removes a cache item
     * @param {string} key - The cache key
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function remove(key) {
        try {
            let success = false;

            // Remove from IndexedDB if available
            if (hasIndexedDB && config.useIndexedDB) {
                try {
                    await window.StashyIndexedDB.removeItem('cache', key);
                    success = true;
                    debugLog(`Removed cache item from IndexedDB: ${key}`);
                } catch (e) {
                    // Ignore errors
                }
            }

            // Remove from chrome.storage.local
            if (window.StashySecureStorage && typeof window.StashySecureStorage.remove === 'function') {
                await new Promise((resolve) => {
                    window.StashySecureStorage.remove(`cache_${key}`, () => {
                        success = true;
                        resolve();
                    });
                });
                debugLog(`Removed cache item from chrome.storage.local: ${key}`);
            }

            // Remove from localStorage
            if (config.useLocalStorage && window.StashySecureStorage &&
                typeof window.StashySecureStorage.localRemove === 'function') {
                window.StashySecureStorage.localRemove(`cache_${key}`);
                success = true;
                debugLog(`Removed cache item from localStorage: ${key}`);
            }

            return success;
        } catch (error) {
            console.error(`Stashy: Error removing cache item ${key}:`, error);
            return false;
        }
    }

    /**
     * Clears all cache items or items with a specific prefix
     * @param {string} [prefix] - Optional prefix to clear only matching keys
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function clear(prefix) {
        try {
            let success = false;

            // Clear from IndexedDB if available
            if (hasIndexedDB && config.useIndexedDB) {
                try {
                    if (prefix) {
                        // TODO: Implement prefix-based clearing in IndexedDB
                        // For now, we'll just clear the memory cache with the prefix
                        window.StashyIndexedDB.clearMemoryCache(prefix);
                    } else {
                        // Clear expired items
                        await window.StashyIndexedDB.clearExpiredCache();
                    }
                    success = true;
                } catch (e) {
                    // Ignore errors
                }
            }

            // TODO: Implement clearing from chrome.storage.local and localStorage

            return success;
        } catch (error) {
            console.error('Stashy: Error clearing cache:', error);
            return false;
        }
    }



    /**
     * Gets or sets the Drive folder ID from cache
     * @param {string} folderType - The type of folder (e.g., 'screenshots')
     * @param {Function} [getFolderCallback] - Function to call if cache miss
     * @returns {Promise<string|null>} A promise that resolves with the folder ID or null
     */
    async function getDriveFolderId(folderType, getFolderCallback) {
        try {
            const key = folderType === 'screenshots'
                ? CACHE_KEYS.SCREENSHOTS_FOLDER_ID
                : `${CACHE_KEYS.DRIVE_FOLDER_ID}${folderType}`;

            // Try to get from cache
            const cachedId = await get(key);
            if (cachedId) {
                debugLog(`Got Drive folder ID from cache for ${folderType}`);
                return cachedId;
            }

            // Cache miss, get the folder ID
            if (typeof getFolderCallback === 'function') {
                const folderId = await getFolderCallback();

                // Cache the result
                if (folderId) {
                    await set(key, folderId, TTL.VERY_LONG); // Cache for 7 days
                }

                return folderId;
            }

            return null;
        } catch (error) {
            console.error(`Stashy: Error getting Drive folder ID for ${folderType}:`, error);
            return null;
        }
    }

    /**
     * Sets the Drive folder ID in cache
     * @param {string} folderType - The type of folder (e.g., 'screenshots')
     * @param {string} folderId - The folder ID to cache
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function setDriveFolderId(folderType, folderId) {
        try {
            const key = folderType === 'screenshots'
                ? CACHE_KEYS.SCREENSHOTS_FOLDER_ID
                : `${CACHE_KEYS.DRIVE_FOLDER_ID}${folderType}`;

            return await set(key, folderId, TTL.VERY_LONG); // Cache for 7 days
        } catch (error) {
            console.error(`Stashy: Error setting Drive folder ID for ${folderType}:`, error);
            return false;
        }
    }

    /**
     * Invalidates a specific cache item
     * @param {string} key - The cache key to invalidate
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function invalidate(key) {
        return await remove(key);
    }



    // Initialize the cache manager when the module loads
    init().catch(error => {
        console.error('Stashy: Failed to initialize cache manager:', error);
    });

    // Return the public API
    return {
        // Core cache operations
        set,
        get,
        remove,
        clear,
        invalidate,

        // Specialized cache operations
        getDriveFolderId,
        setDriveFolderId,

        // Constants
        TTL,
        CACHE_KEYS,

        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stashy: Cache Manager Loaded");