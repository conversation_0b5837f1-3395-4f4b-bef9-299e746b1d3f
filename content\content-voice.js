/**
 * Toggles the voice recording state (starts or stops).
 * Checks for browser support and permissions.
 * Now supports both standard and enhanced voice recording.
 */
function toggleRecording() {
    // Check if enhanced voice module is available
    if (typeof window.startEnhancedRecording === 'function' &&
        typeof window.stopEnhancedRecording === 'function') {

        // Use enhanced voice recording - check both states for proper synchronization
        const enhancedRecording = (typeof window.isEnhancedRecording !== 'undefined' && window.isEnhancedRecording);

        if (isRecording || enhancedRecording) {
            // Force stop regardless of state - ensure immediate response to manual click
            console.log("Stashy: Manual stop requested");
            window.stopEnhancedRecording();
            isRecording = false; // Ensure standard state is also updated
        } else {
            // Check permissions first
            checkMicrophonePermission(() => {
                window.startEnhancedRecording();
                isRecording = true; // Ensure standard state is also updated
            });
        }
        return;
    }

    // Fall back to standard recording if enhanced is not available
    if (!SpeechRecognition) {
        alert("Sorry, your browser doesn't support the Web Speech API. Try Google Chrome.");
        return;
    }
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        alert("Microphone access (getUserMedia) is not supported or blocked by your browser.");
        return;
    }

    // Modern permission check (if available)
    checkMicrophonePermission(proceedToggleRecording);
}

/**
 * Checks for microphone permission and calls the callback if granted
 * @param {Function} callback - Function to call if permission is granted
 */
function checkMicrophonePermission(callback) {
    if (navigator.permissions && navigator.permissions.query) {
        navigator.permissions.query({ name: 'microphone' }).then(permissionStatus => {
            if (permissionStatus.state === 'denied') {
                alert("Microphone permission was denied. Please allow access in your browser's site settings and reload the page.");
                return;
            }
            // If granted or prompt, proceed with the callback
            callback();
            // Listen for changes (e.g., if user revokes permission mid-session)
            permissionStatus.onchange = () => {
                 console.log("Microphone permission state changed to:", permissionStatus.state);
                 if(permissionStatus.state === 'denied' && isRecording) {
                    alert("Microphone permission revoked. Stopping recording.");
                    if (typeof window.stopEnhancedRecording === 'function') {
                        window.stopEnhancedRecording();
                    } else {
                        stopRecording();
                    }
                 }
            };
        }).catch(err => {
            console.warn("Stashy: Microphone permission query failed, proceeding with callback:", err);
            callback(); // Fallback if query fails
        });
    } else {
        // Fallback if Permissions API is not supported
        console.warn("Stashy: Permissions API not available, attempting directly.");
        callback();
    }
}

/**
 * Internal function to actually start or stop recording after checks.
 */
function proceedToggleRecording() {
    if (isRecording) {
        stopRecording();
    } else {
        startRecording();
    }
}

/**
 * Function to insert accumulated text into the note at the cursor position
 */
function insertAccumulatedText() {
    if (pendingTranscript.trim() && noteText) {
        // Clear any interim preview first
        clearInterimTranscript();

        const textToInsert = pendingTranscript.trim();

        // Special handling for YouTube to ensure real-time insertion works
        const isYouTube = window.location.hostname.includes('youtube.com');
        if (isYouTube) {
            console.log("Stashy: Voice typing on YouTube - using optimized insertion");
            insertTextForYouTube(textToInsert);
        } else {
            // Try to insert at the saved cursor position
            if (voiceInsertionPoint && voiceInsertionPoint.startContainer) {
                insertTextAtCursorPosition(textToInsert);
            } else {
                // Fallback to simple insertion if no cursor position saved
                insertTextFallback(textToInsert);
            }
        }

        // Execute any pending delayed voice commands AFTER text insertion
        // (Immediate commands like line breaks are already executed)
        if (pendingVoiceCommands && pendingVoiceCommands.length > 0) {
            // Adaptive delay based on text length and command complexity
            const adaptiveDelay = calculateOptimalDelay(textToInsert, pendingVoiceCommands);

            console.log(`Stashy: Executing ${pendingVoiceCommands.length} delayed commands after ${adaptiveDelay}ms delay`);
            setTimeout(() => {
                executeVoiceCommandsWithRetry(pendingVoiceCommands, textToInsert);
                pendingVoiceCommands = []; // Clear pending commands
            }, adaptiveDelay);
        }

        // Schedule save after updating content
        scheduleSave();
        pendingTranscript = ''; // Clear the buffer
    }
}

/**
 * Shows interim transcription results directly in the note text area
 * @param {string} interimText - The interim transcription text to display
 * @param {number} confidence - Confidence level (0-1) for visual feedback
 */
function showInterimTranscript(interimText, confidence = 0.8) {
    if (!noteText || !interimText.trim()) {
        clearInterimTranscript();
        return;
    }

    // Throttle updates for performance (max 10 updates per second)
    const now = Date.now();
    if (now - lastInterimUpdate < 100) {
        return;
    }
    lastInterimUpdate = now;

    // Store original content and cursor position if not already stored
    if (!interimTranscriptElement) {
        originalNoteContent = noteText.innerHTML;
        voiceInsertionPoint = saveCurrentCursorPosition();
    }

    // Clear any existing interim element
    clearInterimTranscript();

    // Create enhanced interim transcript element
    interimTranscriptElement = document.createElement('span');
    interimTranscriptElement.className = 'Stashy-interim-transcript';
    interimTranscriptElement.textContent = interimText;
    interimTranscriptElement.setAttribute('aria-label', `Interim transcription: ${interimText}`);

    // Adjust styling based on confidence level
    const opacity = Math.max(0.5, confidence);
    interimTranscriptElement.style.opacity = opacity.toString();

    // Insert at cursor position or append at end
    insertInterimAtPosition(interimTranscriptElement);

    // Update ARIA live region for screen readers
    updateAriaLiveRegion(`Transcribing: ${interimText}`);

    // Update voice status indicator
    updateVoiceStatus('listening');
}

/**
 * Clears interim transcription preview from the note text area
 */
function clearInterimTranscript() {
    if (interimTranscriptElement && noteText) {
        // Remove the interim element
        if (interimTranscriptElement.parentNode) {
            interimTranscriptElement.parentNode.removeChild(interimTranscriptElement);
        }

        // Remove any trailing space that was added for interim text
        const lastChild = noteText.lastChild;
        if (lastChild && lastChild.nodeType === Node.TEXT_NODE && lastChild.textContent === ' ') {
            // Check if this space was added for interim text by comparing content
            const contentWithoutSpace = noteText.innerHTML.slice(0, -1);
            if (contentWithoutSpace === originalNoteContent) {
                noteText.removeChild(lastChild);
            }
        }

        interimTranscriptElement = null;
    }
}

/**
 * Saves the current cursor position in the note text area
 * @returns {Object|null} Cursor position data or null if no selection
 */
function saveCurrentCursorPosition() {
    if (!noteText) return null;

    const selection = window.getSelection();

    // If no selection exists, try to create one at the end of the note
    if (!selection.rangeCount) {
        try {
            const range = document.createRange();
            range.selectNodeContents(noteText);
            range.collapse(false); // Collapse to end
            return {
                startContainer: range.startContainer,
                startOffset: range.startOffset,
                endContainer: range.endContainer,
                endOffset: range.endOffset,
                isAtEnd: true
            };
        } catch (e) {
            console.warn('Stashy: Could not create default cursor position:', e);
            return null;
        }
    }

    const range = selection.getRangeAt(0);

    // Check if selection is within our note text area
    if (!noteText.contains(range.commonAncestorContainer)) {
        // If cursor is not in note area, default to end of note
        try {
            const defaultRange = document.createRange();
            defaultRange.selectNodeContents(noteText);
            defaultRange.collapse(false); // Collapse to end
            return {
                startContainer: defaultRange.startContainer,
                startOffset: defaultRange.startOffset,
                endContainer: defaultRange.endContainer,
                endOffset: defaultRange.endOffset,
                isAtEnd: true
            };
        } catch (e) {
            console.warn('Stashy: Could not create fallback cursor position:', e);
            return null;
        }
    }

    // Return the actual cursor position
    return {
        startContainer: range.startContainer,
        startOffset: range.startOffset,
        endContainer: range.endContainer,
        endOffset: range.endOffset,
        isAtEnd: false
    };
}

/**
 * Inserts interim transcript at the saved cursor position or at the end
 * @param {HTMLElement} element - The interim transcript element to insert
 */
function insertInterimAtPosition(element) {
    if (!noteText || !element) return;

    if (voiceInsertionPoint && voiceInsertionPoint.startContainer) {
        try {
            // Try to insert at the saved cursor position
            const range = document.createRange();
            range.setStart(voiceInsertionPoint.startContainer, voiceInsertionPoint.startOffset);
            range.setEnd(voiceInsertionPoint.endContainer, voiceInsertionPoint.endOffset);

            // Determine if we need spacing using the same logic as final text insertion
            const needsSpaceBefore = shouldAddSpaceBefore(range);

            if (needsSpaceBefore) {
                const spaceNode = document.createTextNode(' ');
                range.insertNode(spaceNode);
                range.setStartAfter(spaceNode);
            }

            range.insertNode(element);
            range.setStartAfter(element);

            // Don't update selection for interim text to avoid disrupting user's cursor
            // The interim text is just a preview

        } catch (e) {
            console.warn('Stashy: Could not insert interim at cursor position, appending at end:', e);
            // Fallback to appending at end
            appendInterimAtEnd(element, shouldAddSpaceBeforeFallback());
        }
    } else {
        // Fallback to appending at end
        appendInterimAtEnd(element, shouldAddSpaceBeforeFallback());
    }
}

/**
 * Determines if space should be added before interim text in fallback mode
 * @returns {boolean} True if space should be added
 */
function shouldAddSpaceBeforeFallback() {
    if (!originalNoteContent) return false;
    return originalNoteContent.length > 0 && !/[\s\>]$/.test(originalNoteContent);
}

/**
 * Calculates optimal delay for command execution based on text complexity
 * @param {string} insertedText - The text that was inserted
 * @param {Array} commands - The commands to execute
 * @returns {number} Optimal delay in milliseconds
 */
function calculateOptimalDelay(insertedText, commands) {
    // PERFORMANCE FIX: Significantly reduced delays for faster response

    // Check if we're in symbol command mode (Ctrl pressed)
    const processSymbols = shouldProcessSymbolCommands();

    if (processSymbols) {
        // In symbol mode, use minimal delay for immediate response
        return 5; // Very fast for symbol commands
    }

    // Base delay for non-symbol commands
    let delay = 10; // Reduced from 30

    // Add minimal delay based on text length
    if (insertedText.length > 100) {
        delay += Math.min(10, Math.floor(insertedText.length / 20)); // Reduced calculation
    }

    // Add minimal delay based on number of commands
    if (commands.length > 3) {
        delay += commands.length * 2; // Reduced from 5
    }

    // Minimum delay to ensure DOM is ready
    delay = Math.max(delay, 5); // Reduced from 20

    // Maximum delay to prevent sluggish feeling
    delay = Math.min(delay, 25); // Reduced from 150

    return delay;
}

/**
 * Executes voice commands with retry logic for better reliability
 * @param {Array} commands - Array of command objects to execute
 * @param {string} insertedText - The text that was just inserted
 * @param {number} retryCount - Current retry attempt (default 0)
 */
function executeVoiceCommandsWithRetry(commands, insertedText = '', retryCount = 0) {
    if (!commands || commands.length === 0) return;

    const maxRetries = 2;
    const retryDelay = 25;

    try {
        let successfulCommands = 0;

        for (const command of commands) {
            // Avoid processing the same command multiple times
            const commandKey = `${command.type}-${command.originalText}-${Date.now()}`;
            if (lastProcessedCommand === commandKey) {
                continue;
            }
            lastProcessedCommand = commandKey;

            console.log(`Stashy: Executing voice command: ${command.type} (${command.originalText}) after text: "${command.insertAfterText || ''}" (attempt ${retryCount + 1})`);

            let commandSuccess = false;

            // For symbol commands, we need to position them correctly relative to the text
            if (VOICE_COMMAND_SYMBOLS[command.type]) {
                commandSuccess = insertSymbolAtSpecificPositionWithValidation(
                    VOICE_COMMAND_SYMBOLS[command.type],
                    command.type,
                    command.insertAfterText || ''
                );
            } else {
                // Handle line break commands normally
                switch (command.type) {
                    case 'lineBreak':
                        insertLineBreakAtCursor();
                        commandSuccess = true;
                        break;
                    case 'paragraphBreak':
                        insertParagraphBreakAtCursor();
                        commandSuccess = true;
                        break;
                }
            }

            if (commandSuccess) {
                successfulCommands++;

                // Track performance metrics
                updateVoiceCommandPerformance(command.type, true);

                // Show visual feedback
                updateVoiceStatus('command');
                updateAriaLiveRegion(`Command executed: ${command.type}`);

                // Show command notification
                let commandName;
                let symbol = '';

                if (command.type === 'lineBreak') {
                    commandName = 'Line Break';
                } else if (command.type === 'paragraphBreak') {
                    commandName = 'Paragraph Break';
                } else if (VOICE_COMMAND_SYMBOLS[command.type]) {
                    symbol = VOICE_COMMAND_SYMBOLS[command.type];
                    commandName = getSymbolDisplayName(command.type);
                } else {
                    commandName = command.type;
                }

                const notificationText = symbol ? `✓ ${symbol} (${commandName}) inserted` : `✓ ${commandName} inserted`;
                showVoiceNotification(notificationText, 'success', 1500);
            } else {
                // Track failed command
                updateVoiceCommandPerformance(command.type, false);
            }
        }

        // If some commands failed and we haven't exceeded retry limit, retry
        if (successfulCommands < commands.length && retryCount < maxRetries) {
            console.log(`Stashy: Retrying ${commands.length - successfulCommands} failed commands (attempt ${retryCount + 2})`);
            setTimeout(() => {
                executeVoiceCommandsWithRetry(commands, insertedText, retryCount + 1);
            }, retryDelay);
            return;
        }

        // Reset status after a short delay
        setTimeout(() => {
            if (typeof isRecording !== 'undefined' && isRecording) {
                updateVoiceStatus('listening');
            }
        }, 600);

    } catch (e) {
        console.error('Stashy: Error in executeVoiceCommandsWithRetry:', e);

        // Fallback to original execution method
        if (retryCount === 0) {
            console.log('Stashy: Falling back to original command execution');
            executeVoiceCommands(commands, insertedText);
        }
    }
}

/**
 * Updates voice command performance metrics
 * @param {string} commandType - The type of command executed
 * @param {boolean} success - Whether the command was successful
 */
function updateVoiceCommandPerformance(commandType, success) {
    if (typeof voiceCommandPerformance === 'undefined') return;

    voiceCommandPerformance.totalCommands++;

    if (success) {
        voiceCommandPerformance.successfulCommands++;
    } else {
        voiceCommandPerformance.failedCommands++;
    }

    // Track per-command-type statistics
    if (!voiceCommandPerformance.commandTypeStats[commandType]) {
        voiceCommandPerformance.commandTypeStats[commandType] = {
            total: 0,
            successful: 0,
            failed: 0
        };
    }

    voiceCommandPerformance.commandTypeStats[commandType].total++;
    if (success) {
        voiceCommandPerformance.commandTypeStats[commandType].successful++;
    } else {
        voiceCommandPerformance.commandTypeStats[commandType].failed++;
    }

    // Log performance summary every 10 commands
    if (voiceCommandPerformance.totalCommands % 10 === 0) {
        const successRate = (voiceCommandPerformance.successfulCommands / voiceCommandPerformance.totalCommands * 100).toFixed(1);
        console.log(`Stashy Voice Performance: ${voiceCommandPerformance.totalCommands} commands, ${successRate}% success rate`);
    }
}

/**
 * Gets voice command performance statistics
 * @returns {Object} Performance statistics
 */
function getVoiceCommandPerformanceStats() {
    if (typeof voiceCommandPerformance === 'undefined') {
        return { error: 'Performance tracking not available' };
    }

    const successRate = voiceCommandPerformance.totalCommands > 0 ?
        (voiceCommandPerformance.successfulCommands / voiceCommandPerformance.totalCommands * 100).toFixed(1) : 0;

    return {
        totalCommands: voiceCommandPerformance.totalCommands,
        successfulCommands: voiceCommandPerformance.successfulCommands,
        failedCommands: voiceCommandPerformance.failedCommands,
        successRate: `${successRate}%`,
        commandTypeStats: voiceCommandPerformance.commandTypeStats,
        sessionDuration: Date.now() - voiceCommandPerformance.lastResetTime
    };
}

/**
 * Resets voice command performance statistics
 */
function resetVoiceCommandPerformanceStats() {
    if (typeof voiceCommandPerformance !== 'undefined') {
        voiceCommandPerformance.totalCommands = 0;
        voiceCommandPerformance.successfulCommands = 0;
        voiceCommandPerformance.failedCommands = 0;
        voiceCommandPerformance.commandTypeStats = {};
        voiceCommandPerformance.lastResetTime = Date.now();
        console.log('Stashy: Voice command performance statistics reset');
    }
}

/**
 * Initializes the voice command modifier key system
 */
function initVoiceModifierKeySystem() {
    if (voiceModifierKeySystem.keyListenersAttached) {
        return; // Already initialized
    }

    // Add keydown listener
    document.addEventListener('keydown', handleModifierKeyDown, true);

    // Add keyup listener
    document.addEventListener('keyup', handleModifierKeyUp, true);

    // Add window focus/blur listeners to reset state
    window.addEventListener('blur', resetModifierKeyState);
    window.addEventListener('focus', resetModifierKeyState);

    voiceModifierKeySystem.keyListenersAttached = true;
    console.log(`Stashy: Voice modifier key system initialized with key: ${voiceModifierKeySystem.modifierKey}`);
}

/**
 * Handles keydown events for modifier key detection
 * @param {KeyboardEvent} event - The keyboard event
 */
function handleModifierKeyDown(event) {
    if (!voiceModifierKeySystem.enabled) return;

    const wasPressed = voiceModifierKeySystem.isModifierPressed;
    voiceModifierKeySystem.isModifierPressed = event[voiceModifierKeySystem.modifierKey] || false;

    // Update visual feedback if state changed
    if (wasPressed !== voiceModifierKeySystem.isModifierPressed && voiceModifierKeySystem.isModifierPressed) {
        updateVoiceModifierVisualFeedback(true);
        console.log('Stashy: Voice modifier key pressed - symbol commands ENABLED');
    }
}

/**
 * Handles keyup events for modifier key detection
 * @param {KeyboardEvent} event - The keyboard event
 */
function handleModifierKeyUp(event) {
    if (!voiceModifierKeySystem.enabled) return;

    const wasPressed = voiceModifierKeySystem.isModifierPressed;
    voiceModifierKeySystem.isModifierPressed = event[voiceModifierKeySystem.modifierKey] || false;

    // Update visual feedback if state changed
    if (wasPressed !== voiceModifierKeySystem.isModifierPressed && !voiceModifierKeySystem.isModifierPressed) {
        updateVoiceModifierVisualFeedback(false);
        console.log('Stashy: Voice modifier key released - symbol commands DISABLED');
    }
}

/**
 * Resets the modifier key state (called on window blur/focus)
 */
function resetModifierKeyState() {
    if (voiceModifierKeySystem.isModifierPressed) {
        voiceModifierKeySystem.isModifierPressed = false;
        updateVoiceModifierVisualFeedback(false);
        console.log('Stashy: Voice modifier key state reset');
    }
}

/**
 * Updates visual feedback for modifier key state
 * @param {boolean} modifierActive - Whether the modifier key is active
 */
function updateVoiceModifierVisualFeedback(modifierActive) {
    if (!voiceModifierKeySystem.visualFeedbackEnabled) return;

    const recordButton = document.getElementById('Stashy-voice-record-btn');
    if (!recordButton) return;

    if (modifierActive) {
        // Add modifier active class for visual feedback
        recordButton.classList.add('Stashy-voice-modifier-active');
        recordButton.title = 'Voice Recording (Symbol Commands ENABLED - Hold Ctrl while speaking)';
    } else {
        // Remove modifier active class
        recordButton.classList.remove('Stashy-voice-modifier-active');
        recordButton.title = 'Voice Recording (Normal Mode - Hold Ctrl for symbol commands)';
    }
}

/**
 * Checks if symbol commands should be processed based on modifier key state
 * @returns {boolean} True if symbol commands should be processed
 */
function shouldProcessSymbolCommands() {
    if (!voiceModifierKeySystem.enabled) {
        return false; // Default to disabled if modifier system is disabled
    }

    return voiceModifierKeySystem.isModifierPressed; // Process symbols ONLY when Ctrl is pressed
}

/**
 * Gets the current modifier key configuration
 * @returns {Object} Modifier key configuration
 */
function getVoiceModifierKeyConfig() {
    return {
        enabled: voiceModifierKeySystem.enabled,
        modifierKey: voiceModifierKeySystem.modifierKey,
        isPressed: voiceModifierKeySystem.isModifierPressed,
        visualFeedbackEnabled: voiceModifierKeySystem.visualFeedbackEnabled
    };
}

/**
 * Updates the voice modifier key configuration
 * @param {Object} config - New configuration
 */
function updateVoiceModifierKeyConfig(config) {
    if (config.enabled !== undefined) {
        voiceModifierKeySystem.enabled = config.enabled;
    }

    if (config.modifierKey !== undefined) {
        voiceModifierKeySystem.modifierKey = config.modifierKey;
    }

    if (config.visualFeedbackEnabled !== undefined) {
        voiceModifierKeySystem.visualFeedbackEnabled = config.visualFeedbackEnabled;
    }

    console.log('Stashy: Voice modifier key configuration updated:', voiceModifierKeySystem);
}

/**
 * Determines if a command should execute immediately or be delayed
 * @param {string} commandType - The type of command
 * @returns {boolean} True if command should execute immediately
 */
function shouldExecuteImmediately(commandType) {
    // CRITICAL FIX: Symbol commands should execute immediately when Ctrl is pressed
    // to prevent timing issues and wrong positioning
    const processSymbols = shouldProcessSymbolCommands();

    if (processSymbols && VOICE_COMMAND_SYMBOLS[commandType]) {
        // When Ctrl is pressed, symbol commands execute immediately
        console.log(`Stashy: Symbol command '${commandType}' will execute immediately (Ctrl mode)`);
        return true;
    }

    // Line breaks and paragraph breaks always execute immediately to maintain text flow
    const immediateCommands = ['lineBreak', 'paragraphBreak'];
    return immediateCommands.includes(commandType);
}

/**
 * Processes voice commands for line breaks and formatting
 * @param {string} transcript - The transcript text to process
 * @returns {Object} Object containing processed text, immediate commands, and delayed commands
 */
function processVoiceCommands(transcript) {
    if (!transcript || !transcript.trim()) {
        return { text: transcript, immediateCommands: [], delayedCommands: [] };
    }

    const originalText = transcript;
    const immediateCommands = [];
    const delayedCommands = [];

    // Check if symbol commands should be processed based on modifier key state
    const processSymbols = shouldProcessSymbolCommands();

    // Define command types based on modifier key state
    let commandTypes;
    if (processSymbols) {
        // Process all commands when Ctrl key IS pressed (symbol command mode)
        commandTypes = [
            'paragraphBreak', 'lineBreak', // Line breaks always available
            // Basic punctuation
            'period', 'comma', 'questionMark', 'exclamationMark',
            // Mathematical/Technical symbols
            'plusSign', 'minusSign', 'equalsSign', 'asterisk', 'forwardSlash', 'backslash',
            // Brackets and braces
            'openParenthesis', 'closeParenthesis', 'openBracket', 'closeBracket', 'openBrace', 'closeBrace',
            // Other symbols
            'atSymbol', 'hashSymbol', 'dollarSign', 'percentSign', 'underscore', 'pipe', 'ampersand',
            // Quotes and punctuation
            'colon', 'semicolon', 'doubleQuote', 'openQuote', 'closeQuote', 'apostrophe'
        ];
        console.log('Stashy: Symbol command mode ACTIVE - Ctrl key pressed, processing all commands');
    } else {
        // Only process line breaks when Ctrl key is NOT pressed (normal typing mode)
        commandTypes = ['paragraphBreak', 'lineBreak'];
        console.log('Stashy: Normal typing mode - Ctrl key not pressed, only processing line breaks');
    }

    // Find all command matches with their positions using pre-compiled patterns
    const allMatches = [];

    for (const commandType of commandTypes) {
        if (COMPILED_VOICE_PATTERNS[commandType]) {
            for (const compiledPattern of COMPILED_VOICE_PATTERNS[commandType]) {
                let match;
                // Reset regex lastIndex for global patterns
                compiledPattern.regex.lastIndex = 0;

                while ((match = compiledPattern.regex.exec(originalText)) !== null) {
                    allMatches.push({
                        type: commandType,
                        originalText: match[0],
                        startIndex: match.index,
                        endIndex: match.index + match[0].length
                    });

                    console.log(`Stashy: Voice command detected: ${commandType} ("${match[0]}") at position ${match.index}`);

                    // Prevent infinite loop for non-global regex
                    if (!compiledPattern.regex.global) break;
                }
            }
        }
    }

    // Sort matches by position (earliest first)
    allMatches.sort((a, b) => a.startIndex - b.startIndex);

    // Build the result by processing text and commands in order
    let currentPosition = 0;
    let processedText = '';

    for (const match of allMatches) {
        // Add text before this command
        if (match.startIndex > currentPosition) {
            const textBefore = originalText.substring(currentPosition, match.startIndex).trim();
            if (textBefore) {
                processedText += (processedText ? ' ' : '') + textBefore;
            }
        }

        // Separate immediate and delayed commands
        const command = {
            type: match.type,
            originalText: match.originalText,
            insertAfterText: processedText // Remember where to insert the symbol
        };

        if (shouldExecuteImmediately(match.type)) {
            immediateCommands.push(command);
        } else {
            delayedCommands.push(command);
        }

        currentPosition = match.endIndex;
    }

    // Add any remaining text after the last command
    if (currentPosition < originalText.length) {
        const remainingText = originalText.substring(currentPosition).trim();
        if (remainingText) {
            processedText += (processedText ? ' ' : '') + remainingText;
        }
    }

    // CRITICAL FIX: For immediate commands (Ctrl mode), exclude text that will be handled by insertTextAndSymbolImmediately
    // This prevents text duplication where the same text appears twice
    let finalProcessedText = processedText.trim();

    if (immediateCommands.length > 0) {
        const processSymbols = shouldProcessSymbolCommands();

        if (processSymbols) {
            // When Ctrl is pressed, immediate commands handle their own text insertion
            // So we need to exclude that text from the normal processing pipeline
            console.log(`Stashy: Immediate commands detected in Ctrl mode - filtering processed text`);

            // Remove text that will be handled by immediate commands
            for (const command of immediateCommands) {
                if (command.insertAfterText && command.insertAfterText.trim()) {
                    // This text will be inserted by insertTextAndSymbolImmediately, so remove it from processed text
                    const textToRemove = command.insertAfterText.trim();
                    finalProcessedText = finalProcessedText.replace(textToRemove, '').trim();
                    console.log(`Stashy: Removed "${textToRemove}" from processed text to prevent duplication`);
                }
            }

            // Clean up any extra spaces
            finalProcessedText = finalProcessedText.replace(/\s+/g, ' ').trim();
        }
    }

    return {
        text: finalProcessedText,
        immediateCommands: immediateCommands,
        delayedCommands: delayedCommands,
        // Legacy support for existing code
        commands: [...immediateCommands, ...delayedCommands]
    };
}

/**
 * Executes voice commands for line breaks and symbols
 * @param {Array} commands - Array of command objects to execute
 * @param {string} insertedText - The text that was just inserted (for positioning context)
 */
function executeVoiceCommands(commands, insertedText = '') {
    if (!commands || commands.length === 0) return;

    // Execute commands in the order they appeared in the original text
    for (const command of commands) {
        // Avoid processing the same command multiple times
        const commandKey = `${command.type}-${command.originalText}-${Date.now()}`;
        if (lastProcessedCommand === commandKey) {
            continue;
        }
        lastProcessedCommand = commandKey;

        // CRITICAL FIX: Check if we're in symbol command mode for immediate execution
        const processSymbols = shouldProcessSymbolCommands();

        console.log(`Stashy: Executing voice command: ${command.type} (${command.originalText}) - Symbol mode: ${processSymbols}`);

        // For symbol commands, use correct positioning logic regardless of execution timing
        if (VOICE_COMMAND_SYMBOLS[command.type]) {
            if (processSymbols) {
                // IMMEDIATE EXECUTION: Special handling for immediate symbol insertion
                // CRITICAL FIX: For immediate commands, the insertAfterText hasn't been inserted into DOM yet
                // So we need to use a different approach

                if (command.insertAfterText && command.insertAfterText.trim()) {
                    // Insert the text first, then the symbol immediately after it
                    insertTextAndSymbolImmediately(command.insertAfterText, VOICE_COMMAND_SYMBOLS[command.type], command.type);
                    console.log(`Stashy: Text "${command.insertAfterText}" and symbol '${VOICE_COMMAND_SYMBOLS[command.type]}' inserted immediately`);
                } else {
                    // No positioning context - insert symbol at cursor position
                    insertSymbolAtCursor(VOICE_COMMAND_SYMBOLS[command.type], command.type);
                    console.log(`Stashy: Symbol '${VOICE_COMMAND_SYMBOLS[command.type]}' inserted immediately at cursor (no positioning context)`);
                }
            } else {
                // Normal mode: position relative to text (legacy behavior)
                insertSymbolAtSpecificPosition(VOICE_COMMAND_SYMBOLS[command.type], command.type, command.insertAfterText || '');
            }
        } else {
            // Handle line break commands normally
            switch (command.type) {
                case 'lineBreak':
                    insertLineBreakAtCursor();
                    break;
                case 'paragraphBreak':
                    insertParagraphBreakAtCursor();
                    break;
            }
        }

        // Show visual feedback
        updateVoiceStatus('command');
        updateAriaLiveRegion(`Command executed: ${command.type}`);

        // Show command notification
        let commandName;
        let symbol = '';

        if (command.type === 'lineBreak') {
            commandName = 'Line Break';
        } else if (command.type === 'paragraphBreak') {
            commandName = 'Paragraph Break';
        } else if (VOICE_COMMAND_SYMBOLS[command.type]) {
            symbol = VOICE_COMMAND_SYMBOLS[command.type];
            commandName = getSymbolDisplayName(command.type);
        } else {
            commandName = command.type;
        }

        const notificationText = symbol ? `✓ ${symbol} (${commandName}) inserted` : `✓ ${commandName} inserted`;
        showVoiceNotification(notificationText, 'success', 1500);

        // Reset status after a short delay
        setTimeout(() => {
            if (typeof isRecording !== 'undefined' && isRecording) {
                updateVoiceStatus('listening');
            }
        }, 600);
    }
}

/**
 * Gets a user-friendly display name for symbol commands
 * @param {string} commandType - The command type
 * @returns {string} Display name for the symbol
 */
function getSymbolDisplayName(commandType) {
    const displayNames = {
        exclamationMark: 'Exclamation Mark',
        atSymbol: 'At Symbol',
        hashSymbol: 'Hash Symbol',
        dollarSign: 'Dollar Sign',
        percentSign: 'Percent Sign',
        openBrace: 'Open Brace',
        closeBrace: 'Close Brace',
        colon: 'Colon',
        semicolon: 'Semicolon',
        doubleQuote: 'Double Quote',
        openQuote: 'Open Quote',
        closeQuote: 'Close Quote',
        apostrophe: 'Apostrophe',
        // Basic punctuation
        period: 'Period',
        comma: 'Comma',
        questionMark: 'Question Mark',
        // Mathematical/Technical symbols
        plusSign: 'Plus Sign',
        minusSign: 'Minus Sign',
        equalsSign: 'Equals Sign',
        asterisk: 'Asterisk',
        forwardSlash: 'Forward Slash',
        backslash: 'Backslash',
        // Brackets
        openParenthesis: 'Open Parenthesis',
        closeParenthesis: 'Close Parenthesis',
        openBracket: 'Open Bracket',
        closeBracket: 'Close Bracket',
        // Additional symbols
        underscore: 'Underscore',
        pipe: 'Pipe',
        ampersand: 'Ampersand'
    };

    return displayNames[commandType] || commandType;
}

/**
 * Inserts text and symbol together immediately for Ctrl+voice commands
 * This solves the timing issue where insertAfterText hasn't been inserted into DOM yet
 * @param {string} textToInsert - The text to insert first
 * @param {string} symbol - The symbol to insert after the text
 * @param {string} commandType - The command type for context
 */
function insertTextAndSymbolImmediately(textToInsert, symbol, commandType) {
    if (!symbol || !textToInsert) {
        console.error('Stashy: Missing text or symbol for immediate insertion');
        return;
    }

    // CRITICAL FIX: Ensure noteText element is available and valid
    if (!noteText) {
        console.error('Stashy: noteText element not found - attempting to find it');
        noteText = document.querySelector('#Stashy-note-text') ||
                  document.querySelector('[contenteditable="true"]') ||
                  document.querySelector('.Stashy-note-text');

        if (!noteText) {
            console.error('Stashy: Could not find note text element for immediate insertion');
            return;
        }
        console.log('Stashy: Found note text element for immediate insertion:', noteText);
    }

    try {
        // Validate that noteText is still in the DOM
        if (!document.contains(noteText)) {
            console.error('Stashy: noteText element is not in the DOM');
            return;
        }

        // Determine spacing for the symbol
        const spacingInfo = getSymbolSpacingFallback(symbol, commandType);

        // Create the combined text with symbol
        let combinedText = textToInsert;

        // Add symbol with appropriate spacing
        if (spacingInfo.spaceBefore) {
            combinedText += ' ' + symbol;
        } else {
            combinedText += symbol;
        }

        if (spacingInfo.spaceAfter) {
            combinedText += ' ';
        }

        // Insert the combined text at cursor position
        if (voiceInsertionPoint && voiceInsertionPoint.startContainer) {
            // Insert at saved cursor position
            const range = document.createRange();
            range.setStart(voiceInsertionPoint.startContainer, voiceInsertionPoint.startOffset);
            range.setEnd(voiceInsertionPoint.endContainer, voiceInsertionPoint.endOffset);

            // Delete any selected content first
            range.deleteContents();

            // Insert the combined text
            const textNode = document.createTextNode(combinedText);
            range.insertNode(textNode);

            // Position cursor after the inserted content
            range.setStartAfter(textNode);
            range.setEndAfter(textNode);

            // Update selection
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            // Update saved cursor position
            voiceInsertionPoint = {
                startContainer: range.startContainer,
                startOffset: range.startOffset,
                endContainer: range.endContainer,
                endOffset: range.endOffset,
                isAtEnd: false
            };

            console.log(`Stashy: Combined text and symbol "${combinedText}" inserted at cursor position`);

        } else {
            // Fallback: append at end
            if (noteText.innerHTML.length > 0 && !/[\s\>]$/.test(noteText.innerHTML)) {
                combinedText = ' ' + combinedText;
            }

            const textNode = document.createTextNode(combinedText);
            noteText.appendChild(textNode);
            console.log(`Stashy: Combined text and symbol "${combinedText}" appended at end (fallback)`);
        }

        // Schedule save
        scheduleSave();

    } catch (e) {
        console.error(`Stashy: Error inserting text and symbol immediately:`, e);
        // Fallback to separate insertion
        try {
            insertTextFallback(textToInsert);
            setTimeout(() => {
                insertSymbolAtCursor(symbol, commandType);
            }, 10);
        } catch (fallbackError) {
            console.error(`Stashy: Fallback insertion also failed:`, fallbackError);
        }
    }
}

/**
 * Inserts a symbol at a specific position relative to recently inserted text
 * @param {string} symbol - The symbol to insert
 * @param {string} commandType - The command type for context
 * @param {string} insertAfterText - The text after which to insert the symbol
 */
function insertSymbolAtSpecificPosition(symbol, commandType, insertAfterText) {
    if (!noteText || !symbol) return;

    try {
        // Find the position where we should insert the symbol
        // This should be after the text that was specified in insertAfterText
        const noteContent = noteText.textContent || noteText.innerText || '';

        if (insertAfterText && noteContent.includes(insertAfterText)) {
            // Find the end of the insertAfterText in the note
            const textIndex = noteContent.lastIndexOf(insertAfterText);
            if (textIndex !== -1) {
                const insertPosition = textIndex + insertAfterText.length;

                // Create a range at the correct position
                const range = document.createRange();
                const walker = document.createTreeWalker(
                    noteText,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                let currentPos = 0;
                let targetNode = null;
                let targetOffset = 0;

                // Walk through text nodes to find the correct insertion point
                while (walker.nextNode()) {
                    const node = walker.currentNode;
                    const nodeLength = node.textContent.length;

                    if (currentPos + nodeLength >= insertPosition) {
                        targetNode = node;
                        targetOffset = insertPosition - currentPos;
                        break;
                    }
                    currentPos += nodeLength;
                }

                if (targetNode) {
                    range.setStart(targetNode, targetOffset);
                    range.setEnd(targetNode, targetOffset);

                    // Determine spacing based on symbol type and context
                    const spacingInfo = getSymbolSpacingForPosition(symbol, commandType, range);

                    // Create the text to insert with appropriate spacing
                    let textToInsert = symbol;
                    if (spacingInfo.spaceBefore) {
                        textToInsert = ' ' + textToInsert;
                    }
                    if (spacingInfo.spaceAfter) {
                        textToInsert = textToInsert + ' ';
                    }

                    // Insert the symbol
                    const textNode = document.createTextNode(textToInsert);
                    range.insertNode(textNode);

                    // Position cursor after the inserted symbol
                    range.setStartAfter(textNode);
                    range.setEndAfter(textNode);

                    // Update selection
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);

                    console.log(`Stashy: Symbol "${symbol}" inserted after "${insertAfterText}" at position ${insertPosition}`);

                    // Schedule save
                    scheduleSave();
                    return;
                }
            }
        }

        // Fallback to regular cursor position insertion
        insertSymbolAtCursor(symbol, commandType);

    } catch (e) {
        console.warn(`Stashy: Error inserting symbol "${symbol}" at specific position:`, e);
        // Fallback to regular cursor position insertion
        insertSymbolAtCursor(symbol, commandType);
    }
}

/**
 * Inserts a symbol at a specific position with validation and success reporting
 * @param {string} symbol - The symbol to insert
 * @param {string} commandType - The command type for context
 * @param {string} insertAfterText - The text after which to insert the symbol
 * @returns {boolean} True if insertion was successful, false otherwise
 */
function insertSymbolAtSpecificPositionWithValidation(symbol, commandType, insertAfterText) {
    if (!noteText || !symbol) return false;

    try {
        // Store initial content for validation
        const initialContent = noteText.textContent || noteText.innerText || '';

        // Find the position where we should insert the symbol
        if (insertAfterText && initialContent.includes(insertAfterText)) {
            // Find the end of the insertAfterText in the note
            const textIndex = initialContent.lastIndexOf(insertAfterText);
            if (textIndex !== -1) {
                const insertPosition = textIndex + insertAfterText.length;

                // Create a range at the correct position
                const range = document.createRange();
                const walker = document.createTreeWalker(
                    noteText,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                let currentPos = 0;
                let targetNode = null;
                let targetOffset = 0;

                // Walk through text nodes to find the correct insertion point
                while (walker.nextNode()) {
                    const node = walker.currentNode;
                    const nodeLength = node.textContent.length;

                    if (currentPos + nodeLength >= insertPosition) {
                        targetNode = node;
                        targetOffset = insertPosition - currentPos;
                        break;
                    }
                    currentPos += nodeLength;
                }

                if (targetNode) {
                    range.setStart(targetNode, targetOffset);
                    range.setEnd(targetNode, targetOffset);

                    // Determine spacing based on symbol type and context
                    const spacingInfo = getSymbolSpacingForPosition(symbol, commandType, range);

                    // Create the text to insert with appropriate spacing
                    let textToInsert = symbol;
                    if (spacingInfo.spaceBefore) {
                        textToInsert = ' ' + textToInsert;
                    }
                    if (spacingInfo.spaceAfter) {
                        textToInsert = textToInsert + ' ';
                    }

                    // Insert the symbol
                    const textNode = document.createTextNode(textToInsert);
                    range.insertNode(textNode);

                    // Position cursor after the inserted symbol
                    range.setStartAfter(textNode);
                    range.setEndAfter(textNode);

                    // Update selection
                    const selection = window.getSelection();
                    selection.removeAllRanges();
                    selection.addRange(range);

                    // Validate insertion by checking if content changed
                    const finalContent = noteText.textContent || noteText.innerText || '';
                    const insertionSuccessful = finalContent.includes(symbol) && finalContent !== initialContent;

                    if (insertionSuccessful) {
                        console.log(`Stashy: Symbol "${symbol}" successfully inserted after "${insertAfterText}" at position ${insertPosition}`);
                        scheduleSave();
                        return true;
                    } else {
                        console.warn(`Stashy: Symbol "${symbol}" insertion validation failed`);
                        return false;
                    }
                }
            }
        }

        // Fallback to regular cursor position insertion
        const fallbackSuccess = insertSymbolAtCursorWithValidation(symbol, commandType);
        if (fallbackSuccess) {
            console.log(`Stashy: Symbol "${symbol}" inserted using fallback method`);
            return true;
        }

        return false;

    } catch (e) {
        console.warn(`Stashy: Error inserting symbol "${symbol}" at specific position:`, e);
        // Try fallback method
        try {
            return insertSymbolAtCursorWithValidation(symbol, commandType);
        } catch (fallbackError) {
            console.error(`Stashy: Fallback insertion also failed:`, fallbackError);
            return false;
        }
    }
}

/**
 * Inserts a symbol at cursor position with validation
 * @param {string} symbol - The symbol to insert
 * @param {string} commandType - The command type for context
 * @returns {boolean} True if insertion was successful, false otherwise
 */
function insertSymbolAtCursorWithValidation(symbol, commandType) {
    if (!noteText || !symbol) return false;

    try {
        // Store initial content for validation
        const initialContent = noteText.textContent || noteText.innerText || '';

        // Use the existing insertSymbolAtCursor function
        insertSymbolAtCursor(symbol, commandType);

        // Validate insertion by checking if content changed
        const finalContent = noteText.textContent || noteText.innerText || '';
        const insertionSuccessful = finalContent.includes(symbol) && finalContent !== initialContent;

        return insertionSuccessful;

    } catch (e) {
        console.warn(`Stashy: Error in cursor-based symbol insertion validation:`, e);
        return false;
    }
}

/**
 * Determines spacing for symbols at a specific position
 * @param {string} symbol - The symbol being inserted
 * @param {string} commandType - The command type
 * @param {Range} range - The insertion range
 * @returns {Object} Spacing information
 */
function getSymbolSpacingForPosition(symbol, commandType, range) {
    // For positioned symbols, we typically don't want space before punctuation
    // but may want space after depending on what follows
    const spacing = { spaceBefore: false, spaceAfter: false };

    try {
        // Check character after insertion point
        const afterRange = document.createRange();
        afterRange.setStart(range.endContainer, range.endOffset);
        afterRange.setEnd(range.endContainer, Math.min(range.endContainer.textContent?.length || 0, range.endOffset + 1));
        const afterText = afterRange.toString();

        // Apply spacing rules based on symbol type
        switch (commandType) {
            // Basic punctuation - no space before, space after if needed
            case 'period':
            case 'comma':
            case 'exclamationMark':
            case 'questionMark':
            case 'colon':
            case 'semicolon':
                spacing.spaceBefore = false;
                spacing.spaceAfter = afterText.length > 0 && !/^\s/.test(afterText);
                break;

            // Mathematical operators - space before and after
            case 'plusSign':
            case 'minusSign':
            case 'equalsSign':
            case 'asterisk':
                spacing.spaceBefore = true;
                spacing.spaceAfter = afterText.length > 0 && !/^\s/.test(afterText);
                break;

            // Closing symbols - no space before, space after if needed
            case 'closeParenthesis':
            case 'closeBracket':
            case 'closeBrace':
                spacing.spaceBefore = false;
                spacing.spaceAfter = afterText.length > 0 && !/^\s/.test(afterText);
                break;

            // Opening symbols - space before, no space after
            case 'openParenthesis':
            case 'openBracket':
            case 'openBrace':
                spacing.spaceBefore = true;
                spacing.spaceAfter = false;
                break;

            // Other symbols - context dependent
            default:
                spacing.spaceBefore = false;
                spacing.spaceAfter = afterText.length > 0 && !/^\s/.test(afterText);
                break;
        }

    } catch (e) {
        console.warn('Stashy: Error determining positioned symbol spacing:', e);
        // Conservative fallback
        spacing.spaceBefore = false;
        spacing.spaceAfter = false;
    }

    return spacing;
}

/**
 * Inserts a symbol at the current cursor position with intelligent spacing
 * @param {string} symbol - The symbol to insert
 * @param {string} commandType - The command type for context
 */
function insertSymbolAtCursor(symbol, commandType) {
    if (!noteText || !symbol) return;

    try {
        if (voiceInsertionPoint && voiceInsertionPoint.startContainer) {
            // Insert at saved cursor position
            const range = document.createRange();
            range.setStart(voiceInsertionPoint.startContainer, voiceInsertionPoint.startOffset);
            range.setEnd(voiceInsertionPoint.endContainer, voiceInsertionPoint.endOffset);

            // Delete any selected content first
            range.deleteContents();

            // Determine spacing based on symbol type and context
            const spacingInfo = getSymbolSpacing(symbol, commandType, range);

            // Create the text to insert with appropriate spacing
            let textToInsert = symbol;
            if (spacingInfo.spaceBefore) {
                textToInsert = ' ' + textToInsert;
            }
            if (spacingInfo.spaceAfter) {
                textToInsert = textToInsert + ' ';
            }

            // Insert the symbol with spacing
            const textNode = document.createTextNode(textToInsert);
            range.insertNode(textNode);

            // Position cursor after the inserted symbol
            range.setStartAfter(textNode);
            range.setEndAfter(textNode);

            // Update selection
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            // Update saved cursor position
            voiceInsertionPoint = {
                startContainer: range.startContainer,
                startOffset: range.startOffset,
                endContainer: range.endContainer,
                endOffset: range.endOffset,
                isAtEnd: false
            };

            console.log(`Stashy: Symbol "${symbol}" inserted at cursor position`);

        } else {
            // Fallback: append symbol at end
            const spacingInfo = getSymbolSpacingFallback(symbol, commandType);
            let textToInsert = symbol;

            if (spacingInfo.spaceBefore && noteText.innerHTML.length > 0) {
                textToInsert = ' ' + textToInsert;
            }
            if (spacingInfo.spaceAfter) {
                textToInsert = textToInsert + ' ';
            }

            const textNode = document.createTextNode(textToInsert);
            noteText.appendChild(textNode);
            console.log(`Stashy: Symbol "${symbol}" appended at end (fallback)`);
        }

        // Schedule save
        scheduleSave();

    } catch (e) {
        console.warn(`Stashy: Error inserting symbol "${symbol}":`, e);
    }
}

/**
 * Determines appropriate spacing for a symbol based on context
 * @param {string} symbol - The symbol being inserted
 * @param {string} commandType - The command type
 * @param {Range} range - The insertion range
 * @returns {Object} Spacing information
 */
function getSymbolSpacing(symbol, commandType, range) {
    const spacing = { spaceBefore: false, spaceAfter: false };

    try {
        // Check character before insertion point
        const beforeRange = document.createRange();
        beforeRange.setStart(range.startContainer, Math.max(0, range.startOffset - 1));
        beforeRange.setEnd(range.startContainer, range.startOffset);
        const beforeText = beforeRange.toString();

        // Check character after insertion point
        const afterRange = document.createRange();
        afterRange.setStart(range.endContainer, range.endOffset);
        afterRange.setEnd(range.endContainer, Math.min(range.endContainer.textContent?.length || 0, range.endOffset + 1));
        const afterText = afterRange.toString();

        // Apply spacing rules based on symbol type
        switch (commandType) {
            // Basic punctuation - no space before, space after if needed
            case 'period':
            case 'comma':
            case 'exclamationMark':
            case 'questionMark':
                spacing.spaceBefore = false;
                spacing.spaceAfter = afterText.length > 0 && !/^\s/.test(afterText);
                break;

            // Mathematical operators - space before and after
            case 'plusSign':
            case 'minusSign':
            case 'equalsSign':
            case 'asterisk':
                spacing.spaceBefore = beforeText.length > 0 && !/\s$/.test(beforeText);
                spacing.spaceAfter = afterText.length > 0 && !/^\s/.test(afterText);
                break;

            // Slashes - context dependent, usually no spaces for paths/URLs
            case 'forwardSlash':
            case 'backslash':
                spacing.spaceBefore = false;
                spacing.spaceAfter = false;
                break;

            // Opening brackets/braces - space before, no space after
            case 'openParenthesis':
            case 'openBracket':
            case 'openBrace':
            case 'openQuote':
                spacing.spaceBefore = beforeText.length > 0 && !/\s$/.test(beforeText);
                spacing.spaceAfter = false;
                break;

            // Closing brackets/braces - no space before, space after if needed
            case 'closeParenthesis':
            case 'closeBracket':
            case 'closeBrace':
            case 'closeQuote':
                spacing.spaceBefore = false;
                spacing.spaceAfter = afterText.length > 0 && !/^\s/.test(afterText);
                break;

            // Standalone symbols - space before and after
            case 'atSymbol':
            case 'hashSymbol':
            case 'dollarSign':
            case 'percentSign':
            case 'ampersand':
            case 'pipe':
                spacing.spaceBefore = beforeText.length > 0 && !/\s$/.test(beforeText);
                spacing.spaceAfter = afterText.length > 0 && !/^\s/.test(afterText);
                break;

            // Underscore - typically no spaces (for identifiers)
            case 'underscore':
                spacing.spaceBefore = false;
                spacing.spaceAfter = false;
                break;

            // Colons and semicolons - no space before, space after
            case 'colon':
            case 'semicolon':
                spacing.spaceBefore = false;
                spacing.spaceAfter = afterText.length > 0 && !/^\s/.test(afterText);
                break;

            // Quotes and apostrophes - context-dependent
            case 'doubleQuote':
            case 'apostrophe':
                spacing.spaceBefore = beforeText.length > 0 && !/\s$/.test(beforeText);
                spacing.spaceAfter = afterText.length > 0 && !/^\s/.test(afterText);
                break;

            default:
                // Default spacing for unknown symbols
                spacing.spaceBefore = beforeText.length > 0 && !/\s$/.test(beforeText);
                spacing.spaceAfter = afterText.length > 0 && !/^\s/.test(afterText);
                break;
        }

    } catch (e) {
        console.warn('Stashy: Error determining symbol spacing:', e);
        // Fallback to conservative spacing
        spacing.spaceBefore = range.startOffset > 0;
        spacing.spaceAfter = false;
    }

    return spacing;
}

/**
 * Determines spacing for symbols in fallback mode (appending at end)
 * @param {string} symbol - The symbol being inserted
 * @param {string} commandType - The command type
 * @returns {Object} Spacing information
 */
function getSymbolSpacingFallback(symbol, commandType) {
    const spacing = { spaceBefore: false, spaceAfter: false };

    // In fallback mode, we typically want space before most symbols
    // but not after (since we're at the end)
    switch (commandType) {
        // Basic punctuation - no space before (attach to previous word)
        case 'period':
        case 'comma':
        case 'exclamationMark':
        case 'questionMark':
        case 'colon':
        case 'semicolon':
            spacing.spaceBefore = false;
            spacing.spaceAfter = false;
            break;

        // Closing symbols - no space before
        case 'closeParenthesis':
        case 'closeBracket':
        case 'closeBrace':
        case 'closeQuote':
            spacing.spaceBefore = false;
            spacing.spaceAfter = false;
            break;

        // Opening symbols - space before
        case 'openParenthesis':
        case 'openBracket':
        case 'openBrace':
        case 'openQuote':
            spacing.spaceBefore = true;
            spacing.spaceAfter = false;
            break;

        // Slashes and underscore - no spaces (for paths/identifiers)
        case 'forwardSlash':
        case 'backslash':
        case 'underscore':
            spacing.spaceBefore = false;
            spacing.spaceAfter = false;
            break;

        // Mathematical operators and standalone symbols - space before
        case 'plusSign':
        case 'minusSign':
        case 'equalsSign':
        case 'asterisk':
        case 'atSymbol':
        case 'hashSymbol':
        case 'dollarSign':
        case 'percentSign':
        case 'ampersand':
        case 'pipe':
            spacing.spaceBefore = true;
            spacing.spaceAfter = false;
            break;

        // Quotes and apostrophes - context dependent, default to space before
        case 'doubleQuote':
        case 'apostrophe':
            spacing.spaceBefore = true;
            spacing.spaceAfter = false;
            break;

        default:
            spacing.spaceBefore = true;
            spacing.spaceAfter = false;
            break;
    }

    return spacing;
}

/**
 * Inserts a line break at the current cursor position
 */
function insertLineBreakAtCursor() {
    if (!noteText) return;

    try {
        if (voiceInsertionPoint && voiceInsertionPoint.startContainer) {
            // Insert at saved cursor position
            const range = document.createRange();
            range.setStart(voiceInsertionPoint.startContainer, voiceInsertionPoint.startOffset);
            range.setEnd(voiceInsertionPoint.endContainer, voiceInsertionPoint.endOffset);

            // Delete any selected content first
            range.deleteContents();

            // Create line break element
            const lineBreak = document.createElement('br');
            range.insertNode(lineBreak);

            // Position cursor after the line break
            range.setStartAfter(lineBreak);
            range.setEndAfter(lineBreak);

            // Update selection
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            // Update saved cursor position
            voiceInsertionPoint = {
                startContainer: range.startContainer,
                startOffset: range.startOffset,
                endContainer: range.endContainer,
                endOffset: range.endOffset,
                isAtEnd: false
            };

            console.log('Stashy: Line break inserted at cursor position');

        } else {
            // Fallback: append line break at end
            const lineBreak = document.createElement('br');
            noteText.appendChild(lineBreak);
            console.log('Stashy: Line break appended at end (fallback)');
        }

        // Schedule save
        scheduleSave();

    } catch (e) {
        console.warn('Stashy: Error inserting line break:', e);
    }
}

/**
 * Inserts a paragraph break (double line break) at the current cursor position
 */
function insertParagraphBreakAtCursor() {
    if (!noteText) return;

    try {
        if (voiceInsertionPoint && voiceInsertionPoint.startContainer) {
            // Insert at saved cursor position
            const range = document.createRange();
            range.setStart(voiceInsertionPoint.startContainer, voiceInsertionPoint.startOffset);
            range.setEnd(voiceInsertionPoint.endContainer, voiceInsertionPoint.endOffset);

            // Delete any selected content first
            range.deleteContents();

            // Create double line break for paragraph
            const lineBreak1 = document.createElement('br');
            const lineBreak2 = document.createElement('br');

            range.insertNode(lineBreak1);
            range.setStartAfter(lineBreak1);
            range.insertNode(lineBreak2);

            // Position cursor after the second line break
            range.setStartAfter(lineBreak2);
            range.setEndAfter(lineBreak2);

            // Update selection
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);

            // Update saved cursor position
            voiceInsertionPoint = {
                startContainer: range.startContainer,
                startOffset: range.startOffset,
                endContainer: range.endContainer,
                endOffset: range.endOffset,
                isAtEnd: false
            };

            console.log('Stashy: Paragraph break inserted at cursor position');

        } else {
            // Fallback: append paragraph break at end
            const lineBreak1 = document.createElement('br');
            const lineBreak2 = document.createElement('br');
            noteText.appendChild(lineBreak1);
            noteText.appendChild(lineBreak2);
            console.log('Stashy: Paragraph break appended at end (fallback)');
        }

        // Schedule save
        scheduleSave();

    } catch (e) {
        console.warn('Stashy: Error inserting paragraph break:', e);
    }
}

/**
 * Appends interim transcript at the end of the note
 * @param {HTMLElement} element - The interim transcript element to append
 * @param {boolean} needsSpace - Whether to add a space before the element
 */
function appendInterimAtEnd(element, needsSpace) {
    if (needsSpace) {
        const spaceNode = document.createTextNode(' ');
        noteText.appendChild(spaceNode);
    }
    noteText.appendChild(element);
}

/**
 * Inserts text at the saved cursor position with proper spacing
 * @param {string} textToInsert - The text to insert
 */
function insertTextAtCursorPosition(textToInsert) {
    if (!voiceInsertionPoint || !voiceInsertionPoint.startContainer || !noteText) {
        insertTextFallback(textToInsert);
        return;
    }

    // Special handling for YouTube to bypass keyboard isolation
    const isYouTube = window.location.hostname.includes('youtube.com');
    if (isYouTube) {
        console.log("Stashy: Voice typing on YouTube - using enhanced insertion method");
    }

    try {
        // Create a range at the saved cursor position
        const range = document.createRange();
        range.setStart(voiceInsertionPoint.startContainer, voiceInsertionPoint.startOffset);
        range.setEnd(voiceInsertionPoint.endContainer, voiceInsertionPoint.endOffset);

        // Delete any selected content first
        range.deleteContents();

        // Determine if we need spacing
        const needsSpaceBefore = shouldAddSpaceBefore(range);
        const needsSpaceAfter = shouldAddSpaceAfter(range, textToInsert);

        // Create the text to insert with appropriate spacing
        let finalText = textToInsert;
        if (needsSpaceBefore) {
            finalText = ' ' + finalText;
        }
        if (needsSpaceAfter) {
            finalText = finalText + ' ';
        }

        // Insert the text
        const textNode = document.createTextNode(finalText);
        range.insertNode(textNode);

        // Position cursor after the inserted text
        range.setStartAfter(textNode);
        range.setEndAfter(textNode);

        // Update the selection to the new cursor position
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);

        // Update the saved cursor position for any subsequent insertions
        voiceInsertionPoint = {
            startContainer: range.startContainer,
            startOffset: range.startOffset,
            endContainer: range.endContainer,
            endOffset: range.endOffset,
            isAtEnd: false
        };

        console.log('Stashy: Text inserted at cursor position:', textToInsert);

    } catch (e) {
        console.warn('Stashy: Error inserting text at cursor position:', e);
        insertTextFallback(textToInsert);
    }
}

/**
 * Fallback method to insert text when cursor position insertion fails
 * @param {string} textToInsert - The text to insert
 */
function insertTextFallback(textToInsert) {
    if (!noteText) return;

    if (noteText.innerHTML.length === 0) {
        // If the note is empty, just set the content
        noteText.innerHTML = textToInsert;
    } else {
        // If there's existing content, append to it
        const currentContent = noteText.innerHTML;
        // Check if we need to add a space between existing content and new transcript
        if (!/[\s\>]$/.test(currentContent)) {
            noteText.innerHTML = currentContent + ' ' + textToInsert;
        } else {
            noteText.innerHTML = currentContent + textToInsert;
        }
    }

    console.log('Stashy: Text inserted using fallback method:', textToInsert);
}

/**
 * YouTube-optimized text insertion that bypasses keyboard isolation issues
 * @param {string} textToInsert - The text to insert
 */
function insertTextForYouTube(textToInsert) {
    if (!noteText) return;

    try {
        // Focus the note text area first to ensure it's active
        noteText.focus();

        // Use document.execCommand for more reliable insertion on YouTube
        if (document.execCommand && document.queryCommandSupported('insertText')) {
            const success = document.execCommand('insertText', false, textToInsert);
            if (success) {
                console.log('Stashy: Text inserted on YouTube using execCommand:', textToInsert);
                return;
            }
        }

        // Fallback to selection-based insertion
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            range.deleteContents();

            // Determine spacing
            const needsSpaceBefore = range.startOffset > 0 &&
                                   range.startContainer.textContent &&
                                   !/\s$/.test(range.startContainer.textContent.substring(0, range.startOffset));

            const finalText = needsSpaceBefore ? ' ' + textToInsert : textToInsert;
            const textNode = document.createTextNode(finalText);
            range.insertNode(textNode);

            // Move cursor after inserted text
            range.setStartAfter(textNode);
            range.setEndAfter(textNode);
            selection.removeAllRanges();
            selection.addRange(range);

            console.log('Stashy: Text inserted on YouTube using selection API:', textToInsert);
        } else {
            // Final fallback
            insertTextFallback(textToInsert);
        }

    } catch (error) {
        console.warn('Stashy: YouTube text insertion failed, using fallback:', error);
        insertTextFallback(textToInsert);
    }
}

/**
 * Determines if a space should be added before the inserted text
 * @param {Range} range - The range where text will be inserted
 * @returns {boolean} True if space should be added before
 */
function shouldAddSpaceBefore(range) {
    try {
        // Check the character immediately before the insertion point
        const beforeRange = document.createRange();
        beforeRange.setStart(range.startContainer, Math.max(0, range.startOffset - 1));
        beforeRange.setEnd(range.startContainer, range.startOffset);

        const beforeText = beforeRange.toString();

        // Add space if the previous character is not whitespace or if we're not at the beginning
        return beforeText.length > 0 && !/\s$/.test(beforeText);

    } catch (e) {
        // If we can't determine, err on the side of adding space
        return range.startOffset > 0;
    }
}

/**
 * Determines if a space should be added after the inserted text
 * @param {Range} range - The range where text will be inserted
 * @param {string} textToInsert - The text being inserted
 * @returns {boolean} True if space should be added after
 */
function shouldAddSpaceAfter(range, textToInsert) {
    try {
        // Check the character immediately after the insertion point
        const afterRange = document.createRange();
        afterRange.setStart(range.endContainer, range.endOffset);
        afterRange.setEnd(range.endContainer, Math.min(range.endContainer.textContent?.length || 0, range.endOffset + 1));

        const afterText = afterRange.toString();

        // Add space if the next character exists and is not whitespace, and our text doesn't end with punctuation
        return afterText.length > 0 && !/^\s/.test(afterText) && !/[.!?;,]\s*$/.test(textToInsert);

    } catch (e) {
        // If we can't determine, don't add space after
        return false;
    }
}

/**
 * Creates or updates the ARIA live region for screen reader accessibility
 * @param {string} message - The message to announce to screen readers
 */
function updateAriaLiveRegion(message) {
    if (!voiceAriaLiveRegion) {
        voiceAriaLiveRegion = document.createElement('div');
        voiceAriaLiveRegion.className = 'Stashy-voice-aria-live';
        voiceAriaLiveRegion.setAttribute('aria-live', 'polite');
        voiceAriaLiveRegion.setAttribute('aria-atomic', 'true');
        document.body.appendChild(voiceAriaLiveRegion);
    }

    voiceAriaLiveRegion.textContent = message;
}

/**
 * Creates or updates the voice status indicator
 * @param {string} status - Status: 'listening', 'processing', 'error', 'command', or 'active'
 */
function updateVoiceStatus(status) {
    const recordButton = document.getElementById(VOICE_RECORD_BTN_ID);
    if (!recordButton) return;

    if (!voiceStatusIndicator) {
        voiceStatusIndicator = document.createElement('div');
        voiceStatusIndicator.className = 'Stashy-voice-status';
        recordButton.style.position = 'relative';
        recordButton.appendChild(voiceStatusIndicator);
    }

    // Remove all status classes
    voiceStatusIndicator.classList.remove('listening', 'processing', 'error', 'command');

    // Add the current status class
    if (status !== 'active') {
        voiceStatusIndicator.classList.add(status);
    }
}

/**
 * Shows a non-intrusive notification for voice typing feedback
 * @param {string} message - The notification message
 * @param {string} type - Notification type: 'success', 'error', 'warning', 'info'
 * @param {number} duration - Duration in milliseconds (default: 3000)
 */
function showVoiceNotification(message, type = 'info', duration = 3000) {
    // Remove any existing notifications
    const existingNotifications = document.querySelectorAll('.Stashy-voice-notification');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `Stashy-voice-notification ${type}`;
    notification.textContent = message;
    notification.setAttribute('role', 'alert');
    notification.setAttribute('aria-live', 'assertive');

    document.body.appendChild(notification);

    // Auto-remove after duration
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'Stashy-notification-slide-out 0.3s ease-in forwards';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}

/**
 * Cleans up all voice typing UI elements and state
 */
function cleanupVoiceUI() {
    // Clear interim transcript
    clearInterimTranscript();

    // Remove voice status indicator
    if (voiceStatusIndicator && voiceStatusIndicator.parentNode) {
        voiceStatusIndicator.parentNode.removeChild(voiceStatusIndicator);
        voiceStatusIndicator = null;
    }

    // Clear ARIA live region
    if (voiceAriaLiveRegion && voiceAriaLiveRegion.parentNode) {
        voiceAriaLiveRegion.parentNode.removeChild(voiceAriaLiveRegion);
        voiceAriaLiveRegion = null;
    }

    // Reset state variables with memory optimization
    originalNoteContent = '';
    voiceInsertionPoint = null;
    lastInterimUpdate = 0;
    lastProcessedCommand = null; // Reset command tracking
    pendingVoiceCommands = []; // Clear any pending commands

    // Clear any compiled pattern caches if they exist
    if (typeof COMPILED_VOICE_PATTERNS !== 'undefined') {
        for (const patterns of Object.values(COMPILED_VOICE_PATTERNS)) {
            for (const pattern of patterns) {
                if (pattern.regex) {
                    pattern.regex.lastIndex = 0; // Reset regex state
                }
            }
        }
    }

    // Force garbage collection hint for large transcription sessions
    if (typeof window.gc === 'function') {
        window.gc();
    }
}

// Expose performance functions globally for debugging
window.getVoiceCommandPerformanceStats = getVoiceCommandPerformanceStats;
window.resetVoiceCommandPerformanceStats = resetVoiceCommandPerformanceStats;

// Expose modifier key functions globally
window.getVoiceModifierKeyConfig = getVoiceModifierKeyConfig;
window.updateVoiceModifierKeyConfig = updateVoiceModifierKeyConfig;


/**
 * Starts the voice recording process.
 * Requests microphone access, initializes SpeechRecognition, and sets up event listeners.
 */
function startRecording() {
    if (isRecording || !SpeechRecognition) return;
    const recordButton = document.getElementById(VOICE_RECORD_BTN_ID);
    if (!recordButton) {
        console.error("Stashy: Record button not found.");
        return;
    }

    // Ensure we have microphone access before starting SpeechRecognition
    navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
            // We got permission! Stop the track immediately as Web Speech API handles audio capture internally.
            stream.getTracks().forEach(track => track.stop());
            console.log("Stashy: Microphone access granted.");

            try {
                recognition = new SpeechRecognition();
                recognition.continuous = true; // Keep listening after pauses
                recognition.interimResults = true; // Get results as they come in
                recognition.lang = 'en-US'; // Configurable?
                // recognition.maxAlternatives = 1; // Usually only need the top hypothesis

                let finalTranscriptSegment = ''; // Holds final transcript part before inserting
                let silenceTimeout = null;
                const SILENCE_DELAY = 30000; // 30 seconds of silence stops recording

                // Function to stop recording due to prolonged silence
                const stopDueToSilence = () => {
                    console.log("Stashy: Stopping recording due to silence.");
                    alert("Stopping recording due to silence.");
                    stopRecording();
                };

                // Function to reset the silence timer
                const resetSilenceTimer = () => {
                    clearTimeout(silenceTimeout);
                    silenceTimeout = setTimeout(stopDueToSilence, SILENCE_DELAY);
                };

                recognition.onstart = () => {
                    console.log("Stashy: Voice recognition started.");
                    isRecording = true;
                    recordButton.classList.add('recording'); // Add class for CSS pulse/styling
                    const textSpan = recordButton.querySelector('.Stashy-text');
                    if (textSpan) textSpan.textContent = 'Stop';
                    resetSilenceTimer(); // Start silence timer

                    // Initialize modifier key system
                    initVoiceModifierKeySystem();

                    // Store original note content and cursor position for interim preview
                    originalNoteContent = noteText ? noteText.innerHTML : '';
                    voiceInsertionPoint = saveCurrentCursorPosition();

                    // Debug logging for cursor position
                    if (voiceInsertionPoint) {
                        console.log('Stashy: Saved cursor position:', {
                            offset: voiceInsertionPoint.startOffset,
                            isAtEnd: voiceInsertionPoint.isAtEnd,
                            containerType: voiceInsertionPoint.startContainer.nodeType === Node.TEXT_NODE ? 'text' : 'element'
                        });
                    } else {
                        console.log('Stashy: No cursor position saved, will append to end');
                    }

                    // Initialize voice UI enhancements
                    updateVoiceStatus('active');
                    updateAriaLiveRegion('Voice recording started. Speak now.');
                    showVoiceNotification('🎤 Voice recording started', 'success', 2000);
                };

                // Create variable to accumulate transcript segments
                let accumulatedTranscript = '';

                // Clear any existing pending transcript and timer
                pendingTranscript = '';
                if (insertionTimer) {
                    clearTimeout(insertionTimer);
                    insertionTimer = null;
                }

                // Clear any existing interim transcript
                clearInterimTranscript();

                recognition.onresult = (event) => {
                    resetSilenceTimer(); // Reset timer on any result (interim or final)
                    let interimTranscript = '';
                    let maxConfidence = 0;
                    finalTranscriptSegment = ''; // Reset segment for this event batch

                    for (let i = event.resultIndex; i < event.results.length; ++i) {
                        const result = event.results[i];
                        const transcript = result[0].transcript;
                        const confidence = result[0].confidence || 0.8; // Default confidence if not provided

                        if (result.isFinal) {
                            // Process voice commands in final transcript
                            const processed = processVoiceCommands(transcript);

                            // Execute immediate commands (line breaks) right away
                            if (processed.immediateCommands && processed.immediateCommands.length > 0) {
                                console.log(`Stashy: Executing ${processed.immediateCommands.length} immediate commands`);
                                executeVoiceCommands(processed.immediateCommands);
                            }

                            // Store delayed commands to execute after text insertion
                            if (processed.delayedCommands && processed.delayedCommands.length > 0) {
                                pendingVoiceCommands.push(...processed.delayedCommands);
                                console.log(`Stashy: Stored ${processed.delayedCommands.length} delayed commands for execution after text insertion`);
                            }

                            // Only add remaining text if there's any after command processing
                            if (processed.text.trim()) {
                                finalTranscriptSegment += processed.text + ' '; // Append final parts
                                // Add to accumulated transcript
                                accumulatedTranscript += processed.text + ' ';
                            }
                            updateVoiceStatus('processing');
                        } else {
                            // For interim results, also check for commands but don't execute them yet
                            const processed = processVoiceCommands(transcript);
                            if (processed.text.trim()) {
                                interimTranscript += processed.text; // Accumulate interim parts
                                maxConfidence = Math.max(maxConfidence, confidence);
                            }

                            // Show command feedback in interim if commands detected
                            const allCommands = [...(processed.immediateCommands || []), ...(processed.delayedCommands || [])];
                            if (allCommands.length > 0) {
                                const commandText = allCommands.map(cmd => `[${cmd.type}]`).join(' ');
                                interimTranscript += ` ${commandText}`;
                            }
                        }
                    }

                    // Accumulate final transcript segments instead of inserting immediately
                    if (finalTranscriptSegment.trim()) {
                        pendingTranscript += finalTranscriptSegment;

                        // Clear any existing timer and set a new one
                        clearTimeout(insertionTimer);
                        insertionTimer = setTimeout(insertAccumulatedText, INSERTION_DELAY);

                        finalTranscriptSegment = ''; // Clear after accumulation

                        // Update ARIA for final text
                        updateAriaLiveRegion(`Added: ${finalTranscriptSegment.trim()}`);
                    }

                    // Show interim results directly in the note text area with confidence
                    if (interimTranscript.trim()) {
                        showInterimTranscript(interimTranscript, maxConfidence);
                    } else {
                        clearInterimTranscript();
                    }
                };

                recognition.onerror = (event) => {
                    console.error("Stashy: Speech recognition error:", event.error, event.message);
                    clearTimeout(silenceTimeout); // Stop silence timer on error
                    updateVoiceStatus('error');

                    let userMessage = "Voice recording error: " + (event.message || event.error);
                    let notificationType = 'error';
                    let shouldStop = false;

                    // Provide more specific user messages based on error type
                    if (event.error === 'no-speech') {
                         userMessage = "No speech detected. Please speak clearly into your microphone.";
                         notificationType = 'warning';
                         resetSilenceTimer(); // Reset timer if we want to continue listening
                         updateVoiceStatus('listening');
                    } else if (event.error === 'audio-capture') {
                        userMessage = "Microphone error. Please check your microphone connection and browser permissions.";
                        shouldStop = true;
                    } else if (event.error === 'not-allowed') {
                        userMessage = "Microphone access denied. Please allow access in browser settings.";
                        shouldStop = true;
                    } else if (event.error === 'network') {
                         userMessage = "Network error during speech recognition. Please check your internet connection.";
                         notificationType = 'warning';
                         resetSilenceTimer();
                         updateVoiceStatus('listening');
                    } else if (event.error === 'aborted') {
                         userMessage = "Voice recording stopped.";
                         notificationType = 'info';
                         // No need to call stopRecording again if it was intentional.
                    } else {
                        shouldStop = true;
                    }

                    // Show non-intrusive notification instead of alert
                    showVoiceNotification(userMessage, notificationType, 4000);
                    updateAriaLiveRegion(userMessage);

                    if (shouldStop) {
                        stopRecording();
                    }
                };

                recognition.onend = () => {
                    console.log("Stashy: Speech recognition service ended.");
                    clearTimeout(silenceTimeout); // Clear timer on end
                    // This event fires when stop() is called OR if it ends naturally/unexpectedly.
                    // Only update state/UI if stopRecording wasn't the trigger.
                    if (isRecording) {
                        console.log("Stashy: Recognition ended unexpectedly.");
                        alert("Voice recording stopped unexpectedly.");
                        isRecording = false; // Ensure state is correct
                        updateRecordButtonUI(false); // Update UI
                        scheduleSave(); // Save any last bits
                    }
                };

                recognition.start(); // Start the recognition service

            } catch (e) {
                console.error("Stashy: Failed to initialize SpeechRecognition:", e);
                alert("Could not start voice recording. An internal error occurred.");
                if (isRecording) stopRecording(); // Ensure cleanup if partially started
            }

        })
        .catch(err => {
            console.error("Stashy: Microphone access failed:", err);
            let userMessage = "Could not access microphone.";
            if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
                userMessage = "Microphone permission denied. Please allow access in browser settings.";
            } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
                userMessage = "No microphone found. Please ensure one is connected and enabled.";
            } else {
                userMessage = `Could not access microphone: ${err.message}`;
            }
            alert(userMessage);
            updateRecordButtonUI(false); // Ensure button UI is reset
        });
}

/**
 * Stops the active voice recording session and cleans up resources.
 */
function stopRecording() {
    const wasRecording = isRecording; // Capture state before modification
    isRecording = false; // Set state immediately

    // Clear any pending insertion timers and insert remaining text immediately
    if (insertionTimer) {
        clearTimeout(insertionTimer);
        insertionTimer = null;
    }
    // Force insert any pending transcript immediately when stopping
    insertAccumulatedText();

    // Clean up enhanced voice UI
    cleanupVoiceUI();

    if (recognition) {
        try {
            // Detach listeners to prevent potential late-arriving events after stop() is called
            recognition.onstart = null;
            recognition.onresult = null;
            recognition.onerror = null;
            recognition.onend = null; // Very important to prevent onend calling stopRecording again

            recognition.stop();
            console.log("Stashy: Speech recognition stop explicitly requested.");
        } catch (e) {
            console.warn("Stashy: Error trying to stop recognition instance:", e);
        } finally {
            recognition = null; // Release the instance
        }
    }

    // Always update UI regardless of whether recognition existed
    updateRecordButtonUI(false);

    if (wasRecording) {
        console.log("Stashy: Voice recording stopped by user or timeout.");
        scheduleSave(); // Ensure any final transcribed text is saved

        // Show completion notification
        showVoiceNotification('🎤 Voice recording stopped', 'success', 2000);
        updateAriaLiveRegion('Voice recording stopped.');
    }
}


/**
 * Helper function to update the record button's appearance.
 * @param {boolean} recording - True if recording is active, false otherwise.
 */
function updateRecordButtonUI(recording) {
     const recordButton = document.getElementById(VOICE_RECORD_BTN_ID);
     if (recordButton) {
         recordButton.classList.toggle('recording', recording);
         const textSpan = recordButton.querySelector('.Stashy-text');
         const defaultText = 'Record';
         const activeText = 'Stop';
         if (textSpan) {
            textSpan.textContent = recording ? activeText : defaultText;
         } else {
             // Fallback if spans aren't used (should match createToolButton)
             recordButton.innerHTML = `🎤 ${recording ? activeText : defaultText}`;
         }
         recordButton.title = recording ? 'Stop voice recording' : 'Start voice recording';
         recordButton.setAttribute('aria-label', recording ? 'Stop voice recording' : 'Start voice recording');
     }
}

// Initialize the voice modifier key system
if (typeof initVoiceModifierKeySystem === 'function') {
    initVoiceModifierKeySystem();
    console.log("Stashy: Voice modifier key system initialized");
} else {
    console.warn("Stashy: initVoiceModifierKeySystem function not found");
}

console.log("Stashy: Voice Logic Loaded");