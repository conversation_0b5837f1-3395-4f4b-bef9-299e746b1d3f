/**
 * Stashy Compression Worker
 * Handles compression and decompression operations in a background thread
 * to prevent UI blocking
 */

// Set up worker context
self.isWorkerContext = true;

// Libraries for compression algorithms
let libraries = {
    lzma: null,
    brotli: null,
    zstd: null,
    lz4: null,
    deflate: null,
    lzString: null
};

// Compression algorithms
const ALGORITHMS = {
    LZMA: 'lzma',
    BROTLI: 'brotli',
    ZSTD: 'zstd',
    LZ4: 'lz4',
    DEFLATE: 'deflate',
    LZ_STRING: 'lz-string',
    NONE: 'none'
};

// Send ready status to main thread
self.postMessage({ status: 'ready' });

// Set up message handler
self.onmessage = function(event) {
    const { taskId, action, data } = event.data;

    try {
        // Process the task based on the action
        switch (action) {
            case 'compressData':
                handleCompressData(taskId, data);
                break;

            case 'decompressData':
                handleDecompressData(taskId, data);
                break;

            case 'loadLibraries':
                handleLoadLibraries(taskId, data);
                break;

            default:
                throw new Error(`Unknown action: ${action}`);
        }
    } catch (error) {
        // Send error back to main thread
        self.postMessage({
            taskId,
            error: error.message || 'Unknown error in compression worker'
        });
    }
};

/**
 * Loads compression libraries (local only)
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The libraries to load
 */
async function handleLoadLibraries(taskId, data) {
    try {
        // SECURITY FIX: Only check for locally available libraries
        // No external library loading allowed

        const results = {};

        // Check for locally available libraries
        results.lzma = !!self.LZMA;
        results.brotli = !!self.Brotli;
        results.zstd = !!self.Zstd;
        results.lz4 = !!self.LZ4;
        results.deflate = !!self.pako;
        results.lzString = !!self.LZString;

        // Update libraries object with locally available libraries
        libraries.lzma = self.LZMA || null;
        libraries.brotli = self.Brotli || null;
        libraries.zstd = self.Zstd || null;
        libraries.lz4 = self.LZ4 || null;
        libraries.deflate = self.pako || null;
        libraries.lzString = self.LZString || null;

        // Send result back to main thread
        self.postMessage({
            taskId,
            result: {
                loaded: results,
                available: {
                    lzma: !!libraries.lzma,
                    brotli: !!libraries.brotli,
                    zstd: !!libraries.zstd,
                    lz4: !!libraries.lz4,
                    deflate: !!libraries.deflate,
                    lzString: !!libraries.lzString
                }
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Error checking libraries: ${error.message}`
        });
    }
}

/**
 * Compresses data using the specified algorithm
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data to compress
 */
async function handleCompressData(taskId, data) {
    try {
        const { input, algorithm, level = 5, options = {} } = data;

        // Check if the algorithm is available
        if (algorithm !== ALGORITHMS.NONE && !isAlgorithmAvailable(algorithm)) {
            throw new Error(`Compression algorithm not available: ${algorithm}`);
        }

        // Compress the data
        let compressed;
        let originalSize = input.length;
        let compressedSize;

        switch (algorithm) {
            case ALGORITHMS.LZMA:
                compressed = await compressWithLZMA(input, level);
                compressedSize = compressed.length;
                break;

            case ALGORITHMS.BROTLI:
                compressed = await compressWithBrotli(input, level);
                compressedSize = compressed.length;
                break;

            case ALGORITHMS.ZSTD:
                compressed = await compressWithZstd(input, level);
                compressedSize = compressed.length;
                break;

            case ALGORITHMS.LZ4:
                compressed = await compressWithLZ4(input);
                compressedSize = compressed.length;
                break;

            case ALGORITHMS.DEFLATE:
                compressed = await compressWithDeflate(input, level);
                compressedSize = compressed.length;
                break;

            case ALGORITHMS.LZ_STRING:
                compressed = libraries.lzString.compressToUTF16(input);
                compressedSize = compressed.length * 2; // UTF-16 uses 2 bytes per character
                break;

            case ALGORITHMS.NONE:
            default:
                compressed = input;
                compressedSize = originalSize;
                break;
        }

        // Calculate compression ratio
        const compressionRatio = originalSize / compressedSize;

        // Send result back to main thread
        self.postMessage({
            taskId,
            result: {
                compressed,
                originalSize,
                compressedSize,
                compressionRatio,
                algorithm
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Compression error: ${error.message}`
        });
    }
}

/**
 * Decompresses data using the specified algorithm
 * @param {string} taskId - The ID of the task
 * @param {Object} data - The data to decompress
 */
async function handleDecompressData(taskId, data) {
    try {
        const { input, algorithm, options = {} } = data;

        // Check if the algorithm is available
        if (algorithm !== ALGORITHMS.NONE && !isAlgorithmAvailable(algorithm)) {
            throw new Error(`Decompression algorithm not available: ${algorithm}`);
        }

        // Decompress the data
        let decompressed;

        switch (algorithm) {
            case ALGORITHMS.LZMA:
                decompressed = await decompressWithLZMA(input);
                break;

            case ALGORITHMS.BROTLI:
                decompressed = await decompressWithBrotli(input);
                break;

            case ALGORITHMS.ZSTD:
                decompressed = await decompressWithZstd(input);
                break;

            case ALGORITHMS.LZ4:
                decompressed = await decompressWithLZ4(input);
                break;

            case ALGORITHMS.DEFLATE:
                decompressed = await decompressWithDeflate(input);
                break;

            case ALGORITHMS.LZ_STRING:
                decompressed = libraries.lzString.decompressFromUTF16(input);
                break;

            case ALGORITHMS.NONE:
            default:
                decompressed = input;
                break;
        }

        // Send result back to main thread
        self.postMessage({
            taskId,
            result: {
                decompressed,
                success: true
            }
        });
    } catch (error) {
        self.postMessage({
            taskId,
            error: `Decompression error: ${error.message}`
        });
    }
}

/**
 * Checks if an algorithm is available
 * @param {string} algorithm - The algorithm to check
 * @returns {boolean} True if the algorithm is available
 */
function isAlgorithmAvailable(algorithm) {
    switch (algorithm) {
        case ALGORITHMS.LZMA:
            return !!libraries.lzma;
        case ALGORITHMS.BROTLI:
            return !!libraries.brotli;
        case ALGORITHMS.ZSTD:
            return !!libraries.zstd;
        case ALGORITHMS.LZ4:
            return !!libraries.lz4;
        case ALGORITHMS.DEFLATE:
            return !!libraries.deflate;
        case ALGORITHMS.LZ_STRING:
            return !!libraries.lzString;
        case ALGORITHMS.NONE:
            return true;
        default:
            return false;
    }
}

/**
 * Compresses data using LZMA
 * @param {string} data - The data to compress
 * @param {number} level - Compression level
 * @returns {Promise<string>} A promise that resolves with the compressed data
 */
function compressWithLZMA(data, level) {
    return new Promise((resolve, reject) => {
        try {
            // Map our level (1-9) to LZMA level (1-9)
            const lzmaLevel = Math.max(1, Math.min(9, level));

            libraries.lzma.compress(data, lzmaLevel, (result, error) => {
                if (error) {
                    reject(new Error(`LZMA compression error: ${error}`));
                } else {
                    // Convert Uint8Array to Base64 string for storage
                    const base64 = uint8ArrayToBase64(result);
                    resolve(base64);
                }
            });
        } catch (error) {
            reject(error);
        }
    });
}

/**
 * Decompresses data using LZMA
 * @param {string} data - The compressed data (Base64 string)
 * @returns {Promise<string>} A promise that resolves with the decompressed data
 */
function decompressWithLZMA(data) {
    return new Promise((resolve, reject) => {
        try {
            // Convert Base64 string back to Uint8Array
            const uint8Array = base64ToUint8Array(data);

            libraries.lzma.decompress(uint8Array, (result, error) => {
                if (error) {
                    reject(new Error(`LZMA decompression error: ${error}`));
                } else {
                    resolve(result);
                }
            });
        } catch (error) {
            reject(error);
        }
    });
}

// Additional compression/decompression functions would be implemented here
// Similar to those in the advanced-compression.js file

/**
 * Converts a Uint8Array to a Base64 string
 * @param {Uint8Array} buffer - The buffer to convert
 * @returns {string} The Base64 string
 */
function uint8ArrayToBase64(buffer) {
    let binary = '';
    const len = buffer.length;
    for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(buffer[i]);
    }
    return btoa(binary);
}

/**
 * Converts a Base64 string to a Uint8Array
 * @param {string} base64 - The Base64 string to convert
 * @returns {Uint8Array} The Uint8Array
 */
function base64ToUint8Array(base64) {
    const binary = atob(base64);
    const len = binary.length;
    const bytes = new Uint8Array(len);
    for (let i = 0; i < len; i++) {
        bytes[i] = binary.charCodeAt(i);
    }
    return bytes;
}
