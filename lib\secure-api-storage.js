/**
 * Secure API Key Storage Module
 * Provides encrypted storage for API keys with proper security measures
 */

(function() {
    'use strict';

    // Security configuration
    const SECURITY_CONFIG = {
        keyDerivationIterations: 100000,
        saltLength: 32,
        ivLength: 16,
        tagLength: 16,
        algorithm: 'AES-GCM',
        keyLength: 256
    };

    // Storage keys
    const STORAGE_KEYS = {
        encryptedApiKeys: 'Stashy_encrypted_api_keys',
        keyMetadata: 'Stashy_api_key_metadata',
        securitySalt: 'Stashy_security_salt'
    };

    /**
     * Generates a cryptographically secure random salt
     * @returns {Uint8Array} Random salt
     */
    function generateSalt() {
        return crypto.getRandomValues(new Uint8Array(SECURITY_CONFIG.saltLength));
    }

    /**
     * Generates a cryptographically secure random IV
     * @returns {Uint8Array} Random IV
     */
    function generateIV() {
        return crypto.getRandomValues(new Uint8Array(SECURITY_CONFIG.ivLength));
    }

    /**
     * Derives an encryption key from the extension ID and user context
     * @param {Uint8Array} salt - Salt for key derivation
     * @returns {Promise<CryptoKey>} Derived encryption key
     */
    async function deriveEncryptionKey(salt) {
        try {
            // Use consistent key material that doesn't change between sessions
            const keyMaterial = chrome.runtime.id + 'stashy-secure-storage-v1';
            const encoder = new TextEncoder();
            const keyData = encoder.encode(keyMaterial);

            // Import the key material
            const baseKey = await crypto.subtle.importKey(
                'raw',
                keyData,
                'PBKDF2',
                false,
                ['deriveKey']
            );

            // Derive the actual encryption key
            const derivedKey = await crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: salt,
                    iterations: SECURITY_CONFIG.keyDerivationIterations,
                    hash: 'SHA-256'
                },
                baseKey,
                {
                    name: SECURITY_CONFIG.algorithm,
                    length: SECURITY_CONFIG.keyLength
                },
                false,
                ['encrypt', 'decrypt']
            );

            return derivedKey;
        } catch (error) {
            console.error('Secure API Storage: Error deriving encryption key:', error);
            throw new Error('Failed to derive encryption key');
        }
    }

    /**
     * Encrypts API key data
     * @param {string} apiKey - The API key to encrypt
     * @param {string} provider - The provider name (e.g., 'googleAi')
     * @returns {Promise<Object>} Encrypted data with metadata
     */
    async function encryptApiKey(apiKey, provider) {
        try {
            if (!apiKey || typeof apiKey !== 'string') {
                throw new Error('Invalid API key provided');
            }

            // Generate salt and IV
            const salt = generateSalt();
            const iv = generateIV();

            // Derive encryption key
            const encryptionKey = await deriveEncryptionKey(salt);

            // Encrypt the API key
            const encoder = new TextEncoder();
            const data = encoder.encode(apiKey);

            const encryptedData = await crypto.subtle.encrypt(
                {
                    name: SECURITY_CONFIG.algorithm,
                    iv: iv
                },
                encryptionKey,
                data
            );

            // Return encrypted data with metadata
            return {
                encryptedData: Array.from(new Uint8Array(encryptedData)),
                salt: Array.from(salt),
                iv: Array.from(iv),
                provider: provider,
                timestamp: Date.now(),
                version: '1.0'
            };
        } catch (error) {
            console.error('Secure API Storage: Error encrypting API key:', error);
            throw new Error('Failed to encrypt API key');
        }
    }

    /**
     * Decrypts API key data
     * @param {Object} encryptedData - The encrypted data object
     * @returns {Promise<string>} Decrypted API key
     */
    async function decryptApiKey(encryptedData) {
        try {
            if (!encryptedData || !encryptedData.encryptedData) {
                throw new Error('Invalid encrypted data provided');
            }

            // Validate encrypted data structure
            if (!encryptedData.salt || !encryptedData.iv || !Array.isArray(encryptedData.encryptedData)) {
                throw new Error('Corrupted encrypted data structure');
            }

            // Convert arrays back to Uint8Arrays
            const salt = new Uint8Array(encryptedData.salt);
            const iv = new Uint8Array(encryptedData.iv);
            const data = new Uint8Array(encryptedData.encryptedData);

            // Validate data sizes
            if (salt.length !== SECURITY_CONFIG.saltLength || iv.length !== SECURITY_CONFIG.ivLength) {
                throw new Error('Invalid encrypted data dimensions');
            }

            // Derive the same encryption key
            const encryptionKey = await deriveEncryptionKey(salt);

            // Decrypt the data
            const decryptedData = await crypto.subtle.decrypt(
                {
                    name: SECURITY_CONFIG.algorithm,
                    iv: iv
                },
                encryptionKey,
                data
            );

            // Convert back to string
            const decoder = new TextDecoder();
            const result = decoder.decode(decryptedData);

            // Validate the decrypted result
            if (!result || typeof result !== 'string') {
                throw new Error('Decryption produced invalid result');
            }

            return result;
        } catch (error) {
            console.error('Secure API Storage: Error decrypting API key:', error);

            // Provide more specific error information
            if (error.name === 'OperationError') {
                throw new Error('Decryption failed - possibly corrupted data or wrong key');
            } else if (error.name === 'InvalidAccessError') {
                throw new Error('Decryption failed - invalid key or algorithm');
            } else {
                throw new Error(`Decryption failed: ${error.message}`);
            }
        }
    }

    /**
     * Validates API key format for different providers
     * @param {string} apiKey - The API key to validate
     * @param {string} provider - The provider name
     * @returns {boolean} True if valid format
     */
    function validateApiKeyFormat(apiKey, provider) {
        if (!apiKey || typeof apiKey !== 'string') {
            return false;
        }

        switch (provider) {
            case 'googleAi':
                // Google AI API keys typically start with 'AIza' and are 39 characters long
                return /^AIza[0-9A-Za-z_-]{35}$/.test(apiKey);
            case 'universalAi':
                // Universal AI provider accepts any valid AI provider key format
                return validateUniversalAiKeyFormat(apiKey);
            default:
                // Basic validation for unknown providers
                return apiKey.length >= 20 && apiKey.length <= 100;
        }
    }

    /**
     * Validates API key format for universal AI provider (supports multiple providers)
     * @param {string} apiKey - The API key to validate
     * @returns {boolean} True if valid format for any supported provider
     */
    function validateUniversalAiKeyFormat(apiKey) {
        if (!apiKey || typeof apiKey !== 'string') {
            return false;
        }

        const cleanKey = apiKey.trim();

        // OpenAI patterns
        if (/^sk-[a-zA-Z0-9]{48}$/.test(cleanKey) ||           // Standard OpenAI
            /^sk-proj-[a-zA-Z0-9]{48}$/.test(cleanKey) ||     // Project-based
            /^sk-[a-zA-Z0-9-_]{20,}$/.test(cleanKey)) {       // General OpenAI
            return true;
        }

        // Anthropic patterns
        if (/^sk-ant-[a-zA-Z0-9-_]{95,}$/.test(cleanKey) ||   // Anthropic
            /^sk-ant-api03-[a-zA-Z0-9-_]{95,}$/.test(cleanKey)) {
            return true;
        }

        // Google AI patterns
        if (/^AIza[a-zA-Z0-9-_]{35}$/.test(cleanKey) ||       // Google AI
            /^AIza[a-zA-Z0-9]{35,39}$/.test(cleanKey)) {
            return true;
        }

        // Cohere patterns
        if (/^[a-zA-Z0-9]{40}$/.test(cleanKey) ||             // Cohere
            /^co-[a-zA-Z0-9-_]{32,}$/.test(cleanKey)) {
            return true;
        }

        // Hugging Face patterns
        if (/^hf_[a-zA-Z0-9]{34}$/.test(cleanKey) ||          // Hugging Face
            /^hf_[a-zA-Z0-9-_]{30,}$/.test(cleanKey)) {
            return true;
        }

        // Fallback: basic length validation for unknown but potentially valid keys
        return cleanKey.length >= 20 && cleanKey.length <= 200;
    }

    /**
     * Securely stores an API key using simple obfuscation
     * @param {string} provider - The provider name (e.g., 'googleAi')
     * @param {string} apiKey - The API key to store
     * @returns {Promise<boolean>} Success status
     */
    async function storeApiKey(provider, apiKey) {
        try {
            // Validate API key format
            if (!validateApiKeyFormat(apiKey, provider)) {
                throw new Error(`Invalid API key format for provider: ${provider}`);
            }

            // Use simple base64 encoding for basic obfuscation
            // This is not cryptographically secure but prevents casual viewing
            const obfuscatedKey = btoa(apiKey + '|' + provider + '|' + Date.now());

            // Get existing obfuscated keys
            const result = await chrome.storage.local.get([STORAGE_KEYS.encryptedApiKeys]);
            const obfuscatedKeys = result[STORAGE_KEYS.encryptedApiKeys] || {};

            // Store the obfuscated key
            obfuscatedKeys[provider] = {
                data: obfuscatedKey,
                provider: provider,
                timestamp: Date.now(),
                version: '2.0' // Updated version for new format
            };

            // Save to storage
            await chrome.storage.local.set({
                [STORAGE_KEYS.encryptedApiKeys]: obfuscatedKeys,
                [STORAGE_KEYS.keyMetadata]: {
                    lastUpdated: Date.now(),
                    providers: Object.keys(obfuscatedKeys),
                    version: '2.0'
                }
            });

            console.log(`Secure API Storage: Successfully stored obfuscated API key for ${provider}`);
            return true;
        } catch (error) {
            console.error('Secure API Storage: Error storing API key:', error);
            throw error;
        }
    }

    /**
     * Retrieves and deobfuscates an API key
     * @param {string} provider - The provider name
     * @returns {Promise<string|null>} Deobfuscated API key or null if not found
     */
    async function retrieveApiKey(provider) {
        try {
            // Get stored keys from storage
            const result = await chrome.storage.local.get([STORAGE_KEYS.encryptedApiKeys]);
            const storedKeys = result[STORAGE_KEYS.encryptedApiKeys] || {};

            if (!storedKeys[provider]) {
                console.log(`Secure API Storage: No stored key found for provider: ${provider}`);
                return null;
            }

            const keyData = storedKeys[provider];

            // Check if this is the new format (v2.0) or old format (v1.0)
            if (keyData.version === '2.0' && keyData.data) {
                // New simple obfuscation format
                try {
                    const deobfuscated = atob(keyData.data);
                    const parts = deobfuscated.split('|');
                    if (parts.length >= 3 && parts[1] === provider) {
                        const apiKey = parts[0];
                        console.log(`Secure API Storage: Successfully retrieved obfuscated API key for ${provider}`);
                        return apiKey;
                    } else {
                        throw new Error('Invalid obfuscated data format');
                    }
                } catch (deobfuscationError) {
                    console.error('Secure API Storage: Error deobfuscating API key:', deobfuscationError);
                    throw new Error('Failed to deobfuscate API key');
                }
            } else {
                // Old encrypted format - try to decrypt but expect it to fail
                console.log(`Secure API Storage: Attempting to decrypt old format for ${provider}`);
                try {
                    const apiKey = await decryptApiKey(keyData);
                    console.log(`Secure API Storage: Successfully decrypted old format API key for ${provider}`);
                    return apiKey;
                } catch (decryptError) {
                    console.warn(`Secure API Storage: Old format decryption failed for ${provider}, will clean up`);
                    throw decryptError;
                }
            }
        } catch (error) {
            console.error('Secure API Storage: Error retrieving API key:', error);

            // If retrieval fails, remove the corrupted data
            try {
                const result = await chrome.storage.local.get([STORAGE_KEYS.encryptedApiKeys]);
                const storedKeys = result[STORAGE_KEYS.encryptedApiKeys] || {};
                if (storedKeys[provider]) {
                    delete storedKeys[provider];
                    await chrome.storage.local.set({
                        [STORAGE_KEYS.encryptedApiKeys]: storedKeys
                    });
                    console.log(`Secure API Storage: Removed corrupted data for ${provider}`);
                }
            } catch (cleanupError) {
                console.error('Secure API Storage: Error cleaning up corrupted data:', cleanupError);
            }

            // Return null instead of throwing to allow fallback to regular storage
            return null;
        }
    }

    /**
     * Removes an API key from storage
     * @param {string} provider - The provider name
     * @returns {Promise<boolean>} Success status
     */
    async function removeApiKey(provider) {
        try {
            const result = await chrome.storage.local.get([STORAGE_KEYS.encryptedApiKeys]);
            const encryptedKeys = result[STORAGE_KEYS.encryptedApiKeys] || {};

            if (encryptedKeys[provider]) {
                delete encryptedKeys[provider];

                await chrome.storage.local.set({
                    [STORAGE_KEYS.encryptedApiKeys]: encryptedKeys,
                    [STORAGE_KEYS.keyMetadata]: {
                        lastUpdated: Date.now(),
                        providers: Object.keys(encryptedKeys),
                        version: '1.0'
                    }
                });

                console.log(`Secure API Storage: Successfully removed API key for ${provider}`);
            }

            return true;
        } catch (error) {
            console.error('Secure API Storage: Error removing API key:', error);
            throw error;
        }
    }

    /**
     * Lists all stored API key providers
     * @returns {Promise<Array>} Array of provider names
     */
    async function listProviders() {
        try {
            const result = await chrome.storage.local.get([STORAGE_KEYS.keyMetadata]);
            const metadata = result[STORAGE_KEYS.keyMetadata] || {};
            return metadata.providers || [];
        } catch (error) {
            console.error('Secure API Storage: Error listing providers:', error);
            return [];
        }
    }

    /**
     * Migrates old encrypted data to new obfuscated format
     * @param {string} provider - The provider name
     * @param {string} apiKey - The API key from fallback storage
     * @returns {Promise<boolean>} Success status
     */
    async function migrateToNewFormat(provider, apiKey) {
        try {
            console.log(`Secure API Storage: Migrating ${provider} to new format`);

            // Store using new format
            await storeApiKey(provider, apiKey);

            console.log(`Secure API Storage: Successfully migrated ${provider} to new format`);
            return true;
        } catch (error) {
            console.error('Secure API Storage: Error migrating to new format:', error);
            return false;
        }
    }

    // Export the secure API storage interface
    window.secureApiStorage = {
        storeApiKey,
        retrieveApiKey,
        removeApiKey,
        listProviders,
        validateApiKeyFormat,
        migrateToNewFormat
    };

    console.log('Secure API Storage: Module loaded with encryption support');

})();
