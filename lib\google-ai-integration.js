/**
 * Google AI Integration Module
 * Provides seamless integration with Google AI services using existing OAuth credentials
 */

(function() {
    'use strict';

    // Configuration
    const AI_CONFIG = {
        baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
        models: {
            gemini: 'models/gemini-2.5-flash',
            geminiPro: 'models/gemini-2.5-pro'
        },
        maxRetries: 3,
        retryDelay: 3000, // Balanced retry delay
        timeout: 45000, // Increased timeout for longer content
        maxTokensLimit: 8192, // Increased token limit for better responses
        maxPromptLength: 30000, // Significantly increased for Gemini 1.5 capabilities (roughly 30k chars = ~7.5k tokens)
        maxDirectPromptLength: 8000, // Maximum length for direct processing without chunking
        rateLimitDelay: 65000, // Wait time for rate limit errors
        // Enhanced intelligent chunking settings
        enableChunking: true,
        chunkSize: 6000, // Larger chunks for better context preservation
        chunkOverlap: 500, // Increased overlap for better continuity
        autoChunkThreshold: 8000 // Automatically use chunking above this length
    };

    // State management
    let isInitialized = false;
    let authToken = null;
    let aiCapabilities = {
        available: false,
        models: [],
        features: {
            textGeneration: false,
            summarization: false,
            enhancement: false
        }
    };

    // Rate limiting for free tier (15 requests per minute)
    let lastRequestTime = 0;
    let requestCount = 0;
    let requestWindow = Date.now();
    const MIN_REQUEST_INTERVAL = 5000; // 5 seconds between requests
    const MAX_REQUESTS_PER_MINUTE = 12; // Conservative limit

    /**
     * Initializes the Google AI integration
     * @returns {Promise<boolean>} Success status
     */
    async function init() {
        try {
            console.log('Stashy AI: Initializing Google AI integration...');

            // Check if already initialized
            if (isInitialized) {
                return aiCapabilities.available;
            }

            // Check if universal AI configuration exists with non-Google provider
            try {
                const result = await chrome.storage.local.get(['universalAiConfig']);
                const universalConfig = result.universalAiConfig;

                if (universalConfig && universalConfig.apiKey && universalConfig.providerConfig) {
                    console.log('Stashy AI: Universal AI configuration detected, provider:', universalConfig.providerId);

                    // If it's not a Google provider, completely disable legacy Google AI
                    if (universalConfig.providerId !== 'google') {
                        console.log('Stashy AI: Non-Google provider detected (' + universalConfig.providerId + '), disabling legacy Google AI integration');
                        isInitialized = true;
                        return false;
                    }

                    // If it's Google provider, verify the key is actually a Google AI key
                    if (universalConfig.providerId === 'google') {
                        const isGoogleKey = /^AIza[a-zA-Z0-9-_]{35}$/.test(universalConfig.apiKey) ||
                                          /^AIza[a-zA-Z0-9]{35,39}$/.test(universalConfig.apiKey);

                        if (!isGoogleKey) {
                            console.log('Stashy AI: Non-Google API key detected in Google provider config, disabling legacy integration');
                            isInitialized = true;
                            return false;
                        }
                    }
                }
            } catch (configError) {
                console.warn('Stashy AI: Error checking universal AI config:', configError);
            }

            // Try to get authentication credentials
            const credentials = await getAuthenticationCredentials();

            if (!credentials) {
                console.log('Stashy AI: No authentication credentials available - AI features disabled');
                isInitialized = true;
                return false;
            }

            // Store API key credentials
            if (credentials.type === 'apikey') {
                authToken = credentials.key;
            }

            // Test AI service availability
            const available = await testAiAvailability();
            aiCapabilities.available = available;

            if (available) {
                await loadAvailableModels();
                console.log('Stashy AI: Successfully initialized with AI capabilities');
            } else {
                console.log('Stashy AI: Initialized without AI capabilities');
            }

            isInitialized = true;
            return available;

        } catch (error) {
            console.error('Stashy AI: Error during initialization:', error);
            isInitialized = true;
            return false;
        }
    }

    /**
     * Gets authentication credentials from available sources
     * @returns {Promise<Object|null>} Authentication credentials or null
     */
    async function getAuthenticationCredentials() {
        try {
            let apiKey = null;

            // Try secure storage first if available
            if (window.secureApiStorage) {
                try {
                    apiKey = await window.secureApiStorage.retrieveApiKey('googleAi');
                    if (apiKey) {
                        console.log('Stashy AI: Using secure API key authentication');
                        return {
                            type: 'apikey',
                            key: apiKey.trim()
                        };
                    }
                } catch (secureError) {
                    console.warn('Stashy AI: Error retrieving from secure storage:', secureError);
                }
            }

            // Fallback to regular storage
            const result = await chrome.storage.local.get(['googleAiApiKey']);
            apiKey = result.googleAiApiKey;

            if (apiKey && apiKey.trim()) {
                console.log('Stashy AI: Using fallback API key authentication');
                return {
                    type: 'apikey',
                    key: apiKey.trim()
                };
            }

            console.log('Stashy AI: No API key configured');
            return null;

        } catch (error) {
            console.error('Stashy AI: Error getting authentication credentials:', error);
            return null;
        }
    }

    /**
     * Tests if Google AI services are available for the current user
     * @returns {Promise<boolean>} Availability status
     */
    async function testAiAvailability() {
        try {
            const response = await makeAiApiCall('models', {
                method: 'GET'
            });

            console.log('Stashy AI: Models API response:', response);

            if (response && response.models && Array.isArray(response.models)) {
                aiCapabilities.features.textGeneration = true;
                aiCapabilities.features.summarization = true;
                aiCapabilities.features.enhancement = true;
                return true;
            }

            return false;
        } catch (error) {
            // Check if this is a provider blocking error
            if (error.message.includes('Google AI disabled') || error.message.includes('different provider configured')) {
                console.log('Stashy AI: Google AI disabled due to different provider configuration');
                return false; // Return false gracefully, don't throw
            }

            console.log('Stashy AI: AI services not available for this user:', error.message);
            return false;
        }
    }

    /**
     * Loads available AI models
     * @returns {Promise<void>}
     */
    async function loadAvailableModels() {
        try {
            const response = await makeAiApiCall('models', {
                method: 'GET'
            });

            console.log('Stashy AI: Available models response:', response);

            if (response && response.models) {
                aiCapabilities.models = response.models
                    .filter(model => model.name.includes('gemini'))
                    .map(model => ({
                        name: model.name,
                        displayName: model.displayName || model.name,
                        supportedGenerationMethods: model.supportedGenerationMethods || []
                    }));
                console.log('Stashy AI: Loaded models:', aiCapabilities.models);
            }
        } catch (error) {
            console.warn('Stashy AI: Could not load available models:', error);
        }
    }

    /**
     * Makes an API call to Google AI services via background script
     * @param {string} endpoint - The API endpoint
     * @param {Object} options - Request options
     * @returns {Promise<Object>} API response
     */
    async function makeAiApiCall(endpoint, options = {}) {
        if (!authToken) {
            throw new Error('No API key available. Please configure your Google AI API key in settings.');
        }

        // Use security manager for rate limiting if available
        if (window.aiSecurityManager) {
            const rateLimitCheck = window.aiSecurityManager.checkRateLimit();
            if (!rateLimitCheck.allowed) {
                const waitTime = Math.ceil((rateLimitCheck.resetTime - Date.now()) / 1000);
                throw new Error(`${rateLimitCheck.reason}. Please wait ${waitTime} seconds.`);
            }
        } else {
            // Fallback to original rate limiting
            const now = Date.now();

            // Reset request count if window has passed
            if (now - requestWindow > 60000) { // 1 minute window
                requestCount = 0;
                requestWindow = now;
            }

            // Check if we've exceeded the rate limit
            if (requestCount >= MAX_REQUESTS_PER_MINUTE) {
                const waitTime = 60000 - (now - requestWindow);
                throw new Error(`Rate limit exceeded. Please wait ${Math.ceil(waitTime / 1000)} seconds before trying again.`);
            }

            // Check minimum interval between requests
            const timeSinceLastRequest = now - lastRequestTime;
            if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
                const waitTime = MIN_REQUEST_INTERVAL - timeSinceLastRequest;
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }

            lastRequestTime = Date.now();
            requestCount++;
        }

        // Use background script to make the API call
        try {
            console.log(`Stashy AI: Sending request to background script with endpoint: ${endpoint}`);
            console.log(`Stashy AI: Request body:`, options.body);

            const response = await chrome.runtime.sendMessage({
                action: 'googleAiRequest',
                model: endpoint,
                requestBody: options.body
            });

            if (response && response.success) {
                return response.data;
            } else {
                const error = new Error(response?.error || 'Unknown error from Google AI API');

                // Use security manager for enhanced error handling if available
                if (window.aiSecurityManager) {
                    const handledError = window.aiSecurityManager.handleApiError(error, {
                        endpoint: endpoint,
                        method: options.method || 'POST'
                    });
                    throw new Error(handledError.error);
                } else {
                    // Fallback error handling
                    const errorMessage = response?.error || 'Unknown error from Google AI API';

                    // Check if this is a provider blocking error
                    if (errorMessage.includes('blocked') || errorMessage.includes('configured instead')) {
                        console.log('Stashy AI: Google AI requests are blocked due to different provider configuration');
                        throw new Error('Google AI disabled - different provider configured');
                    } else if (errorMessage.includes('rate limit') || errorMessage.includes('quota')) {
                        throw new Error('Rate limit exceeded. Please wait 60 seconds before trying again.');
                    } else if (errorMessage.includes('API key not found') || errorMessage.includes('Invalid API key')) {
                        throw new Error('Invalid API key. Please check your Google AI API key in settings.');
                    } else if (errorMessage.includes('403') || errorMessage.includes('forbidden')) {
                        throw new Error('Access forbidden. Please check your API key permissions.');
                    } else {
                        throw new Error(errorMessage);
                    }
                }
            }
        } catch (error) {
            // Use security manager for error handling if available
            if (window.aiSecurityManager) {
                const handledError = window.aiSecurityManager.handleApiError(error, {
                    endpoint: endpoint,
                    method: options.method || 'POST'
                });
                throw new Error(handledError.error);
            } else {
                // Fallback error handling
                if (error.message.includes('Google AI disabled') || error.message.includes('different provider configured')) {
                    throw error; // Pass through provider blocking errors
                } else if (error.message.includes('Rate limit exceeded') ||
                    error.message.includes('Invalid API key') ||
                    error.message.includes('Access forbidden')) {
                    throw error;
                }

                console.error('Stashy AI: Error communicating with background script:', error);
                throw new Error('Failed to communicate with AI service. Please try again.');
            }
        }
    }

    /**
     * Generates text using Google AI with intelligent prompt handling
     * @param {string} prompt - The input prompt
     * @param {Object} options - Generation options
     * @returns {Promise<string>} Generated text
     */
    async function generateText(prompt, options = {}) {
        if (!aiCapabilities.available) {
            throw new Error('AI services not available');
        }

        // Validate prompt
        if (!prompt || typeof prompt !== 'string') {
            throw new Error('Invalid prompt provided');
        }

        let processedPrompt = prompt.trim();

        // Use security manager for input validation and sanitization if available
        if (window.aiSecurityManager) {
            // Sanitize input to prevent injection attacks
            processedPrompt = window.aiSecurityManager.sanitizeInput(processedPrompt);

            // Validate request parameters
            const validation = window.aiSecurityManager.validateRequest({
                prompt: processedPrompt,
                maxTokens: options.maxTokens
            });

            if (!validation.valid) {
                throw new Error(`Invalid request: ${validation.errors.join(', ')}`);
            }

            // Record the request for rate limiting
            window.aiSecurityManager.recordRequest();

            // Log security event
            window.aiSecurityManager.logSecurityEvent('ai_request', {
                promptLength: processedPrompt.length,
                maxTokens: options.maxTokens,
                model: options.model
            });
        }

        // Check if prompt is too long and needs intelligent handling
        if (processedPrompt.length > AI_CONFIG.maxPromptLength) {
            throw new Error(`Prompt too long (${processedPrompt.length} chars). Maximum supported length is ${AI_CONFIG.maxPromptLength} characters.`);
        }

        // For very long prompts that are still within limits, use chunking if enabled and beneficial
        if (processedPrompt.length > AI_CONFIG.autoChunkThreshold && AI_CONFIG.enableChunking && !options.skipChunking) {
            console.log(`Stashy AI: Large prompt detected (${processedPrompt.length} chars), using intelligent processing`);
            return await processLargePrompt(processedPrompt, options);
        }

        // For moderately long prompts, optimize but process directly
        if (processedPrompt.length > AI_CONFIG.maxDirectPromptLength) {
            console.log(`Stashy AI: Optimizing large prompt (${processedPrompt.length} chars) for direct processing`);
            processedPrompt = optimizePromptForDirectProcessing(processedPrompt, options);
        }

        const model = options.model || AI_CONFIG.models.gemini;
        const maxTokens = Math.min(options.maxTokens || 6000, AI_CONFIG.maxTokensLimit); // Massively increased for Deep Research

        const requestBody = {
            contents: [{
                parts: [{
                    text: processedPrompt
                }]
            }],
            generationConfig: {
                temperature: Math.max(0.0, Math.min(1.0, options.temperature || 0.7)),
                maxOutputTokens: maxTokens,
                topP: Math.max(0.0, Math.min(1.0, options.topP || 0.8)),
                topK: Math.max(1, Math.min(40, options.topK || 40))
            },
            safetySettings: [
                {
                    category: "HARM_CATEGORY_HARASSMENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_HATE_SPEECH",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
        };

        const response = await makeAiApiCall(`${model}:generateContent`, {
            method: 'POST',
            body: requestBody
        });

        // Enhanced response validation and error handling
        if (!response) {
            throw new Error('Empty response from AI service');
        }

        console.log('Stashy AI: Full API response:', JSON.stringify(response, null, 2));

        if (response.error) {
            throw new Error(`AI API Error: ${response.error.message || 'Unknown error'}`);
        }

        if (!response.candidates || !Array.isArray(response.candidates) || response.candidates.length === 0) {
            console.error('Stashy AI: No candidates in response. Full response:', response);
            throw new Error('No candidates in AI response');
        }

        const candidate = response.candidates[0];
        console.log('Stashy AI: First candidate:', JSON.stringify(candidate, null, 2));

        // Check for safety filtering
        if (candidate.finishReason === 'SAFETY') {
            throw new Error('Content was filtered due to safety concerns. Please try rephrasing your request.');
        }

        if (candidate.finishReason === 'RECITATION') {
            throw new Error('Content was filtered due to recitation concerns. Please try rephrasing your request.');
        }

        // Enhanced content validation with detailed logging
        if (!candidate.content) {
            console.error('Stashy AI: No content field in candidate:', candidate);
            throw new Error('No content field in AI response candidate');
        }

        // Handle different response formats for Gemini 2.5 vs older models
        if (!candidate.content.parts) {
            console.warn('Stashy AI: No parts field in candidate content, checking for alternative formats...');

            // Check if this is a Gemini 2.5 response with different format
            if (candidate.content.role === 'model' && candidate.finishReason === 'MAX_TOKENS') {
                console.error('Stashy AI: Gemini 2.5 response hit token limit without generating content');
                throw new Error('AI response was truncated due to token limit. Please try with shorter content or increase token limit.');
            }

            // Check if content is directly in the candidate
            if (candidate.text) {
                console.log('Stashy AI: Found text directly in candidate');
                return candidate.text.trim();
            }

            console.error('Stashy AI: No parts field in candidate content:', candidate.content);
            throw new Error('No parts field in AI response candidate content');
        }

        if (candidate.content.parts.length === 0) {
            console.error('Stashy AI: Empty parts array in candidate content:', candidate.content);
            throw new Error('Empty parts array in AI response candidate content');
        }

        const generatedText = candidate.content.parts[0].text;
        if (!generatedText || typeof generatedText !== 'string') {
            throw new Error('Invalid text content in AI response');
        }

        return generatedText.trim();
    }

    /**
     * Processes large prompts using intelligent strategies
     * @param {string} prompt - Large prompt to process
     * @param {Object} options - Processing options
     * @returns {Promise<string>} Generated text
     */
    async function processLargePrompt(prompt, options = {}) {
        // Extract the instruction/question part and content part
        const { instruction, content } = extractPromptParts(prompt);

        if (!content || content.length < AI_CONFIG.autoChunkThreshold) {
            // If no clear content separation, use direct optimization
            return await generateText(optimizePromptForDirectProcessing(prompt, options), { ...options, skipChunking: true });
        }

        // Use content chunking for the main content while preserving instruction
        console.log(`Stashy AI: Processing large prompt with instruction (${instruction.length} chars) and content (${content.length} chars)`);

        const chunks = chunkContent(content);
        const results = [];

        for (let i = 0; i < chunks.length; i++) {
            const chunkPrompt = `${instruction}\n\n${chunks[i]}`;

            try {
                const result = await generateText(chunkPrompt, { ...options, skipChunking: true });
                results.push(result);

                // Delay between chunks to avoid rate limiting
                if (i < chunks.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
            } catch (error) {
                console.warn(`Stashy AI: Error processing prompt chunk ${i + 1}:`, error.message);
                results.push(`[Chunk ${i + 1} failed: ${error.message}]`);
            }
        }

        // Combine results intelligently
        return combinePromptResults(results, instruction, options);
    }

    /**
     * Extracts instruction and content parts from a prompt
     * @param {string} prompt - Full prompt
     * @returns {Object} Object with instruction and content parts
     */
    function extractPromptParts(prompt) {
        // Common patterns that separate instruction from content
        const separators = [
            /\n\n(Content:|Text:|Article:|Document:|Input:)/i,
            /\n\n([A-Z][^:]*:)\s*\n/,
            /\n\n(.{500,})/s, // Large content block
        ];

        for (const separator of separators) {
            const match = prompt.match(separator);
            if (match) {
                const splitIndex = prompt.indexOf(match[0]);
                return {
                    instruction: prompt.substring(0, splitIndex).trim(),
                    content: prompt.substring(splitIndex + match[0].length).trim()
                };
            }
        }

        // If no clear separation, treat the whole thing as content with minimal instruction
        if (prompt.length > AI_CONFIG.autoChunkThreshold) {
            return {
                instruction: "Process this content:",
                content: prompt
            };
        }

        return {
            instruction: prompt,
            content: ""
        };
    }

    /**
     * Optimizes prompts for direct processing by removing redundancy
     * @param {string} prompt - Prompt to optimize
     * @param {Object} options - Processing options
     * @returns {string} Optimized prompt
     */
    function optimizePromptForDirectProcessing(prompt, options = {}) {
        let optimized = prompt
            // Remove excessive whitespace
            .replace(/\s+/g, ' ')
            .replace(/\n\s*\n\s*\n/g, '\n\n')

            // Remove redundant phrases
            .replace(/please\s+(analyze|summarize|process|review)\s+this\s+(content|text|document|article)/gi, '')
            .replace(/\b(the\s+following|this\s+is|here\s+is)\s+(content|text|document|article)/gi, '')

            // Compress repetitive formatting
            .replace(/(-{3,}|={3,}|\*{3,})/g, '---')
            .replace(/\n\s*\n\s*\n/g, '\n\n')

            .trim();

        // If still too long, intelligently truncate while preserving structure
        if (optimized.length > AI_CONFIG.maxDirectPromptLength) {
            optimized = intelligentTruncate(optimized, AI_CONFIG.maxDirectPromptLength);
        }

        return optimized;
    }

    /**
     * Intelligently truncates content while preserving important parts
     * @param {string} content - Content to truncate
     * @param {number} maxLength - Maximum length
     * @returns {string} Truncated content
     */
    function intelligentTruncate(content, maxLength) {
        if (content.length <= maxLength) return content;

        // Try to preserve the beginning and end, removing middle content
        const preserveStart = Math.floor(maxLength * 0.6);
        const preserveEnd = Math.floor(maxLength * 0.3);
        const ellipsisLength = 50; // Space for "... [content truncated] ..."

        if (preserveStart + preserveEnd + ellipsisLength < maxLength) {
            const start = content.substring(0, preserveStart);
            const end = content.substring(content.length - preserveEnd);
            return `${start}\n\n... [content truncated for length] ...\n\n${end}`;
        }

        // If that doesn't work, just truncate from the end
        return content.substring(0, maxLength - 20) + '... [truncated]';
    }

    /**
     * Combines results from processing large prompts in chunks
     * @param {Array} results - Array of chunk results
     * @param {string} instruction - Original instruction
     * @param {Object} options - Processing options
     * @returns {string} Combined result
     */
    function combinePromptResults(results, instruction, options = {}) {
        const validResults = results.filter(result =>
            result && typeof result === 'string' &&
            !result.startsWith('[Chunk') &&
            result.length > 10
        );

        if (validResults.length === 0) {
            return 'Unable to process the large prompt. Please try breaking it into smaller parts.';
        }

        if (validResults.length === 1) {
            return validResults[0];
        }

        // Determine combination strategy based on instruction type
        if (instruction.toLowerCase().includes('summarize')) {
            return createUnifiedSummary(validResults);
        } else if (instruction.toLowerCase().includes('analyze')) {
            return validResults.map((result, i) =>
                `**Analysis Part ${i + 1}:**\n${result}`
            ).join('\n\n---\n\n');
        } else {
            // Default: combine with clear separation
            return validResults.join('\n\n');
        }
    }

    /**
     * Intelligently chunks long content for processing
     * @param {string} content - Content to chunk
     * @param {number} maxChunkSize - Maximum size per chunk
     * @param {number} overlap - Overlap between chunks
     * @returns {Array} Array of content chunks
     */
    function chunkContent(content, maxChunkSize = AI_CONFIG.chunkSize, overlap = AI_CONFIG.chunkOverlap) {
        if (content.length <= maxChunkSize) {
            return [content];
        }

        // Clean content first to remove formatting artifacts
        const cleanedContent = cleanContentForChunking(content);

        // Validate cleaned content
        if (cleanedContent.length < 100) {
            console.warn('Stashy AI: Content too short after cleaning, using original');
            return [content.substring(0, maxChunkSize)];
        }

        const chunks = [];
        let start = 0;
        let chunkCount = 0;
        const maxChunks = 8; // Increased limit for better coverage of large content

        while (start < cleanedContent.length && chunkCount < maxChunks) {
            let end = start + maxChunkSize;

            // If this isn't the last chunk, try to break at natural boundaries
            if (end < cleanedContent.length) {
                // Try different boundary types in order of preference
                const boundaries = [
                    { pattern: '\n\n', minRatio: 0.4 }, // Paragraph breaks
                    { pattern: '. ', minRatio: 0.6 },   // Sentence breaks
                    { pattern: ', ', minRatio: 0.8 },   // Clause breaks
                    { pattern: ' ', minRatio: 0.9 }     // Word breaks
                ];

                let bestEnd = end;
                for (const boundary of boundaries) {
                    const lastBoundary = cleanedContent.lastIndexOf(boundary.pattern, end);
                    if (lastBoundary > start + maxChunkSize * boundary.minRatio) {
                        bestEnd = lastBoundary + boundary.pattern.length;
                        break;
                    }
                }
                end = bestEnd;
            }

            const chunk = cleanedContent.substring(start, end).trim();

            // Quality check for chunk content
            if (chunk.length > 100 && isValidChunk(chunk)) {
                chunks.push(chunk);
                chunkCount++;
            } else if (chunk.length > 50) {
                // Include shorter chunks if they seem meaningful
                chunks.push(chunk);
                chunkCount++;
            }

            start = end - overlap; // Overlap for context continuity

            // Prevent infinite loops
            if (start >= cleanedContent.length - overlap) {
                break;
            }
        }

        return chunks.length > 0 ? chunks : [cleanedContent.substring(0, maxChunkSize)];
    }

    /**
     * Validates if a chunk contains meaningful content
     * @param {string} chunk - Content chunk to validate
     * @returns {boolean} True if chunk is valid
     */
    function isValidChunk(chunk) {
        // Check for minimum word count
        const wordCount = chunk.split(/\s+/).length;
        if (wordCount < 10) return false;

        // Check for meaningful content (not just formatting)
        const meaningfulChars = chunk.replace(/[^a-zA-Z0-9]/g, '').length;
        const ratio = meaningfulChars / chunk.length;
        if (ratio < 0.5) return false;

        // Check for common formatting artifacts
        const artifacts = [
            /^[^a-zA-Z]*$/,  // Only symbols/numbers
            /^\s*\d+\s*$/,   // Only numbers
            /^[^a-zA-Z]*[a-zA-Z]{1,3}[^a-zA-Z]*$/, // Very short words only
        ];

        for (const artifact of artifacts) {
            if (artifact.test(chunk)) return false;
        }

        return true;
    }

    /**
     * Cleans content for better chunking by removing formatting artifacts
     * @param {string} content - Raw content
     * @returns {string} Cleaned content
     */
    function cleanContentForChunking(content) {
        return content
            // Remove CSS and style blocks completely
            .replace(/\{[^}]*\}/g, '')
            .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
            .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')

            // Remove HTML-like tags but preserve content
            .replace(/<[^>]*>/g, ' ')

            // Remove wiki markup and formatting
            .replace(/\[\[([^\]|]+)(\|[^\]]+)?\]\]/g, '$1') // Convert [[link|text]] to text
            .replace(/\{\{[^}]*\}\}/g, '') // Remove templates
            .replace(/\[\[|\]\]/g, '') // Remove remaining brackets

            // Remove CSS properties and values
            .replace(/[a-z-]+:\s*[^;]+;/gi, '')
            .replace(/#[0-9a-f]{3,6}/gi, '')
            .replace(/var\([^)]+\)/g, '')
            .replace(/\d+px|\d+em|\d+%|\d+rem/g, '')

            // Remove common web artifacts
            .replace(/\bedit\b\s*\]/gi, '') // Remove [edit] links
            .replace(/\[\s*citation\s+needed\s*\]/gi, '') // Remove citation needed
            .replace(/\[\s*\d+\s*\]/g, '') // Remove reference numbers
            .replace(/\[\s*[a-z]+\s*\]/gi, '') // Remove other bracketed items

            // Clean up navigation and metadata
            .replace(/\b(jump to|navigation|search|contents|references|external links|see also)\b/gi, '')

            // Remove excessive punctuation and symbols
            .replace(/[^\w\s.,!?;:()\-'"]/g, ' ')

            // Normalize whitespace
            .replace(/\s+/g, ' ')
            .replace(/\n\s*\n/g, '\n\n')

            // Remove lines that are too short (likely artifacts)
            .split('\n')
            .filter(line => line.trim().length > 10)
            .join('\n')

            .trim();
    }

    /**
     * Processes long content using intelligent chunking
     * @param {string} content - Long content to process
     * @param {string} operation - Type of operation (summarize, analyze, etc.)
     * @param {Object} options - Processing options
     * @returns {Promise<string>} Combined result
     */
    async function processLongContent(content, operation, options = {}) {
        if (!AI_CONFIG.enableChunking || content.length <= AI_CONFIG.autoChunkThreshold) {
            // Process normally if chunking disabled or content is short enough
            return await processSingleContent(content, operation, options);
        }

        console.log(`Stashy AI: Processing long content (${content.length} chars) using intelligent chunking`);

        const chunks = chunkContent(content);
        const results = [];

        // Show progress indicator
        const progressId = showProgressIndicator(chunks.length, operation);

        for (let i = 0; i < chunks.length; i++) {
            console.log(`Stashy AI: Processing chunk ${i + 1}/${chunks.length}`);

            // Update progress
            updateProgressIndicator(progressId, i + 1, chunks.length, `Processing section ${i + 1}...`);

            try {
                const chunkResult = await processSingleContent(chunks[i], operation, {
                    ...options,
                    chunkIndex: i,
                    totalChunks: chunks.length
                });
                results.push(chunkResult);

                // Small delay between chunks to avoid rate limiting
                if (i < chunks.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1500)); // Increased delay
                }
            } catch (error) {
                console.warn(`Stashy AI: Error processing chunk ${i + 1}:`, error.message);
                results.push(`[Section ${i + 1} processing failed: ${error.message}]`);
            }
        }

        // Update progress to completion
        updateProgressIndicator(progressId, chunks.length, chunks.length, 'Combining results...');

        // Combine results intelligently based on operation type
        const finalResult = combineChunkResults(results, operation, options);

        // Hide progress indicator
        setTimeout(() => hideProgressIndicator(progressId), 1000);

        return finalResult;
    }

    /**
     * Processes a single piece of content
     * @param {string} content - Content to process
     * @param {string} operation - Type of operation
     * @param {Object} options - Processing options
     * @returns {Promise<string>} Processing result
     */
    async function processSingleContent(content, operation, options = {}) {
        const { chunkIndex, totalChunks } = options;
        const chunkInfo = totalChunks > 1 ? ` (Part ${chunkIndex + 1}/${totalChunks})` : '';

        try {
            let result;

            switch (operation) {
                case 'summarize':
                    result = await summarizeText(content, { ...options, skipChunking: true });
                    break;
                case 'analyze':
                    result = await analyzeContent(content, { ...options, skipChunking: true });
                    break;

                case 'enhance':
                    result = await enhanceText(content, { ...options, skipChunking: true });
                    break;
                default:
                    throw new Error(`Unknown operation: ${operation}`);
            }

            // Validate the result
            return validateAiResult(result, operation, chunkIndex);

        } catch (error) {
            console.warn(`Stashy AI: Error processing chunk ${chunkIndex + 1}:`, error.message);
            return `[Processing failed for section ${chunkIndex + 1}: ${error.message}]`;
        }
    }

    /**
     * Validates AI result for quality and completeness
     * @param {string} result - AI result to validate
     * @param {string} operation - Type of operation
     * @param {number} chunkIndex - Index of the chunk
     * @returns {string} Validated result
     */
    function validateAiResult(result, operation, chunkIndex = 0) {
        if (!result || typeof result !== 'string') {
            throw new Error('AI returned invalid response');
        }

        // Check for common AI error patterns
        const errorPatterns = [
            /please provide the text/i,
            /i need the text/i,
            /i cannot summarize/i,
            /no text provided/i,
            /invalid input/i,
            /error:/i
        ];

        for (const pattern of errorPatterns) {
            if (pattern.test(result)) {
                throw new Error('AI could not process the content properly');
            }
        }

        // Check minimum length based on operation
        const minLengths = {
            'summarize': 50,
            'analyze': 30,
            'extract': 20,
            'enhance': 20
        };

        const minLength = minLengths[operation] || 20;
        if (result.length < minLength) {
            throw new Error('AI response too short to be meaningful');
        }

        // Clean up the result
        return result.trim();
    }

    /**
     * Combines results from multiple chunks
     * @param {Array} results - Array of chunk results
     * @param {string} operation - Type of operation
     * @param {Object} options - Processing options
     * @returns {string} Combined result
     */
    function combineChunkResults(results, operation, options = {}) {
        // Filter out invalid results and error messages
        const validResults = results.filter(result => {
            if (!result || typeof result !== 'string') return false;
            if (result.startsWith('[Section') || result.startsWith('[Chunk')) return false;
            if (result.includes('Please provide the text you would like me to summarize')) return false;
            if (result.includes('I need the text containing')) return false;
            if (result.length < 20) return false; // Too short to be meaningful
            return true;
        });

        if (validResults.length === 0) {
            return 'Unable to process content - all sections contained formatting artifacts or errors. Please try with different content.';
        }

        // If only one valid result, return it directly (no chunking needed)
        if (validResults.length === 1) {
            return cleanResultText(validResults[0]);
        }

        switch (operation) {
            case 'summarize':
                // Create a unified summary instead of separate sections
                const combinedSummary = createUnifiedSummary(validResults);
                return combinedSummary;

            case 'analyze':
                return `**Comprehensive Analysis:**\n\n${validResults.map((result, i) => {
                    const cleanResult = cleanResultText(result);
                    return `**Key Insights ${i + 1}:**\n${cleanResult}`;
                }).join('\n\n---\n\n')}`;

            case 'enhance':
                // For enhancement, create seamless flow
                return validResults.map(cleanResultText).join('\n\n');

            case 'translate':
                // For translation, maintain structure
                return validResults.map(cleanResultText).join('\n\n');

            default:
                return validResults.map(cleanResultText).join('\n\n');
        }
    }

    /**
     * Creates a unified summary from multiple chunk results
     * @param {Array} results - Array of valid results
     * @returns {string} Unified summary
     */
    function createUnifiedSummary(results) {
        // Try to create a flowing narrative instead of separate sections
        const cleanedResults = results.map(cleanResultText);

        // If results seem to be about the same topic, merge them
        const firstSentences = cleanedResults.map(result => result.split('.')[0]);
        const hasCommonTheme = firstSentences.some(sentence =>
            firstSentences.filter(s => s !== sentence &&
                s.toLowerCase().includes(sentence.toLowerCase().split(' ')[0])
            ).length > 0
        );

        if (hasCommonTheme && cleanedResults.length <= 3) {
            // Merge into flowing narrative
            return cleanedResults.join(' ').replace(/\s+/g, ' ').trim();
        } else {
            // Keep as organized sections but with better flow
            return cleanedResults.map((result, i) => {
                const prefix = i === 0 ? '' : 'Additionally, ';
                return prefix + result;
            }).join(' ');
        }
    }

    /**
     * Cleans result text of common artifacts
     * @param {string} text - Text to clean
     * @returns {string} Cleaned text
     */
    function cleanResultText(text) {
        return text
            // Remove section headers that might have been added
            .replace(/^\*\*Section \d+:\*\*\s*/i, '')
            .replace(/^\*\*Part \d+:\*\*\s*/i, '')
            .replace(/^\*\*Key Insights \d+:\*\*\s*/i, '')
            // Remove incomplete sentences at the end
            .replace(/\.\.\.\s*$/, '.')
            // Clean up spacing
            .replace(/\s+/g, ' ')
            .trim();
    }

    /**
     * Analyzes content for various purposes
     * @param {string} content - Content to analyze
     * @param {Object} options - Analysis options
     * @returns {Promise<string>} Analysis result
     */
    async function analyzeContent(content, options = {}) {
        const { type = 'general', skipChunking = false } = options;

        if (!skipChunking && AI_CONFIG.enableChunking && content.length > AI_CONFIG.autoChunkThreshold) {
            return await processLongContent(content, 'analyze', options);
        }

        const prompt = `Analyze this content for ${type} insights:

${content}

Analysis:`;

        return await generateText(prompt, {
            model: AI_CONFIG.models.geminiPro,
            temperature: 0.3,
            maxTokens: 800
        });
    }

    /**
     * Summarizes text content
     * @param {string} text - Text to summarize
     * @param {Object} options - Summarization options
     * @returns {Promise<string>} Summary
     */
    async function summarizeText(text, options = {}) {
        // Input validation
        if (!text || typeof text !== 'string' || text.trim().length === 0) {
            throw new Error('Invalid text provided for summarization');
        }

        const maxLength = Math.min(options.maxLength || 200, 1000);
        const style = options.style || 'concise';
        const isNoteContent = options.isNoteContent || false;
        const skipChunking = options.skipChunking || false;

        // Use intelligent chunking for long content
        if (!skipChunking && AI_CONFIG.enableChunking && text.length > AI_CONFIG.autoChunkThreshold) {
            console.log(`Stashy AI: Using intelligent chunking for long content (${text.length} chars)`);
            return await processLongContent(text, 'summarize', options);
        }

        // Process normally for shorter content
        let inputText = text.trim();
        const maxInputLength = isNoteContent ? AI_CONFIG.maxDirectPromptLength : AI_CONFIG.maxDirectPromptLength * 0.75;

        if (inputText.length > maxInputLength) {
            console.log(`Stashy AI: Optimizing long text (${inputText.length} chars) for direct processing`);
            inputText = optimizePromptForDirectProcessing(inputText, options);
        }

        const prompt = `Summarize this text in ${maxLength} words:

${inputText}

Summary:`;

        // Increased token limits by 2000
        const maxTokens = isNoteContent ? Math.min(maxLength * 4, 4000) : Math.min(maxLength * 3, 3500);

        return await generateText(prompt, {
            model: options.model || AI_CONFIG.models.gemini,
            temperature: 0.3,
            maxTokens: maxTokens
        });
    }

    /**
     * Enhances text content
     * @param {string} text - Text to enhance
     * @param {Object} options - Enhancement options
     * @returns {Promise<string>} Enhanced text
     */
    async function enhanceText(text, options = {}) {
        // Input validation
        if (!text || typeof text !== 'string' || text.trim().length === 0) {
            throw new Error('Invalid text provided for enhancement');
        }

        const enhancementType = options.type || 'improve';
        const isNoteContent = options.isNoteContent || false;
        const skipChunking = options.skipChunking || false;

        // Use intelligent chunking for long content
        if (!skipChunking && AI_CONFIG.enableChunking && text.length > AI_CONFIG.autoChunkThreshold) {
            console.log(`Stashy AI: Using intelligent chunking for enhancement (${text.length} chars)`);
            return await processLongContent(text, 'enhance', options);
        }

        // Process normally for shorter content
        let inputText = text.trim();
        const maxInputLength = isNoteContent ? AI_CONFIG.maxDirectPromptLength : AI_CONFIG.maxDirectPromptLength * 0.75;

        if (inputText.length > maxInputLength) {
            console.log(`Stashy AI: Optimizing long text (${inputText.length} chars) for direct processing`);
            inputText = optimizePromptForDirectProcessing(inputText, options);
        }

        let prompt;
        switch (enhancementType) {
            case 'improve':
                prompt = `Improve this text:

${inputText}

Improved:`;
                break;
            case 'expand':
                prompt = `Expand this text with more details:

${inputText}

Expanded:`;
                break;
            case 'simplify':
                prompt = `Simplify this text:

${inputText}

Simplified:`;
                break;
            default:
                throw new Error(`Unknown enhancement type: ${enhancementType}`);
        }

        // Increased token limits by 2000
        const maxTokens = isNoteContent ? Math.min(options.maxTokens || 4000, 5000) : Math.min(options.maxTokens || 3500, 4000);

        return await generateText(prompt, {
            model: options.model || AI_CONFIG.models.gemini,
            temperature: 0.5,
            maxTokens: maxTokens
        });
    }

    /**
     * Suggests categories for content
     * @param {string} content - Content to categorize
     * @param {Array} existingCategories - Existing categories to consider
     * @returns {Promise<Array>} Suggested categories
     */
    async function suggestCategories(content, existingCategories = []) {
        // Input validation
        if (!content || typeof content !== 'string' || content.trim().length === 0) {
            throw new Error('Invalid content provided for categorization');
        }

        // Truncate content aggressively
        let inputContent = content.trim();
        if (inputContent.length > 600) {
            inputContent = inputContent.substring(0, 600) + '...';
        }

        const existingCatsText = existingCategories.length > 0
            ? `\nExisting categories: ${existingCategories.join(', ')}`
            : '';

        const prompt = `Suggest 3-5 categories for this content:${existingCatsText}

${inputContent}

Categories:`;

        const response = await generateText(prompt, {
            model: AI_CONFIG.models.gemini,
            temperature: 0.4,
            maxTokens: 150
        });

        if (!response || typeof response !== 'string') {
            return [];
        }

        return response.split(',').map(cat => cat.trim()).filter(cat => cat.length > 0 && cat.length < 50);
    }

    /**
     * Gets AI capabilities
     * @returns {Object} Current AI capabilities
     */
    function getCapabilities() {
        return { ...aiCapabilities };
    }

    /**
     * Checks if AI features are available
     * @returns {boolean} Availability status
     */
    function isAvailable() {
        return aiCapabilities.available;
    }











    /**
     * Generates bullet points from content
     * @param {string} content - Content to convert to bullet points
     * @param {Object} options - Bullet point options
     * @returns {Promise<string>} Formatted bullet points
     */
    async function generateBulletPoints(content, options = {}) {
        const { maxPoints = 6, isNoteContent = false } = options;

        // Use intelligent processing for large content
        let inputContent = content.trim();
        const maxInputLength = isNoteContent ? AI_CONFIG.maxDirectPromptLength : AI_CONFIG.maxDirectPromptLength * 0.5;

        if (inputContent.length > maxInputLength) {
            console.log(`Stashy AI: Optimizing large content (${inputContent.length} chars) for bullet points`);
            inputContent = optimizePromptForDirectProcessing(inputContent, options);
        }

        const prompt = `Convert to ${maxPoints} bullet points:

${inputContent}

Bullet points:`;

        // Increased token limits by 2000 for note content
        const maxTokens = isNoteContent ? 2500 : 2300;

        return await generateText(prompt, {
            model: AI_CONFIG.models.gemini,
            temperature: 0.3,
            maxTokens: maxTokens
        });
    }

    /**
     * Extracts action items from content
     * @param {string} content - Content to analyze for action items
     * @param {Object} options - Action item options
     * @returns {Promise<string>} Formatted action items
     */
    async function extractActionItems(content, options = {}) {
        const { isNoteContent = false } = options;

        // Different limits for note content vs webpage content
        let inputContent = content.trim();
        const maxInputLength = isNoteContent ? 2500 : 800;

        if (inputContent.length > maxInputLength) {
            inputContent = inputContent.substring(0, maxInputLength) + '...';
        }

        const prompt = `Extract action items and tasks from this content:

${inputContent}

Action Items:`;

        // Increased token limits by 2000 for note content
        const maxTokens = isNoteContent ? 2500 : 2300;

        return await generateText(prompt, {
            model: AI_CONFIG.models.gemini,
            temperature: 0.3,
            maxTokens: maxTokens
        });
    }

    /**
     * Performs grammar and style correction
     * @param {string} content - Content to correct
     * @param {Object} options - Correction options
     * @returns {Promise<string>} Corrected content
     */
    async function correctGrammarAndStyle(content, options = {}) {
        const { isNoteContent = false } = options;

        // Use intelligent processing for large content
        let inputContent = content.trim();
        const maxInputLength = isNoteContent ? AI_CONFIG.maxDirectPromptLength : AI_CONFIG.maxDirectPromptLength * 0.5;

        if (inputContent.length > maxInputLength) {
            console.log(`Stashy AI: Optimizing large content (${inputContent.length} chars) for grammar correction`);
            inputContent = optimizePromptForDirectProcessing(inputContent, options);
        }

        const prompt = `Improve grammar and style:

${inputContent}

Improved version:`;

        // Increased token limits by 2000 for note content
        const maxTokens = isNoteContent ? 3200 : 2600;

        return await generateText(prompt, {
            model: AI_CONFIG.models.gemini,
            temperature: 0.4,
            maxTokens: maxTokens
        });
    }



    /**
     * Generates an outline from content
     * @param {string} content - Content to outline
     * @param {Object} options - Outline options
     * @returns {Promise<string>} Generated outline
     */
    async function generateOutline(content, options = {}) {
        const { isNoteContent = false } = options;

        // Use intelligent processing for large content
        let inputContent = content.trim();
        const maxInputLength = isNoteContent ? AI_CONFIG.maxDirectPromptLength : AI_CONFIG.maxDirectPromptLength * 0.5;

        if (inputContent.length > maxInputLength) {
            console.log(`Stashy AI: Optimizing large content (${inputContent.length} chars) for outline generation`);
            inputContent = optimizePromptForDirectProcessing(inputContent, options);
        }

        const prompt = `Create an outline from this content:

${inputContent}

Outline:`;

        // Increased token limits by 2000 for note content
        const maxTokens = isNoteContent ? 2800 : 2400;

        return await generateText(prompt, {
            model: AI_CONFIG.models.gemini,
            temperature: 0.3,
            maxTokens: maxTokens
        });
    }

    /**
     * Expands content with related information
     * @param {string} content - Content to expand
     * @param {Object} options - Expansion options
     * @returns {Promise<string>} Expanded content
     */
    async function expandContent(content, options = {}) {
        const { isNoteContent = false } = options;

        // Use intelligent processing for large content
        let inputContent = content.trim();
        const maxInputLength = isNoteContent ? AI_CONFIG.maxDirectPromptLength * 0.75 : AI_CONFIG.maxDirectPromptLength * 0.4; // Slightly lower for expansion to allow more output

        if (inputContent.length > maxInputLength) {
            console.log(`Stashy AI: Optimizing large content (${inputContent.length} chars) for content expansion`);
            inputContent = optimizePromptForDirectProcessing(inputContent, options);
        }

        const prompt = `Expand this content with more details:

${inputContent}

Expanded version:`;

        // Increased token limits by 2000 for note content
        const maxTokens = isNoteContent ? 3200 : 2600;

        return await generateText(prompt, {
            model: AI_CONFIG.models.gemini,
            temperature: 0.5,
            maxTokens: maxTokens
        });
    }

    /**
     * Shows a progress indicator for long content processing
     * @param {number} totalSteps - Total number of processing steps
     * @param {string} operation - Type of operation
     * @returns {string} Progress indicator ID
     */
    function showProgressIndicator(totalSteps, operation) {
        const progressId = `ai-progress-${Date.now()}`;

        const progressContainer = document.createElement('div');
        progressContainer.id = progressId;
        progressContainer.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #2196f3;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10001;
            min-width: 300px;
            text-align: center;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;

        progressContainer.innerHTML = `
            <div style="margin-bottom: 15px;">
                <div style="font-size: 16px; font-weight: 600; color: #333; margin-bottom: 8px;">
                    🤖 AI Processing
                </div>
                <div style="font-size: 14px; color: #666;">
                    ${getOperationDisplayName(operation)}
                </div>
            </div>

            <div style="margin-bottom: 15px;">
                <div style="background: #f0f0f0; border-radius: 10px; height: 8px; overflow: hidden;">
                    <div id="${progressId}-bar" style="background: linear-gradient(90deg, #2196f3, #21cbf3); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                </div>
            </div>

            <div style="font-size: 12px; color: #888;">
                <div id="${progressId}-status">Initializing...</div>
                <div id="${progressId}-step" style="margin-top: 4px;">Step 0 of ${totalSteps}</div>
            </div>
        `;

        document.body.appendChild(progressContainer);
        return progressId;
    }

    /**
     * Updates the progress indicator
     * @param {string} progressId - Progress indicator ID
     * @param {number} currentStep - Current step number
     * @param {number} totalSteps - Total number of steps
     * @param {string} status - Current status message
     */
    function updateProgressIndicator(progressId, currentStep, totalSteps, status) {
        const progressContainer = document.getElementById(progressId);
        if (!progressContainer) return;

        const progressBar = document.getElementById(`${progressId}-bar`);
        const statusElement = document.getElementById(`${progressId}-status`);
        const stepElement = document.getElementById(`${progressId}-step`);

        if (progressBar) {
            const percentage = (currentStep / totalSteps) * 100;
            progressBar.style.width = `${percentage}%`;
        }

        if (statusElement) {
            statusElement.textContent = status;
        }

        if (stepElement) {
            stepElement.textContent = `Step ${currentStep} of ${totalSteps}`;
        }
    }

    /**
     * Hides the progress indicator
     * @param {string} progressId - Progress indicator ID
     */
    function hideProgressIndicator(progressId) {
        const progressContainer = document.getElementById(progressId);
        if (progressContainer && progressContainer.parentNode) {
            progressContainer.style.opacity = '0';
            progressContainer.style.transform = 'translate(-50%, -50%) scale(0.9)';
            progressContainer.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

            setTimeout(() => {
                if (progressContainer.parentNode) {
                    progressContainer.parentNode.removeChild(progressContainer);
                }
            }, 300);
        }
    }

    /**
     * Gets display name for operation
     * @param {string} operation - Operation type
     * @returns {string} Display name
     */
    function getOperationDisplayName(operation) {
        const displayNames = {
            'summarize': 'Creating comprehensive summary...',
            'enhance': 'Enhancing content quality...',
            'extract': 'Extracting key information...',
            'analyze': 'Analyzing content...',
            'translate': 'Translating content...',
            'outline': 'Generating outline...',
            'bullets': 'Creating bullet points...'
        };

        return displayNames[operation] || 'Processing content...';
    }

    // Public API
    window.StashyAI = {
        init,
        generateText,
        processLargePrompt,
        optimizePromptForDirectProcessing,
        summarizeText,
        enhanceText,
        suggestCategories,
        generateBulletPoints,
        extractActionItems,
        correctGrammarAndStyle,
        generateOutline,
        expandContent,
        analyzeContent,
        processLongContent,
        chunkContent,
        getCapabilities,
        isAvailable
    };

    console.log('Stashy AI: Module loaded');

})();
