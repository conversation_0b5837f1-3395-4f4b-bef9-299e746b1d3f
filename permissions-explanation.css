/* Modern CSS Variables for Consistent Theming */
:root {
    --primary-color: #059669;
    --primary-light: #10B981;
    --primary-bg: #D1FAE5;
    --secondary-color: #7C3AED;
    --secondary-light: #8B5CF6;
    --secondary-bg: #EDE9FE;
    --accent-color: #DC2626;
    --accent-light: #EF4444;
    --accent-bg: #FEF2F2;
    --info-color: #2563EB;
    --info-light: #3B82F6;
    --info-bg: #DBEAFE;
    --text-primary: #1F2937;
    --text-secondary: #6B7280;
    --text-muted: #9CA3AF;
    --bg-primary: #FFFFFF;
    --bg-secondary: #F9FAFB;
    --bg-tertiary: #F3F4F6;
    --border-light: #E5E7EB;
    --border-medium: #D1D5DB;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
}

/* Enhanced Typography and Layout */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.7;
    max-width: 900px;
    margin: 0 auto;
    padding: 24px;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    font-size: 16px;
}

/* Enhanced Header Styling */
h1 {
    color: var(--primary-color);
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 16px;
    margin-bottom: 32px;
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    color: var(--primary-color);
    margin-top: 48px;
    margin-bottom: 24px;
    border-bottom: 2px solid var(--primary-bg);
    padding-bottom: 12px;
    font-size: 1.875rem;
    font-weight: 600;
    letter-spacing: -0.025em;
    position: relative;
}

h2::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

/* Enhanced Permission Cards */
.permission {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.permission::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.permission:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.permission h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.25rem;
    font-weight: 600;
    flex-wrap: wrap;
}

.permission-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-bg), var(--primary-light));
    border-radius: var(--radius-md);
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
    border: 2px solid var(--primary-light);
    flex-shrink: 0;
}

/* Enhanced Badges */
.badge {
    font-size: 0.75rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 2px solid;
    transition: all 0.2s ease;
}

.required {
    background: linear-gradient(135deg, var(--accent-bg), #FECACA);
    color: var(--accent-color);
    border-color: var(--accent-light);
}

.optional-badge {
    background: linear-gradient(135deg, var(--info-bg), #BFDBFE);
    color: var(--info-color);
    border-color: var(--info-light);
}

/* Enhanced Permission Reason Boxes */
.permission-reason {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, #E5E7EB 100%);
    padding: 20px;
    border-radius: var(--radius-md);
    margin-top: 16px;
    border-left: 4px solid var(--primary-color);
    position: relative;
}

.permission-reason::before {
    content: '💡';
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 18px;
}

.permission-reason p {
    margin: 12px 0;
    margin-left: 32px;
    color: var(--text-secondary);
    line-height: 1.6;
}

.permission-reason strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* Enhanced Typography */
p {
    margin-bottom: 16px;
    line-height: 1.7;
    color: var(--text-secondary);
}

ul {
    padding-left: 24px;
    margin: 16px 0;
}

li {
    margin: 12px 0;
    line-height: 1.6;
    color: var(--text-secondary);
}

li strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* Enhanced Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--primary-light);
    text-decoration: underline;
}

/* Enhanced Footer */
.last-updated {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    margin-top: 48px;
    padding-top: 24px;
    border-top: 2px solid var(--border-light);
    font-size: 0.875rem;
}

/* Privacy Highlight Section */
.privacy-highlight {
    background: linear-gradient(135deg, var(--primary-bg) 0%, #F0FDF4 100%);
    border: 1px solid var(--primary-light);
    border-radius: var(--radius-lg);
    padding: 24px;
    margin: 32px 0;
    border-left: 4px solid var(--primary-color);
    position: relative;
}

.privacy-highlight::before {
    content: '🛡️';
    position: absolute;
    top: 24px;
    left: 24px;
    font-size: 24px;
}

.privacy-highlight h3 {
    color: var(--primary-color);
    margin-top: 0;
    margin-bottom: 16px;
    margin-left: 40px;
}

.privacy-highlight p {
    margin-left: 40px;
    color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 16px;
        font-size: 14px;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .permission {
        padding: 16px;
    }

    .permission h3 {
        font-size: 1.125rem;
    }

    .permission-icon {
        width: 32px;
        height: 32px;
        font-size: 18px;
    }

    .permission-reason {
        padding: 16px;
    }
}
