/* Modern CSS Variables for Consistent Theming */
:root {
    --primary-color: #10B981;
    --primary-light: #34D399;
    --primary-bg: #D1FAE5;
    --secondary-color: #6366F1;
    --secondary-light: #818CF8;
    --secondary-bg: #E0E7FF;
    --accent-color: #F59E0B;
    --accent-light: #FCD34D;
    --accent-bg: #FEF3C7;
    --text-primary: #1F2937;
    --text-secondary: #6B7280;
    --text-muted: #9CA3AF;
    --bg-primary: #FFFFFF;
    --bg-secondary: #F9FAFB;
    --bg-tertiary: #F3F4F6;
    --border-light: #E5E7EB;
    --border-medium: #D1D5DB;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
}

/* Enhanced Typography and Layout */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.7;
    max-width: 900px;
    margin: 0 auto;
    padding: 24px;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    font-size: 16px;
}

/* Enhanced Header Styling */
h1 {
    color: var(--primary-color);
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 16px;
    margin-bottom: 32px;
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    color: var(--primary-color);
    margin-top: 48px;
    margin-bottom: 24px;
    border-bottom: 2px solid var(--primary-bg);
    padding-bottom: 12px;
    font-size: 1.875rem;
    font-weight: 600;
    letter-spacing: -0.025em;
    position: relative;
}

h2::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

h3 {
    color: var(--text-primary);
    margin-top: 32px;
    margin-bottom: 16px;
    font-size: 1.5rem;
    font-weight: 600;
}

h4 {
    color: var(--text-primary);
    margin-top: 24px;
    margin-bottom: 12px;
    font-size: 1.25rem;
    font-weight: 600;
}

/* Enhanced Feature Cards */
.feature-card {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.feature-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
}

.feature-icon {
    font-size: 28px;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-bg), var(--primary-light));
    border-radius: var(--radius-lg);
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
    border: 2px solid var(--primary-light);
}

.feature-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}
/* Enhanced Step-by-Step Instructions */
.steps {
    margin: 24px 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.step {
    padding: 20px;
    background: linear-gradient(135deg, #F0F9FF 0%, #E0F2FE 100%);
    border: 1px solid var(--secondary-light);
    border-left: 4px solid var(--secondary-color);
    border-radius: var(--radius-md);
    position: relative;
    transition: all 0.3s ease;
}

.step:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: var(--secondary-color);
    color: white;
    border-radius: 50%;
    font-weight: 700;
    font-size: 14px;
    margin-right: 12px;
    flex-shrink: 0;
}

/* Enhanced Alert Boxes */
.tip {
    background: linear-gradient(135deg, var(--accent-bg) 0%, #FEF9C3 100%);
    border: 1px solid var(--accent-light);
    border-radius: var(--radius-md);
    padding: 20px;
    margin: 24px 0;
    border-left: 4px solid var(--accent-color);
    position: relative;
}

.tip::before {
    content: '💡';
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 20px;
}

.tip strong {
    color: var(--accent-color);
    margin-left: 32px;
}

.warning {
    background: linear-gradient(135deg, #FEF2F2 0%, #FECACA 100%);
    border: 1px solid #F87171;
    border-radius: var(--radius-md);
    padding: 20px;
    margin: 24px 0;
    border-left: 4px solid #EF4444;
    position: relative;
}

.warning::before {
    content: '⚠️';
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 20px;
}

.warning strong {
    color: #DC2626;
    margin-left: 32px;
}

/* Enhanced Keyboard Shortcuts */
.keyboard-shortcut {
    background: linear-gradient(135deg, var(--bg-tertiary), #E5E7EB);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.875rem;
    font-weight: 600;
    border: 1px solid var(--border-medium);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* Enhanced Images */
img {
    max-width: 100%;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    margin: 24px 0;
    transition: transform 0.3s ease;
}

img:hover {
    transform: scale(1.02);
}

/* Enhanced Table of Contents */
.toc {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: 32px;
    margin-bottom: 40px;
    box-shadow: var(--shadow-sm);
}

.toc h2 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--text-primary);
    border-bottom: 2px solid var(--primary-bg);
    padding-bottom: 12px;
    font-size: 1.5rem;
}

.toc ul {
    margin: 0;
    padding-left: 0;
    list-style: none;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 12px;
}

.toc li {
    margin: 0;
    padding: 8px 16px;
    background: var(--bg-primary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-light);
    transition: all 0.2s ease;
}

.toc li:hover {
    background: var(--primary-bg);
    border-color: var(--primary-light);
    transform: translateY(-1px);
}

.toc a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: block;
}

.toc a:hover {
    color: var(--primary-light);
}

/* Enhanced Provider Cards */
.provider-list {
    margin: 24px 0;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.provider {
    background: linear-gradient(135deg, var(--bg-primary) 0%, #F8FAFC 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: 24px;
    border-left: 4px solid var(--secondary-color);
    transition: all 0.3s ease;
    position: relative;
    box-shadow: var(--shadow-sm);
}

.provider:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-left-color: var(--secondary-light);
}

.provider strong {
    color: var(--secondary-color);
    font-size: 1.125rem;
    font-weight: 600;
    display: block;
    margin-bottom: 8px;
}

.provider p {
    margin: 12px 0;
    color: var(--text-secondary);
    line-height: 1.6;
}

.provider ul {
    margin: 16px 0;
    padding-left: 20px;
}

.provider li {
    margin: 8px 0;
    color: var(--text-secondary);
}

.provider a {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.provider a:hover {
    color: var(--secondary-light);
    text-decoration: underline;
}

/* Enhanced Typography */
ul, ol {
    padding-left: 24px;
    margin: 16px 0;
}

li {
    margin: 12px 0;
    line-height: 1.6;
}

li strong {
    color: var(--text-primary);
    font-weight: 600;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--primary-light);
    text-decoration: underline;
}

code {
    background-color: var(--bg-tertiary);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.875em;
    color: var(--text-primary);
    border: 1px solid var(--border-light);
}

/* Enhanced Footer */
.last-updated {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    margin-top: 48px;
    padding-top: 24px;
    border-top: 2px solid var(--border-light);
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 16px;
        font-size: 14px;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .feature-card {
        padding: 20px;
    }

    .toc ul {
        grid-template-columns: 1fr;
    }

    .step {
        padding: 16px;
    }

    .feature-icon {
        width: 48px;
        height: 48px;
        font-size: 24px;
    }
}
