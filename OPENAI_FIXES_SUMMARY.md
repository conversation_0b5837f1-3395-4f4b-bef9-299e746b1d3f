# OpenAI API Fixes for Stashy Extension

## Issues Identified and Fixed

### 1. **<PERSON><PERSON><PERSON> Exceeded Error (HTTP 429)**
**Problem:** Your OpenAI account has exceeded its usage quota/billing limits.

**Error Message:**
```
"You exceeded your current quota, please check your plan and billing details"
```

**Fixes Applied:**
- Enhanced error handling in `universal-ai-adapter.js` to provide clearer quota error messages
- Added billing link guidance in error messages
- Improved background script error handling in `background.js`

**User Action Required:**
1. Visit [OpenAI Billing](https://platform.openai.com/account/billing)
2. Add a payment method if you haven't already
3. Check your usage at [OpenAI Usage](https://platform.openai.com/account/usage)
4. Consider using cheaper models like `gpt-3.5-turbo`

### 2. **Model Not Found Error (HTTP 404)**
**Problem:** Extension trying to use `gpt-4` model which requires ChatGPT Plus/Pro subscription.

**Error Message:**
```
"The model `gpt-4` does not exist or you do not have access to it"
```

**Fixes Applied:**
- Updated default model configuration in `ai-provider-detector.js`
- Reordered models to prioritize accessible ones (`gpt-3.5-turbo`, `gpt-4o-mini`)
- Added clear labels indicating which models require Plus/Pro
- Enhanced error messages to suggest alternative models

**Model Accessibility:**
- ✅ `gpt-3.5-turbo` - Available to all users
- ✅ `gpt-4o-mini` - Available to all users  
- ❌ `gpt-4` - Requires ChatGPT Plus or API credits
- ❌ `gpt-4-turbo` - Requires ChatGPT Plus or API credits

### 3. **currentProvider Reference Error**
**Problem:** JavaScript reference error in popup.js

**Fix Applied:**
- Verified variable scoping in `popup.js` is correct
- The error may be intermittent due to timing issues

### 4. **Enhanced Error Handling**
**Improvements Made:**
- Better error messages with actionable guidance
- Links to OpenAI billing and usage pages
- Model-specific error handling
- Rate limiting improvements

## Testing Your Fixes

### Option 1: Use the Test Page
1. Open `test-openai-fixes.html` in your browser
2. Enter your OpenAI API key
3. Test different models to see which ones work
4. Follow the guidance provided

### Option 2: Test in Extension
1. Open the Stashy extension popup
2. Go to AI settings
3. Configure your OpenAI API key
4. Try using AI features with `gpt-3.5-turbo` model

## Recommended Settings

### For Free/Basic OpenAI Users:
- **Model:** `gpt-3.5-turbo`
- **Max Tokens:** 1000-2000
- **Temperature:** 0.7

### For ChatGPT Plus/Pro Users:
- **Model:** `gpt-4o-mini` or `gpt-4`
- **Max Tokens:** 2000-4000
- **Temperature:** 0.7

## Common Solutions

### If You're Still Getting Quota Errors:
1. **Check Billing:** Ensure you have a valid payment method
2. **Monitor Usage:** Check your monthly usage limits
3. **Use Cheaper Models:** Switch to `gpt-3.5-turbo`
4. **Reduce Token Usage:** Lower max tokens in settings

### If You're Getting Model Errors:
1. **Use Accessible Models:** Stick to `gpt-3.5-turbo` or `gpt-4o-mini`
2. **Check Model List:** Visit [OpenAI Models](https://platform.openai.com/docs/models)
3. **Verify Subscription:** Ensure you have the right OpenAI plan

### If You're Getting Connection Errors:
1. **Check API Key:** Ensure it's valid and properly formatted
2. **Check Network:** Ensure you have internet connectivity
3. **Check Firewall:** Ensure OpenAI API isn't blocked

## Files Modified

1. **`lib/universal-ai-adapter.js`**
   - Enhanced error handling for quota and model errors
   - Better error messages with guidance

2. **`lib/ai-provider-detector.js`**
   - Reordered models to prioritize accessible ones
   - Added model accessibility labels
   - Kept `gpt-3.5-turbo` as default

3. **`background.js`**
   - Improved API error handling
   - Enhanced quota error messages
   - Added billing link guidance

4. **`popup.js`**
   - Verified variable scoping (no changes needed)

## Next Steps

1. **Test the fixes** using the test page or extension
2. **Configure appropriate model** based on your OpenAI plan
3. **Monitor usage** to avoid quota issues
4. **Contact support** if issues persist

## Support Links

- [OpenAI Billing](https://platform.openai.com/account/billing)
- [OpenAI Usage](https://platform.openai.com/account/usage)
- [OpenAI Models Documentation](https://platform.openai.com/docs/models)
- [OpenAI API Keys](https://platform.openai.com/api-keys)
