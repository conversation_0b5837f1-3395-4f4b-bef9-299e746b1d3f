/**
 * Stashy Settings UI
 * Handles the settings page user interface
 */

document.addEventListener('DOMContentLoaded', () => {
    // Get DOM elements
    const defaultWidthInput = document.getElementById('default-width');
    const defaultHeightInput = document.getElementById('default-height');
    const defaultTopInput = document.getElementById('default-top');
    const defaultRightInput = document.getElementById('default-right');
    const saveButton = document.getElementById('save-button');
    const resetButton = document.getElementById('reset-button');

    // Load settings and populate form
    loadSettingsToForm();

    saveButton.addEventListener('click', saveFormSettings);
    resetButton.addEventListener('click', resetSettings);

    /**
     * Loads settings from storage and populates the form
     */
    function loadSettingsToForm() {
        // Use the settings module to get current settings
        window.StashySettings.loadSettings().then(settings => {
            // Remove 'px' from size values and convert to numbers
            defaultWidthInput.value = parseInt(settings.defaultNoteSize.width);
            defaultHeightInput.value = parseInt(settings.defaultNoteSize.height);
            defaultTopInput.value = parseInt(settings.defaultNotePosition.top);
            defaultRightInput.value = parseInt(settings.defaultNotePosition.right);
        });
    }



    /**
     * Saves the form settings to storage
     */
    function saveFormSettings() {
        // Get values from form
        const defaultWidth = defaultWidthInput.value + 'px';
        const defaultHeight = defaultHeightInput.value + 'px';
        const defaultTop = defaultTopInput.value + 'px';
        const defaultRight = defaultRightInput.value + 'px';

        // Update settings
        window.StashySettings.updateSetting('defaultNoteSize.width', defaultWidth);
        window.StashySettings.updateSetting('defaultNoteSize.height', defaultHeight);
        window.StashySettings.updateSetting('defaultNotePosition.top', defaultTop);
        window.StashySettings.updateSetting('defaultNotePosition.right', defaultRight);

        // Save all settings
        window.StashySettings.saveSettings().then(() => {
            showSaveConfirmation();
        });
    }

    /**
     * Resets settings to defaults
     */
    function resetSettings() {
        if (confirm('Reset all settings to default values?')) {
            // Reset to defaults
            window.StashySettings.loadSettings().then(() => {
                // Reload form with default values
                loadSettingsToForm();
                // Show confirmation
                showResetConfirmation();
            });
        }
    }

    /**
     * Shows a save confirmation message
     */
    function showSaveConfirmation() {
        const message = document.createElement('div');
        message.className = 'save-confirmation';
        message.textContent = 'Settings saved successfully!';
        message.style.position = 'fixed';
        message.style.bottom = '20px';
        message.style.left = '50%';
        message.style.transform = 'translateX(-50%)';
        message.style.backgroundColor = '#4caf50';
        message.style.color = 'white';
        message.style.padding = '10px 20px';
        message.style.borderRadius = '4px';
        message.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        message.style.zIndex = '1000';

        document.body.appendChild(message);

        // Remove after 3 seconds
        setTimeout(() => {
            message.style.opacity = '0';
            message.style.transition = 'opacity 0.5s ease';
            setTimeout(() => {
                document.body.removeChild(message);
            }, 500);
        }, 3000);
    }

    /**
     * Shows a reset confirmation message
     */
    function showResetConfirmation() {
        const message = document.createElement('div');
        message.className = 'reset-confirmation';
        message.textContent = 'Settings reset to defaults';
        message.style.position = 'fixed';
        message.style.bottom = '20px';
        message.style.left = '50%';
        message.style.transform = 'translateX(-50%)';
        message.style.backgroundColor = '#ff9800';
        message.style.color = 'white';
        message.style.padding = '10px 20px';
        message.style.borderRadius = '4px';
        message.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        message.style.zIndex = '1000';

        document.body.appendChild(message);

        // Remove after 3 seconds
        setTimeout(() => {
            message.style.opacity = '0';
            message.style.transition = 'opacity 0.5s ease';
            setTimeout(() => {
                document.body.removeChild(message);
            }, 500);
        }, 3000);
    }
});
