/**
 * Dashboard Events System for Stashy
 * 
 * This module provides an event system for the Stashy dashboard.
 */

// Create a namespace to avoid global pollution
window.StashyDashboardEvents = (function() {
    // Private variables
    let isInitialized = false;
    let selectedItems = [];
    
    /**
     * Initializes the dashboard events system
     * @returns {Promise<boolean>} A promise that resolves to true if initialization was successful
     */
    async function init() {
        try {
            // Check if already initialized
            if (isInitialized) {
                return true;
            }
            
            // Add event listeners
            addEventListeners();
            
            isInitialized = true;
            return true;
        } catch (error) {
            console.error('Error initializing dashboard events system:', error);
            return false;
        }
    }
    
    /**
     * Adds event listeners
     */
    function addEventListeners() {
        // Listen for checkbox changes in the dashboard
        document.addEventListener('change', function(event) {
            if (event.target.classList.contains('Stashy-item-checkbox')) {
                handleItemCheckboxChange(event);
            }
        });
        
        // Listen for "Select All" checkbox changes
        document.addEventListener('change', function(event) {
            if (event.target.id === 'Stashy-select-all') {
                handleSelectAllChange(event);
            }
        });
    }
    
    /**
     * Handles item checkbox changes
     * @param {Event} event - The change event
     */
    function handleItemCheckboxChange(event) {
        // Get all checked checkboxes
        const checkedCheckboxes = document.querySelectorAll('.Stashy-item-checkbox:checked');
        
        // Get selected items
        selectedItems = Array.from(checkedCheckboxes).map(checkbox => {
            const itemElement = checkbox.closest('.Stashy-dashboard-item');
            if (!itemElement) return null;
            
            // Get item data
            const itemId = itemElement.dataset.itemId;
            const itemType = itemElement.dataset.itemType;
            const itemUrl = itemElement.dataset.itemUrl;
            const itemText = itemElement.querySelector('.Stashy-item-text')?.textContent || '';
            
            return {
                id: itemId,
                type: itemType,
                url: itemUrl,
                text: itemText
            };
        }).filter(Boolean); // Remove null items
        
        // Dispatch selection changed event
        dispatchSelectionChangedEvent();
    }
    
    /**
     * Handles "Select All" checkbox changes
     * @param {Event} event - The change event
     */
    function handleSelectAllChange(event) {
        // Get all item checkboxes
        const itemCheckboxes = document.querySelectorAll('.Stashy-item-checkbox');
        
        // Set all checkboxes to the same state as the "Select All" checkbox
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = event.target.checked;
        });
        
        // Update selected items
        if (event.target.checked) {
            // Select all items
            selectedItems = Array.from(itemCheckboxes).map(checkbox => {
                const itemElement = checkbox.closest('.Stashy-dashboard-item');
                if (!itemElement) return null;
                
                // Get item data
                const itemId = itemElement.dataset.itemId;
                const itemType = itemElement.dataset.itemType;
                const itemUrl = itemElement.dataset.itemUrl;
                const itemText = itemElement.querySelector('.Stashy-item-text')?.textContent || '';
                
                return {
                    id: itemId,
                    type: itemType,
                    url: itemUrl,
                    text: itemText
                };
            }).filter(Boolean); // Remove null items
        } else {
            // Deselect all items
            selectedItems = [];
        }
        
        // Dispatch selection changed event
        dispatchSelectionChangedEvent();
    }
    
    /**
     * Dispatches a selection changed event
     */
    function dispatchSelectionChangedEvent() {
        // Create custom event
        const event = new CustomEvent('StashySelectionChanged', {
            detail: {
                selectedItems: selectedItems
            }
        });
        
        // Dispatch event
        document.dispatchEvent(event);
    }
    
    /**
     * Gets the selected items
     * @returns {Array} The selected items
     */
    function getSelectedItems() {
        return [...selectedItems]; // Return a copy
    }
    
    // Return the public API
    return {
        init,
        getSelectedItems,
        isInitialized: () => isInitialized
    };
})();

// Initialize when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize after a short delay to ensure dashboard is loaded
    setTimeout(() => {
        window.StashyDashboardEvents.init();
    }, 1000);
});

console.log("Stashy: Dashboard Events System Loaded");
