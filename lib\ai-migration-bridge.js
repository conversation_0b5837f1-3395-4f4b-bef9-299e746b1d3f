/**
 * AI Migration Bridge
 * Provides backward compatibility while migrating to universal AI system
 * Maintains the existing window.StashyAI interface while using the new universal adapter
 */

(function() {
    'use strict';

    // Store reference to original Google AI integration if it exists
    let originalStashyAI = null;
    let universalAdapter = null;
    let isUniversalMode = false;

    // AI Configuration (migrated from google-ai-integration.js)
    const AI_CONFIG = {
        models: {
            gemini: 'gemini-2.5-flash',
            geminiPro: 'gemini-2.5-pro',
            geminiFlash: 'gemini-2.5-flash'
        },
        maxTokens: 6048, // Massively increased for Deep Research
        temperature: 0.7,
        enableChunking: true,
        autoChunkThreshold: 8000,
        chunkSize: 6000,
        chunkOverlap: 200
    };

    // AI Capabilities tracking
    let aiCapabilities = {
        available: false,
        models: [],
        features: {
            textGeneration: false,
            summarization: false,
            enhancement: false
        }
    };

    /**
     * Initializes the AI system with universal provider support
     * @returns {Promise<boolean>} Success status
     */
    async function init() {
        try {
            console.log('AI Migration Bridge: Initializing...');

            // Check if universal AI adapter is available
            console.log('AI Migration Bridge: Checking for universal AI adapter...');
            console.log('AI Migration Bridge: window.universalAiAdapter exists:', !!window.universalAiAdapter);

            if (window.universalAiAdapter) {
                universalAdapter = window.universalAiAdapter;
                console.log('AI Migration Bridge: Universal adapter found, checking if ready...');
                console.log('AI Migration Bridge: Universal adapter isReady():', universalAdapter.isReady());

                // Check if universal adapter is ready
                if (universalAdapter.isReady()) {
                    console.log('AI Migration Bridge: Using universal AI adapter');
                    isUniversalMode = true;
                    aiCapabilities.available = true;
                    aiCapabilities.features = {
                        textGeneration: true,
                        summarization: true,
                        enhancement: true
                    };

                    // Get available models from current provider
                    const models = universalAdapter.getAvailableModels();
                    aiCapabilities.models = models;

                    // Store reference to original StashyAI before replacing it
                    if (window.StashyAI && window.StashyAI !== bridgeAPI) {
                        originalStashyAI = window.StashyAI;
                    }

                    return true;
                } else {
                    console.log('AI Migration Bridge: Universal adapter not ready, checking for saved config...');
                }
            } else {
                console.log('AI Migration Bridge: Universal adapter not found');
            }

            // Check if we have a universal AI configuration but adapter isn't ready
            try {
                const result = await chrome.storage.local.get(['universalAiConfig']);
                const universalConfig = result.universalAiConfig;

                if (universalConfig && universalConfig.apiKey && universalConfig.providerConfig) {
                    console.log('AI Migration Bridge: Found universal AI config, attempting to initialize adapter...');

                    // Try to initialize the universal adapter if it exists but isn't ready
                    if (window.universalAiAdapter && !window.universalAiAdapter.isReady()) {
                        try {
                            await window.universalAiAdapter.initialize(
                                universalConfig.providerId,
                                universalConfig.apiKey,
                                universalConfig.providerConfig
                            );

                            // Now check if it's ready
                            if (window.universalAiAdapter.isReady()) {
                                console.log('AI Migration Bridge: Successfully initialized universal adapter');
                                universalAdapter = window.universalAiAdapter;
                                isUniversalMode = true;
                                aiCapabilities.available = true;
                                aiCapabilities.features = {
                                    textGeneration: true,
                                    summarization: true,
                                    enhancement: true
                                };
                                const models = universalAdapter.getAvailableModels();
                                aiCapabilities.models = models;
                                return true;
                            }
                        } catch (initError) {
                            console.warn('AI Migration Bridge: Failed to initialize universal adapter:', initError.message);
                        }
                    }

                    console.log('AI Migration Bridge: Universal provider detected, preventing legacy Google AI initialization');
                    return false;
                }
            } catch (configError) {
                console.warn('AI Migration Bridge: Error checking universal AI config:', configError);
            }

            // Fallback to original Google AI integration only if no universal config exists
            if (window.StashyAI && typeof window.StashyAI.init === 'function' && window.StashyAI !== bridgeAPI) {
                console.log('AI Migration Bridge: Falling back to original Google AI integration');
                originalStashyAI = window.StashyAI;
                const result = await originalStashyAI.init();
                aiCapabilities = originalStashyAI.getCapabilities ? originalStashyAI.getCapabilities() : aiCapabilities;
                return result;
            }

            console.warn('AI Migration Bridge: No AI system available');
            return false;

        } catch (error) {
            console.error('AI Migration Bridge: Initialization error:', error);
            return false;
        }
    }

    /**
     * Generates text using the current AI provider
     * @param {string} prompt - The text prompt
     * @param {Object} options - Generation options
     * @returns {Promise<string>} Generated text
     */
    async function generateText(prompt, options = {}) {
        if (isUniversalMode && universalAdapter) {
            // Use universal adapter
            return await universalAdapter.generateText(prompt, options);
        } else if (originalStashyAI) {
            // Use original Google AI integration
            return await originalStashyAI.generateText(prompt, options);
        } else {
            throw new Error('No AI system available. Please configure an API key.');
        }
    }

    /**
     * Generates text with enhanced page content extraction
     * @param {string} prompt - The text prompt
     * @param {Object} options - Generation options including usePageContent
     * @returns {Promise<string>} Generated text
     */
    async function generateTextWithContent(prompt, options = {}) {
        if (isUniversalMode && universalAdapter && universalAdapter.generateTextWithContent) {
            console.log('AI Migration Bridge: Using universal adapter for enhanced content generation');
            return await universalAdapter.generateTextWithContent(prompt, options);
        } else {
            // Fallback to regular generation
            console.log('AI Migration Bridge: Enhanced content generation not available, using regular generation');
            return await generateText(prompt, options);
        }
    }

    /**
     * Extracts page content using Readability.js
     * @returns {Object} Extracted content with metadata
     */
    function extractPageContent() {
        if (isUniversalMode && universalAdapter && universalAdapter.extractPageContent) {
            return universalAdapter.extractPageContent();
        } else {
            // Basic fallback
            return {
                content: document.body ? document.body.innerText : '',
                title: document.title || 'Untitled Page',
                url: window.location.href,
                siteName: window.location.hostname,
                success: true,
                extractionMethod: 'fallback'
            };
        }
    }

    /**
     * Summarizes text using the current AI provider
     * @param {string} text - Text to summarize
     * @param {Object} options - Summarization options
     * @returns {Promise<string>} Summary
     */
    async function summarizeText(text, options = {}) {
        if (isUniversalMode && universalAdapter) {
            return await universalAdapter.summarizeText(text, options);
        } else if (originalStashyAI) {
            return await originalStashyAI.summarizeText(text, options);
        } else {
            throw new Error('No AI system available. Please configure an API key.');
        }
    }

    /**
     * Enhances text using the current AI provider
     * @param {string} text - Text to enhance
     * @param {Object} options - Enhancement options
     * @returns {Promise<string>} Enhanced text
     */
    async function enhanceText(text, options = {}) {
        if (isUniversalMode && universalAdapter) {
            return await universalAdapter.enhanceText(text, options);
        } else if (originalStashyAI) {
            return await originalStashyAI.enhanceText(text, options);
        } else {
            throw new Error('No AI system available. Please configure an API key.');
        }
    }

    /**
     * Corrects grammar and style
     * @param {string} text - Text to correct
     * @param {Object} options - Correction options
     * @returns {Promise<string>} Corrected text
     */
    async function correctGrammarAndStyle(text, options = {}) {
        if (isUniversalMode && universalAdapter) {
            return await universalAdapter.correctGrammarAndStyle(text, options);
        } else if (originalStashyAI) {
            return await originalStashyAI.correctGrammarAndStyle(text, options);
        } else {
            throw new Error('No AI system available. Please configure an API key.');
        }
    }

    /**
     * Analyzes content
     * @param {string} content - Content to analyze
     * @param {Object} options - Analysis options
     * @returns {Promise<string>} Analysis result
     */
    async function analyzeContent(content, options = {}) {
        if (isUniversalMode && universalAdapter) {
            const prompt = `Analyze this content for ${options.type || 'general'} insights:\n\n${content}\n\nAnalysis:`;
            return await universalAdapter.generateText(prompt, {
                maxTokens: options.maxTokens || 2800, // Increased by 2000
                temperature: options.temperature || 0.3
            });
        } else if (originalStashyAI) {
            return await originalStashyAI.analyzeContent(content, options);
        } else {
            throw new Error('No AI system available. Please configure an API key.');
        }
    }

    /**
     * Suggests categories for content
     * @param {string} content - Content to categorize
     * @param {Array} existingCategories - Existing categories
     * @returns {Promise<Array>} Suggested categories
     */
    async function suggestCategories(content, existingCategories = []) {
        const existingCatsText = existingCategories.length > 0 ? 
            `\n\nExisting categories: ${existingCategories.join(', ')}\n` : '';

        const prompt = `Suggest 3-5 categories for this content:${existingCatsText}\n\n${content}\n\nCategories:`;

        let response;
        if (isUniversalMode && universalAdapter) {
            response = await universalAdapter.generateText(prompt, {
                maxTokens: 150,
                temperature: 0.4
            });
        } else if (originalStashyAI) {
            return await originalStashyAI.suggestCategories(content, existingCategories);
        } else {
            throw new Error('No AI system available. Please configure an API key.');
        }

        if (!response || typeof response !== 'string') {
            return [];
        }

        return response.split(',').map(cat => cat.trim()).filter(cat => cat.length > 0 && cat.length < 50);
    }

    /**
     * Generates bullet points from content
     * @param {string} content - Content to convert
     * @param {Object} options - Generation options
     * @returns {Promise<string>} Bullet points
     */
    async function generateBulletPoints(content, options = {}) {
        if (isUniversalMode && universalAdapter) {
            const prompt = `Convert this content into clear bullet points:\n\n${content}\n\nBullet Points:`;
            return await universalAdapter.generateText(prompt, {
                maxTokens: options.maxTokens || 600,
                temperature: options.temperature || 0.3
            });
        } else if (originalStashyAI) {
            return await originalStashyAI.generateBulletPoints(content, options);
        } else {
            throw new Error('No AI system available. Please configure an API key.');
        }
    }

    /**
     * Extracts action items from content
     * @param {string} content - Content to analyze
     * @param {Object} options - Extraction options
     * @returns {Promise<string>} Action items
     */
    async function extractActionItems(content, options = {}) {
        if (isUniversalMode && universalAdapter) {
            const prompt = `Extract actionable items and tasks from this content:\n\n${content}\n\nAction Items:`;
            return await universalAdapter.generateText(prompt, {
                maxTokens: options.maxTokens || 2500, // Increased by 2000
                temperature: options.temperature || 0.2
            });
        } else if (originalStashyAI) {
            return await originalStashyAI.extractActionItems(content, options);
        } else {
            throw new Error('No AI system available. Please configure an API key.');
        }
    }

    /**
     * Generates an outline from content
     * @param {string} content - Content to outline
     * @param {Object} options - Outline options
     * @returns {Promise<string>} Content outline
     */
    async function generateOutline(content, options = {}) {
        if (isUniversalMode && universalAdapter) {
            const prompt = `Create a structured outline for this content:\n\n${content}\n\nOutline:`;
            return await universalAdapter.generateText(prompt, {
                maxTokens: options.maxTokens || 800,
                temperature: options.temperature || 0.3
            });
        } else if (originalStashyAI) {
            return await originalStashyAI.generateOutline(content, options);
        } else {
            throw new Error('No AI system available. Please configure an API key.');
        }
    }

    /**
     * Expands content with additional details
     * @param {string} content - Content to expand
     * @param {Object} options - Expansion options
     * @returns {Promise<string>} Expanded content
     */
    async function expandContent(content, options = {}) {
        if (isUniversalMode && universalAdapter) {
            const prompt = `Expand this content with additional details and context:\n\n${content}\n\nExpanded Content:`;
            return await universalAdapter.generateText(prompt, {
                maxTokens: options.maxTokens || 1200,
                temperature: options.temperature || 0.5
            });
        } else if (originalStashyAI) {
            return await originalStashyAI.expandContent(content, options);
        } else {
            throw new Error('No AI system available. Please configure an API key.');
        }
    }

    /**
     * Processes large prompts by chunking
     * @param {string} content - Large content to process
     * @param {string} operation - Operation to perform
     * @param {Object} options - Processing options
     * @returns {Promise<string>} Processed result
     */
    async function processLargePrompt(content, operation, options = {}) {
        if (originalStashyAI && originalStashyAI.processLargePrompt) {
            return await originalStashyAI.processLargePrompt(content, operation, options);
        } else {
            // Simplified chunking for universal mode
            if (content.length <= AI_CONFIG.autoChunkThreshold) {
                return await generateText(content, options);
            }
            
            // Basic chunking implementation
            const chunks = chunkContent(content);
            const results = [];
            
            for (let i = 0; i < chunks.length; i++) {
                const result = await generateText(chunks[i], options);
                results.push(result);
            }
            
            return results.join('\n\n');
        }
    }

    /**
     * Chunks content into smaller pieces
     * @param {string} content - Content to chunk
     * @param {Object} options - Chunking options
     * @returns {Array} Content chunks
     */
    function chunkContent(content, options = {}) {
        if (originalStashyAI && originalStashyAI.chunkContent) {
            return originalStashyAI.chunkContent(content, options);
        }
        
        // Basic chunking implementation
        const chunkSize = options.chunkSize || AI_CONFIG.chunkSize;
        const chunks = [];
        
        for (let i = 0; i < content.length; i += chunkSize) {
            chunks.push(content.slice(i, i + chunkSize));
        }
        
        return chunks;
    }

    /**
     * Gets current AI capabilities
     * @returns {Object} AI capabilities
     */
    function getCapabilities() {
        if (isUniversalMode) {
            return { ...aiCapabilities };
        } else if (originalStashyAI && originalStashyAI.getCapabilities) {
            return originalStashyAI.getCapabilities();
        } else {
            return { ...aiCapabilities };
        }
    }

    /**
     * Checks if AI is available
     * @returns {boolean} Availability status
     */
    function isAvailable() {
        if (isUniversalMode) {
            return aiCapabilities.available;
        } else if (originalStashyAI && originalStashyAI.isAvailable) {
            return originalStashyAI.isAvailable();
        } else {
            return false;
        }
    }

    // Create the bridge API that maintains backward compatibility
    const bridgeAPI = {
        init,
        generateText,
        generateTextWithContent,
        extractPageContent,
        summarizeText,
        enhanceText,
        suggestCategories,
        generateBulletPoints,
        extractActionItems,
        correctGrammarAndStyle,
        generateOutline,
        expandContent,
        analyzeContent,
        processLargePrompt,
        chunkContent,
        getCapabilities,
        isAvailable,
        
        // Additional properties for compatibility
        processLongContent: processLargePrompt,
        optimizePromptForDirectProcessing: (prompt) => prompt // Pass-through for now
    };

    // Store reference to original StashyAI if it exists
    if (window.StashyAI && typeof window.StashyAI === 'object') {
        originalStashyAI = window.StashyAI;
        console.log('AI Migration Bridge: Stored reference to original StashyAI');
    }

    // Replace window.StashyAI with the bridge immediately
    window.StashyAI = bridgeAPI;

    console.log('AI Migration Bridge: Loaded and ready, bridge API installed');

    // Listen for reinitialization messages from popup
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
        chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
            if (message.type === 'REINITIALIZE_AI_ADAPTER') {
                console.log('AI Migration Bridge: Received reinitialization request');

                // Reinitialize the universal adapter with new configuration
                if (window.universalAiAdapter && message.providerId && message.apiKey && message.providerConfig) {
                    window.universalAiAdapter.initialize(
                        message.providerId,
                        message.apiKey,
                        message.providerConfig
                    ).then(() => {
                        console.log('AI Migration Bridge: Successfully reinitialized with new configuration');

                        // Reinitialize the bridge to use the updated adapter
                        init().then(() => {
                            console.log('AI Migration Bridge: Bridge reinitialized successfully');
                            sendResponse({ success: true });
                        }).catch(error => {
                            console.error('AI Migration Bridge: Error reinitializing bridge:', error);
                            sendResponse({ success: false, error: error.message });
                        });
                    }).catch(error => {
                        console.error('AI Migration Bridge: Error reinitializing adapter:', error);
                        sendResponse({ success: false, error: error.message });
                    });

                    return true; // Keep message channel open for async response
                }
            }
        });
    }

})();
