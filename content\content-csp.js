// --- START OF FILE content-csp.js ---

/**
 * Simplified Content Security Policy (CSP) Implementation for Stashy
 *
 * This module provides basic CSP protections without aggressive sandboxing
 * that was causing compatibility issues.
 */

// Create a namespace to avoid global pollution
window.StashyCSP = (function() {

    /**
     * SECURITY ENHANCED: Stricter CSP policy directives for Stashy content
     */
    const CSP_DIRECTIVES = {
        'script-src': ["'self'"], // SECURITY FIX: Removed 'unsafe-inline' - only allow extension scripts
        'style-src': ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],  // Allow Google Fonts
        'img-src': ["'self'", "data:", "https://*", "file://*"],    // Allow images from various sources
        'font-src': ["'self'", "https://fonts.gstatic.com"],  // Allow Google Fonts
        'connect-src': ["'self'", "https://*.googleapis.com", "https://www.googleapis.com", "https://oauth2.googleapis.com"],  // SECURITY FIX: Restricted to specific Google APIs only
        'frame-src': ["'none'"],  // Block iframes for security
        'object-src': ["'none'"],  // Block objects and embeds
        'worker-src': ["'self'"],  // SECURITY FIX: Only allow local workers
        'base-uri': ["'self'"]  // SECURITY FIX: Prevent base URI manipulation
    };

    /**
     * Builds a CSP policy string from the directives
     * @returns {string} The CSP policy string
     */
    function buildCSPString() {
        return Object.entries(CSP_DIRECTIVES)
            .map(([directive, sources]) => {
                if (sources.length === 0) {
                    return directive;
                }
                return `${directive} ${sources.join(' ')}`;
            })
            .join('; ');
    }

    /**
     * Validates if a URL is safe for use
     * @param {string} url - The URL to validate
     * @returns {boolean} True if the URL is safe
     */
    function isURLSafe(url) {
        try {
            const parsedURL = new URL(url);
            // Allow http, https, data, and file protocols
            return ['http:', 'https:', 'data:', 'file:'].includes(parsedURL.protocol);
        } catch (e) {
            return false;
        }
    }

    /**
     * Sanitizes HTML content for safe rendering
     * @param {string} content - The HTML content to sanitize
     * @returns {string} The sanitized HTML content
     */
    function sanitizeContent(content) {
        if (!content || typeof content !== 'string') {
            return '';
        }

        // Use the sanitization manager if available
        if (window.StashySanitizationManager && typeof window.StashySanitizationManager.sanitize === 'function') {
            return window.StashySanitizationManager.sanitize(content);
        }

        // Use DOMPurify if available
        if (window.DOMPurify && typeof window.DOMPurify.sanitize === 'function') {
            return window.DOMPurify.sanitize(content);
        }

        // Simple fallback sanitization
        return content
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
            .replace(/javascript:/gi, 'removed:')
            .replace(/\son\w+\s*=\s*["'][^"']*["']/gi, '');
    }

    /**
     * Renders HTML content securely in a container
     * @param {HTMLElement} container - The container to render in
     * @param {string} content - The HTML content to render
     */
    function renderSecureContent(container, content) {
        if (!container || !content) {
            return;
        }

        try {
            // Sanitize the content first
            const sanitizedContent = sanitizeContent(content);

            // Clear the container
            container.innerHTML = '';

            // Set the sanitized content
            container.innerHTML = sanitizedContent;

            // Remove any dangerous elements that might have slipped through
            const dangerousElements = container.querySelectorAll('script, iframe, object, embed');
            dangerousElements.forEach(el => el.remove());

        } catch (e) {
            console.error("Stashy: Error rendering secure content:", e);
            // Fallback to text content
            container.textContent = content;
        }
    }

    /**
     * Validates and sanitizes a URL for safe use
     * @param {string} url - The URL to validate
     * @returns {string|null} The sanitized URL or null if unsafe
     */
    function validateURL(url) {
        if (!url || typeof url !== 'string') {
            return null;
        }

        try {
            const parsedURL = new URL(url);

            // Only allow safe protocols
            if (['http:', 'https:', 'data:', 'file:'].includes(parsedURL.protocol)) {
                return parsedURL.href;
            }

            return null;
        } catch (e) {
            return null;
        }
    }

    // Return the public API
    return {
        buildCSPString,
        sanitizeContent,
        renderSecureContent,
        isURLSafe,
        validateURL
    };
})();

console.log("Stashy: Simplified Content Security Policy Module Loaded");
// --- END OF FILE content-csp.js ---
