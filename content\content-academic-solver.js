/**
 * Academic Problem Solver AI Module
 * Provides STEM problem-solving capabilities for Stashy extension
 * Handles math, physics, chemistry, and other academic subjects
 */

(function() {
    'use strict';

    // Academic Problem Solver Configuration
    const ACADEMIC_CONFIG = {
        // STEM subject detection patterns
        subjects: {
            mathematics: {
                keywords: ['equation', 'solve', 'calculate', 'derivative', 'integral', 'algebra', 'geometry', 'trigonometry', 'calculus', 'statistics', 'probability', 'matrix', 'vector', 'function', 'graph', 'theorem', 'proof', 'formula', 'polynomial', 'exponential', 'logarithm', 'fraction', 'radical', 'power', 'exponent'],
                patterns: [
                    // Basic arithmetic and equations
                    /\d+\s*[+\-*/=]\s*\d+/,
                    /[a-z]\s*[+\-*/=]/,

                    // Polynomial expressions (x², x³, 2x², etc.)
                    /[a-z]\s*[\^²³⁴⁵⁶⁷⁸⁹⁰]|\b[a-z]\d*\s*\^\s*\d+/,
                    /\b\d*[a-z]\s*[\^²³⁴⁵⁶⁷⁸⁹⁰]|\b\d*[a-z]\s*\^\s*\d+/,

                    // Exponential expressions (e^x, 2^n, 10^5, etc.)
                    /\b(e|E)\s*\^\s*[a-z\d\-+()]+/,
                    /\b\d+\s*\^\s*[a-z\d\-+()]+/,
                    /\b(exp|EXP)\s*\([^)]+\)/,

                    // Scientific notation (3.2 × 10^8, 1.5E+6, etc.)
                    /\d+\.?\d*\s*[×x*]\s*10\s*\^\s*[+\-]?\d+/,
                    /\d+\.?\d*\s*[eE]\s*[+\-]?\d+/,

                    // Fractions and complex expressions
                    /\d+\/\d+|\b\d+\s+\d+\/\d+/,
                    /\(\s*\d+\s*\/\s*\d+\s*\)/,

                    // Mathematical functions
                    /\b(sin|cos|tan|sec|csc|cot|sinh|cosh|tanh)\s*\([^)]+\)/,
                    /\b(log|ln|lg|sqrt|cbrt)\s*\([^)]+\)/,
                    /\b(arcsin|arccos|arctan|asin|acos|atan)\s*\([^)]+\)/,

                    // Calculus notation
                    /\b(dx|dy|dt|du|dv|∫|∑|∆|∂|lim|limit)/,
                    /\b(d\/dx|d\/dt|∂\/∂[a-z])/,

                    // Equations and expressions
                    /[a-z]\s*=\s*[^=]+$/m,
                    /=\s*0\b/,
                    /\b(solve|find|calculate|determine)\s+(for\s+)?[a-z]/i
                ]
            },
            physics: {
                keywords: ['force', 'velocity', 'acceleration', 'energy', 'momentum', 'mass', 'gravity', 'friction', 'wave', 'frequency', 'amplitude', 'electric', 'magnetic', 'current', 'voltage', 'resistance', 'power', 'work', 'heat', 'temperature', 'pressure', 'motion', 'kinematics', 'dynamics', 'thermodynamics', 'electromagnetism', 'optics', 'quantum', 'relativity'],
                patterns: [
                    // Classic physics formulas
                    /F\s*=\s*ma|F\s*=\s*m\s*\*\s*a/,
                    /E\s*=\s*mc²|E\s*=\s*m\s*\*\s*c\s*\^\s*2/,
                    /v\s*=\s*[^=]+/,
                    /a\s*=\s*[^=]+/,

                    // Kinematic equations
                    /v\s*=\s*v₀\s*\+\s*at|v\s*=\s*v0\s*\+\s*a\s*\*\s*t/,
                    /s\s*=\s*ut\s*\+\s*½at²|x\s*=\s*x₀\s*\+\s*v₀t\s*\+\s*½at²/,

                    // Energy and work
                    /KE\s*=\s*½mv²|E_k\s*=\s*½mv²/,
                    /PE\s*=\s*mgh|U\s*=\s*mgh/,
                    /W\s*=\s*Fd|W\s*=\s*F\s*\*\s*d/,

                    // Electrical formulas
                    /V\s*=\s*IR|V\s*=\s*I\s*\*\s*R/,
                    /P\s*=\s*VI|P\s*=\s*V\s*\*\s*I/,
                    /P\s*=\s*I²R|P\s*=\s*I\s*\^\s*2\s*\*\s*R/,

                    // Units (more comprehensive)
                    /\b(kg|g|m|cm|mm|km|s|min|h|m\/s|km\/h|m\/s²|N|J|kJ|W|kW|V|mV|A|mA|Ω|kΩ|Hz|kHz|MHz|°C|K|°F|Pa|kPa|MPa|atm|bar)\b/,

                    // Scientific notation in physics
                    /\d+\.?\d*\s*[×x*]\s*10\s*\^\s*[+\-]?\d+\s*(m|kg|s|N|J|W|V|A|Hz|Pa|°C|K)/,

                    // Physics constants
                    /\b(g\s*=\s*9\.8|c\s*=\s*3\.0?\s*[×x*]\s*10\s*\^\s*8|h\s*=\s*6\.63\s*[×x*]\s*10\s*\^\s*-34)/
                ]
            },
            chemistry: {
                keywords: ['molecule', 'atom', 'element', 'compound', 'reaction', 'bond', 'electron', 'proton', 'neutron', 'mole', 'molarity', 'pH', 'acid', 'base', 'oxidation', 'reduction', 'catalyst', 'equilibrium', 'stoichiometry', 'concentration', 'solution', 'titration', 'thermochemistry', 'kinetics', 'organic', 'inorganic'],
                patterns: [
                    // Chemical formulas (improved)
                    /\b[A-Z][a-z]?\d*(\([A-Z][a-z]?\d*\))?\d*/,
                    /\b(H₂O|CO₂|NaCl|CaCO₃|H₂SO₄|HCl|NaOH|CH₄|C₂H₆|C₆H₁₂O₆)/,

                    // Chemical equations and reactions
                    /[A-Z][a-z]?\d*\s*\+\s*[A-Z][a-z]?\d*\s*→\s*[A-Z][a-z]?\d*/,
                    /→|⇌|⇄|⟶|⟷/,

                    // Concentration and molarity
                    /\b(mol|M|mM|μM|molarity|molality|normality)\b/,
                    /\b\d+\.?\d*\s*M\b|\b\d+\.?\d*\s*mol\/L/,
                    /\b\d+\.?\d*\s*g\/mol\b/,

                    // pH and equilibrium
                    /\b(pH|pOH|pKa|pKb|Ka|Kb|Kw|Ksp|Kc|Kp)\b/,
                    /pH\s*=\s*-?log|\bpH\s*=\s*\d+\.?\d*/,
                    /\[H\+\]|\[OH\-\]|\[H₃O\+\]/,

                    // Chemical notation
                    /\[[A-Z][a-z]?\d*[\+\-]*\]/,
                    /\b(aq|s|l|g)\b/,

                    // Thermochemistry
                    /ΔH|ΔG|ΔS|Δ[A-Z]/,
                    /\b\d+\.?\d*\s*(kJ\/mol|kcal\/mol|J\/mol)/,

                    // Stoichiometry
                    /\b\d+\.?\d*\s*(g|kg|mg|L|mL|mol)\s+(of|to)\s+\d+\.?\d*\s*(g|kg|mg|L|mL|mol)/
                ]
            },
            engineering: {
                keywords: ['circuit', 'voltage', 'current', 'resistance', 'capacitor', 'inductor', 'transistor', 'amplifier', 'filter', 'signal', 'frequency', 'bandwidth', 'gain', 'impedance', 'power', 'efficiency', 'mechanical', 'structural', 'thermal', 'fluid', 'control', 'system', 'design', 'analysis'],
                patterns: [
                    // Electrical engineering
                    /V\s*=\s*IR|V\s*=\s*I\s*\*\s*R/,
                    /P\s*=\s*VI|P\s*=\s*V\s*\*\s*I/,
                    /P\s*=\s*I²R|P\s*=\s*V²\/R/,
                    /Z\s*=\s*R\s*\+\s*jX/,

                    // Mechanical engineering
                    /σ\s*=\s*F\/A|stress\s*=\s*force\/area/,
                    /ε\s*=\s*ΔL\/L|strain\s*=\s*change\/length/,
                    /τ\s*=\s*F\s*\*\s*r|torque\s*=\s*force\s*\*\s*radius/,

                    // Fluid mechanics
                    /Q\s*=\s*A\s*\*\s*v|flow\s*=\s*area\s*\*\s*velocity/,
                    /Re\s*=\s*ρvD\/μ|Reynolds\s*=\s*density\s*\*\s*velocity\s*\*\s*diameter\/viscosity/,

                    // Thermodynamics
                    /Q\s*=\s*mcΔT|heat\s*=\s*mass\s*\*\s*specific_heat\s*\*\s*temp_change/,
                    /η\s*=\s*W_out\/Q_in|efficiency\s*=\s*work_out\/heat_in/,

                    // Engineering units
                    /\b(V|mV|kV|A|mA|kA|Ω|kΩ|MΩ|F|μF|nF|pF|H|mH|μH|Hz|kHz|MHz|GHz|dB|W|kW|MW|Pa|kPa|MPa|GPa|N|kN|MN|m|mm|cm|km|kg|g|mg|s|ms|μs|ns)\b/,

                    // Engineering calculations
                    /\b(calculate|design|analyze|determine|find)\s+(the\s+)?(stress|strain|force|moment|power|voltage|current|resistance|frequency|efficiency)/i
                ]
            }
        },

        // Problem detection confidence thresholds
        detection: {
            minKeywordMatches: 2,
            minPatternMatches: 1,
            confidenceThreshold: 0.6
        },

        // AI generation settings
        ai: {
            maxTokens: 3500, // Increased by 2000 for better responses
            temperature: 0.2,
            stepByStepPrompt: true
        }
    };

    /**
     * Detects academic problems in content
     * @param {string} content - Content to analyze
     * @returns {Object} Detection results with confidence scores
     */
    function detectAcademicProblems(content) {
        try {
            if (!content || typeof content !== 'string') {
                console.warn('Academic Solver: Invalid content provided for detection');
                return {
                    hasProblems: false,
                    confidence: 0,
                    subjects: [],
                    problems: [],
                    totalScore: 0,
                    error: 'Invalid content provided'
                };
            }

            const results = {
                hasProblems: false,
                confidence: 0,
                subjects: [],
                problems: [],
                totalScore: 0
            };

            const contentLength = content.length;

            // Minimum content length check
            if (contentLength < 10) {
                return {
                    ...results,
                    error: 'Content too short for meaningful analysis'
                };
            }

            // Check each subject
            Object.entries(ACADEMIC_CONFIG.subjects).forEach(([subject, config]) => {
                let score = 0;
                let keywordMatches = 0;
                let patternMatches = 0;

                // Check keywords with better matching
                config.keywords.forEach(keyword => {
                    const regex = new RegExp(`\\b${keyword}\\b`, 'i');
                    if (regex.test(content)) {
                        keywordMatches++;
                        score += 1;
                    }
                });

                // Check patterns with error handling
                config.patterns.forEach(pattern => {
                    try {
                        if (pattern.test(content)) {
                            patternMatches++;
                            score += 2; // Patterns are weighted higher
                        }
                    } catch (patternError) {
                        console.warn('Academic Solver: Pattern test error:', patternError);
                    }
                });

                // Calculate subject confidence with improved algorithm
                const keywordWeight = Math.min(keywordMatches / Math.max(config.keywords.length, 1), 1.0);
                const patternWeight = Math.min(patternMatches / Math.max(config.patterns.length, 1), 1.0);
                const subjectConfidence = (keywordWeight * 0.4) + (patternWeight * 0.6);

                if (keywordMatches >= ACADEMIC_CONFIG.detection.minKeywordMatches ||
                    patternMatches >= ACADEMIC_CONFIG.detection.minPatternMatches) {
                    results.subjects.push({
                        name: subject,
                        confidence: subjectConfidence,
                        keywordMatches,
                        patternMatches,
                        score
                    });
                    results.totalScore += score;
                }
            });

            // Determine overall confidence with improved calculation
            const maxPossibleScore = Object.keys(ACADEMIC_CONFIG.subjects).length * 5; // Rough estimate
            results.confidence = Math.min(results.totalScore / Math.max(maxPossibleScore * 0.3, 1), 1.0);
            results.hasProblems = results.confidence >= ACADEMIC_CONFIG.detection.confidenceThreshold && results.subjects.length > 0;

            // Extract potential problem statements
            if (results.hasProblems || results.subjects.length > 0) {
                try {
                    results.problems = extractProblemStatements(content);
                } catch (extractError) {
                    console.warn('Academic Solver: Problem extraction error:', extractError);
                    results.problems = [];
                }
            }

            console.log('Academic Solver: Detection results:', {
                hasProblems: results.hasProblems,
                confidence: results.confidence,
                subjectCount: results.subjects.length,
                problemCount: results.problems.length
            });

            return results;
        } catch (error) {
            console.error('Academic Solver: Detection error:', error);
            return {
                hasProblems: false,
                confidence: 0,
                subjects: [],
                problems: [],
                totalScore: 0,
                error: error.message
            };
        }
    }

    /**
     * Extracts individual problem statements from content
     * @param {string} content - Content to analyze
     * @returns {Array} Array of problem statements
     */
    function extractProblemStatements(content) {
        const problems = [];
        const lines = content.split('\n');
        
        lines.forEach((line, index) => {
            const trimmedLine = line.trim();
            if (trimmedLine.length < 10) return;

            // Look for problem indicators
            const problemIndicators = [
                /^\d+[\.\)]/,  // Numbered problems
                /^[a-z][\.\)]/i,  // Lettered problems
                /solve|find|calculate|determine|prove|show|derive/i,
                /what is|how much|how many/i,
                /given.*find/i
            ];

            const hasIndicator = problemIndicators.some(pattern => pattern.test(trimmedLine));
            
            if (hasIndicator) {
                problems.push({
                    text: trimmedLine,
                    lineNumber: index + 1,
                    context: getContextLines(lines, index, 2)
                });
            }
        });

        return problems;
    }

    /**
     * Gets context lines around a specific line
     * @param {Array} lines - All lines
     * @param {number} targetIndex - Target line index
     * @param {number} contextSize - Number of context lines
     * @returns {string} Context text
     */
    function getContextLines(lines, targetIndex, contextSize) {
        const start = Math.max(0, targetIndex - contextSize);
        const end = Math.min(lines.length, targetIndex + contextSize + 1);
        return lines.slice(start, end).join('\n');
    }

    /**
     * Loads the AI Enhancement Utilities module for dynamic prompts
     * @returns {Promise<void>}
     */
    async function loadAiEnhancementUtilities() {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            if (window.AIEnhancementUtilities) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('lib/ai-enhancement-utilities.js');
            script.onload = () => {
                console.log('Academic Solver: AI Enhancement Utilities loaded successfully');
                resolve();
            };
            script.onerror = (error) => {
                console.error('Academic Solver: Failed to load AI Enhancement Utilities:', error);
                reject(error);
            };
            document.head.appendChild(script);
        });
    }

    /**
     * Solves academic problems with step-by-step explanations using Dynamic Prompts
     * @param {string} content - Selected problem content
     * @param {Object} options - Solving options
     * @returns {Promise<string>} Solution with steps
     */
    async function solveAcademicProblems(content, options = {}) {
        try {
            const { aiModule } = options;

            if (!aiModule) {
                throw new Error('AI module not available for problem solving');
            }

            if (!content || typeof content !== 'string') {
                throw new Error('No content selected. Please highlight the specific problem you want to solve.');
            }

            // Use the selected content directly
            const problemContent = content;

            if (problemContent.length < 5) {
                throw new Error('Selected text too short. Please select the complete problem statement you want to solve.');
            }

            console.log('Academic Solver: Initializing enhanced AI solving system...');

            // Load AI Enhancement Utilities if not already loaded
            await loadAiEnhancementUtilities();

            // Check if Dynamic Prompts processing is available
            if (window.AIEnhancementUtilities) {
                console.log('Academic Solver: Using Dynamic Prompts for subject-specific solving');

                // Create Dynamic Prompts processor
                const dynamicProcessor = window.AIEnhancementUtilities.createDynamicPromptsProcessor(aiModule);

                // Solve using dynamic prompts
                const result = await dynamicProcessor.solveWithDynamicPrompt(problemContent);

                if (!result.solution || result.solution.trim().length < 10) {
                    throw new Error('AI failed to generate a meaningful solution. Please try selecting a clearer problem statement.');
                }

                // Format the solution with subject information
                return formatEnhancedSolution(result.solution, result.subjectInfo, problemContent.length);

            } else {
                // Fallback to original implementation
                console.log('Academic Solver: Falling back to original implementation');
                return await solveAcademicProblemsFallback(problemContent, aiModule);
            }

        } catch (error) {
            console.error('Academic Solver: Problem solving error:', error);

            // Try fallback implementation
            try {
                console.log('Academic Solver: Attempting fallback solving...');
                return await solveAcademicProblemsFallback(content, options.aiModule);
            } catch (fallbackError) {
                console.error('Academic Solver: Fallback solving also failed:', fallbackError);
                throw new Error(`Problem solving failed: ${error.message}`);
            }
        }
    }

    /**
     * Fallback implementation of Academic Problem Solver (original version)
     * @param {string} problemContent - Problem content
     * @param {Object} aiModule - AI module
     * @returns {Promise<string>} Solution
     */
    async function solveAcademicProblemsFallback(problemContent, aiModule) {
        // Simple detection for basic subject classification
        let detection = detectAcademicProblems(problemContent);

        // Always proceed with solving, even if detection is uncertain
        if (!detection.hasProblems) {
            console.log('Academic Solver: Proceeding with general academic analysis of selected text');
            detection = {
                hasProblems: true,
                confidence: 0.7,
                subjects: [{ name: 'general academic', confidence: 0.7, keywordMatches: 0, patternMatches: 0, score: 1 }],
                problems: [{ text: problemContent.substring(0, 200), lineNumber: 1, context: problemContent }],
                totalScore: 1
            };
        }

        // Generate step-by-step solution
        const prompt = createSolvingPrompt(problemContent, detection);

        console.log('Academic Solver: Generating solution with AI...');
        const solution = await aiModule.generateText(prompt, {
            maxTokens: ACADEMIC_CONFIG.ai.maxTokens,
            temperature: ACADEMIC_CONFIG.ai.temperature
        });

        if (!solution || solution.trim().length < 10) {
            throw new Error('AI failed to generate a meaningful solution. Please try selecting a clearer problem statement.');
        }

        return formatSolution(solution, detection, problemContent.length);
    }

    /**
     * Creates a solving prompt for the AI
     * @param {string} content - Problem content
     * @param {Object} detection - Detection results
     * @returns {string} AI prompt
     */
    function createSolvingPrompt(content, detection) {
        try {
            const subjects = detection.subjects && detection.subjects.length > 0
                ? detection.subjects.map(s => s.name).join(', ')
                : 'mathematics, science, and engineering';

            return `You are an expert academic tutor and researcher with advanced degrees in ${subjects}. Provide a comprehensive, scientifically rigorous solution to the following problem.

CRITICAL REQUIREMENTS:
1. **Mathematical Rigor**: Show every mathematical step with proper notation. Use standard mathematical symbols (×, ÷, ², ³, √, ∫, ∂, etc.)
2. **Units & Dimensional Analysis**: Always include proper units and verify dimensional consistency throughout calculations
3. **Scientific Accuracy**: Use precise scientific terminology and notation. Cite relevant laws, principles, or theorems
4. **Step-by-Step Clarity**: Number each step clearly and explain the reasoning behind every operation
5. **Verification**: Check your final answer for reasonableness and dimensional correctness
6. **Professional Presentation**: Format as you would for a peer-reviewed academic publication

FORMATTING GUIDELINES:
- Use proper mathematical notation (e.g., x² not x^2, H₂O not H2O)
- Include units in every calculation step
- Highlight final answers with clear formatting
- Show intermediate results to maintain clarity
- Use scientific notation for very large or small numbers (e.g., 3.0 × 10⁸ m/s)
- Structure your response with clear numbered sections and subsections
- Use proper line breaks between different steps and concepts
- Ensure each mathematical operation is on its own line for clarity

RESPONSE STRUCTURE:
Please structure your response as follows:
1. Problem Statement: [Restate the problem clearly]
2. Given Information: [List all given values and conditions]
3. Solution Approach: [Outline your methodology]
4. Step-by-Step Solution: [Detailed calculations with explanations]
5. Final Answer: [Clear statement of the result with units]
6. Verification: [Check the reasonableness of your answer]

PROBLEM TO SOLVE:
${content}

COMPREHENSIVE SOLUTION:`;
        } catch (error) {
            console.warn('Academic Solver: Error creating prompt, using fallback:', error);
            return `You are an expert academic tutor. Solve the following problem with detailed step-by-step explanations.

REQUIREMENTS:
1. Show all mathematical work with proper notation
2. Include units in all calculations
3. Explain each step clearly
4. Verify your final answer
5. Use scientific terminology appropriately

PROBLEM:
${content}

SOLUTION:`;
        }
    }

    /**
     * Formats the enhanced AI solution with subject information and styling
     * @param {string} solution - Raw AI solution
     * @param {Object} subjectInfo - Subject detection information
     * @param {number} contentLength - Original content length
     * @returns {string} Formatted HTML solution
     */
    function formatEnhancedSolution(solution, subjectInfo, contentLength) {
        const { subject, confidence } = subjectInfo;
        const subjectDisplay = subject.charAt(0).toUpperCase() + subject.slice(1);
        const confidencePercent = Math.round(confidence * 100);

        return `
        <div class="academic-solver-result enhanced">
            <div class="result-header">
                <div class="header-title">
                    <span class="icon">🎓</span>
                    <span class="title">Academic Problem Solution (Enhanced)</span>
                </div>
                <div class="subject-info">
                    <span class="subject-badge" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500;">
                        ${subjectDisplay} • ${confidencePercent}% confidence
                    </span>
                </div>
            </div>

            <div class="solution-content">
                ${formatSolutionSteps(solution)}
            </div>

            <div class="result-footer">
                <div class="stats">
                    <span>📝 Analyzed ${contentLength} characters of selected text</span>
                    <span>🧠 Subject-specific AI processing</span>
                    <span>🎯 Dynamic prompt optimization</span>
                </div>
            </div>
        </div>

        <style>
        .academic-solver-result.enhanced {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            border: 2px solid #667eea;
            border-radius: 12px;
            padding: 20px;
            margin: 16px 0;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .academic-solver-result.enhanced .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 2px solid #e8f4fd;
        }

        .academic-solver-result.enhanced .header-title {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .academic-solver-result.enhanced .icon {
            font-size: 20px;
        }

        .academic-solver-result.enhanced .title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .academic-solver-result.enhanced .subject-info {
            display: flex;
            align-items: center;
        }

        .academic-solver-result.enhanced .solution-content {
            line-height: 1.7;
            color: #2c3e50;
            font-size: 15px;
        }

        .academic-solver-result.enhanced .result-footer {
            margin-top: 16px;
            padding-top: 12px;
            border-top: 1px solid #e8f4fd;
        }

        .academic-solver-result.enhanced .stats {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            font-size: 13px;
            color: #6c757d;
        }

        .academic-solver-result.enhanced .stats span {
            background: rgba(102, 126, 234, 0.1);
            padding: 4px 8px;
            border-radius: 8px;
            font-weight: 500;
        }
        </style>`;
    }

    /**
     * Formats the AI solution with enhanced styling (fallback version)
     * @param {string} solution - Raw AI solution
     * @param {Object} detection - Detection results (unused, kept for compatibility)
     * @param {number} contentLength - Original content length
     * @returns {string} Formatted HTML solution
     */
    function formatSolution(solution, _detection, contentLength) {
        return `
        <div class="academic-solver-result">
            <div class="result-header">
                <div class="header-title">
                    <span class="icon">🎓</span>
                    <span class="title">Academic Problem Solution</span>
                </div>
            </div>

            <div class="solution-content">
                ${formatSolutionSteps(solution)}
            </div>

            <div class="result-footer">
                <div class="stats">
                    <span>📝 Analyzed ${contentLength} characters of selected text</span>
                    <span>🎯 Focused solution provided</span>
                </div>
            </div>
        </div>

        <style>
        .academic-solver-result {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
            border: 1px solid #d1e7dd;
            border-radius: 12px;
            padding: 20px;
            margin: 12px 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Segoe UI Math', 'Times New Roman', serif;
            box-shadow: 0 2px 8px rgba(0, 102, 204, 0.1);
        }

        .result-header {
            border-bottom: 2px solid #0066cc;
            padding-bottom: 12px;
            margin-bottom: 20px;
        }

        .header-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            font-weight: 600;
            color: #0066cc;
        }

        .solution-content {
            line-height: 1.7;
            color: #2c3e50;
            font-size: 15px;
        }

        .solution-step {
            margin: 16px 0;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.8);
            border-left: 4px solid #0066cc;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            font-family: 'Segoe UI Math', 'Times New Roman', serif;
        }

        .solution-header {
            margin: 20px 0 12px 0;
            padding: 8px 12px;
            background: linear-gradient(135deg, #0066cc, #004499);
            color: white;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
        }

        .formula-block {
            margin: 12px 0;
            padding: 10px 16px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            font-family: 'Segoe UI Math', 'Times New Roman', serif;
            font-size: 16px;
            text-align: center;
            color: #2c3e50;
        }

        .solution-step.has-formula {
            background: rgba(248, 249, 250, 0.9);
            border-left-color: #28a745;
        }

        .step-title {
            font-weight: 600;
            color: #0066cc;
            margin-bottom: 8px;
            font-size: 15px;
        }

        .sub-step {
            margin: 8px 0;
            padding-left: 16px;
            border-left: 2px solid #6c757d;
            color: #495057;
        }

        .solution-text {
            margin: 6px 0;
            line-height: 1.6;
            color: #2c3e50;
        }

        .result-footer {
            margin-top: 20px;
            padding-top: 12px;
            border-top: 1px solid #e0e6ed;
        }

        .stats {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            font-size: 12px;
            color: #7f8c8d;
            font-style: italic;
        }

        /* Enhanced mathematical notation support */
        .solution-content sup {
            font-size: 0.8em;
            vertical-align: super;
        }

        .solution-content sub {
            font-size: 0.8em;
            vertical-align: sub;
        }

        .solution-content code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            color: #e83e8c;
        }
        </style>`;
    }

    /**
     * Formats solution steps with enhanced styling and mathematical notation support
     * @param {string} solution - Raw solution text
     * @returns {string} Formatted solution HTML with enhanced mathematical notation
     */
    function formatSolutionSteps(solution) {
        if (!solution || typeof solution !== 'string') {
            return '<p>No solution provided</p>';
        }

        // Clean and format the solution text
        let formattedSolution = solution
            // Superscripts for common exponents (be more careful with replacements)
            .replace(/\^2(?!\d)/g, '²')
            .replace(/\^3(?!\d)/g, '³')
            .replace(/\^4(?!\d)/g, '⁴')
            .replace(/\^5(?!\d)/g, '⁵')
            .replace(/\^6(?!\d)/g, '⁶')
            .replace(/\^7(?!\d)/g, '⁷')
            .replace(/\^8(?!\d)/g, '⁸')
            .replace(/\^9(?!\d)/g, '⁹')
            .replace(/\^0(?!\d)/g, '⁰')
            .replace(/\^1(?!\d)/g, '¹')
            .replace(/\^-1(?!\d)/g, '⁻¹')
            .replace(/\^-2(?!\d)/g, '⁻²')
            .replace(/\^-3(?!\d)/g, '⁻³')

            // Improve mathematical symbols (keep × symbol, it's important for multiplication)
            .replace(/\s\*\s/g, ' × ')
            .replace(/\+\/-/g, '±')
            .replace(/-\+/g, '∓')
            .replace(/sqrt\(/g, '√(')

            // Chemical formulas (more specific)
            .replace(/\bH2O\b/g, 'H₂O')
            .replace(/\bCO2\b/g, 'CO₂')
            .replace(/\bH2SO4\b/g, 'H₂SO₄')
            .replace(/\bCaCO3\b/g, 'CaCO₃')
            .replace(/\bCH4\b/g, 'CH₄')
            .replace(/\bC2H6\b/g, 'C₂H₆')
            .replace(/\bC6H12O6\b/g, 'C₆H₁₂O₆')

            // Units formatting (more specific)
            .replace(/\bm\/s2\b/g, 'm/s²')
            .replace(/\bm\/s\^2\b/g, 'm/s²')

            // Scientific notation (more careful)
            .replace(/(\d+\.?\d*)\s*[xX]\s*10\^([+-]?\d+)/g, '$1 × 10^$2')
            .replace(/(\d+\.?\d*)[eE]([+-]?\d+)/g, '$1 × 10^$2');

        // Enhanced step formatting with better structure
        const lines = formattedSolution.split('\n');
        let formattedHTML = '';
        let currentSection = '';

        lines.forEach(line => {
            const trimmedLine = line.trim();
            if (!trimmedLine) {
                if (currentSection) {
                    formattedHTML += '<br>';
                }
                return;
            }

            // Enhanced step detection with better patterns
            const isMainHeader = /^(comprehensive\s+solution|solution|problem\s+statement|methodology|conclusion):/i.test(trimmedLine);
            const isStepHeader = /^(\d+\.\s+|step\s+\d+:|answer:|final\s+answer:)/i.test(trimmedLine);
            const isSubStep = /^(step\s+\d+:|substep|part\s+[a-z])/i.test(trimmedLine);
            const isFormula = /[=<>≤≥≠±∓÷√∫∂∑∆]/.test(trimmedLine) && !/^(step|solution|answer|final|conclusion)/i.test(trimmedLine);

            if (isMainHeader) {
                if (currentSection) formattedHTML += '</div>';
                formattedHTML += `<div class="solution-header">${trimmedLine}</div>`;
                currentSection = 'header';
            } else if (isStepHeader) {
                if (currentSection && currentSection !== 'step') formattedHTML += '</div>';
                if (currentSection !== 'step') formattedHTML += '<div class="solution-step">';
                formattedHTML += `<div class="step-title">${trimmedLine}</div>`;
                currentSection = 'step';
            } else if (isSubStep) {
                if (currentSection !== 'step') {
                    if (currentSection) formattedHTML += '</div>';
                    formattedHTML += '<div class="solution-step">';
                }
                formattedHTML += `<div class="sub-step">${trimmedLine}</div>`;
                currentSection = 'step';
            } else if (isFormula) {
                if (currentSection !== 'step') {
                    if (currentSection) formattedHTML += '</div>';
                    formattedHTML += '<div class="solution-step">';
                    currentSection = 'step';
                }
                formattedHTML += `<div class="formula-block">${trimmedLine}</div>`;
            } else {
                // Regular content
                if (currentSection !== 'step') {
                    if (currentSection) formattedHTML += '</div>';
                    formattedHTML += '<div class="solution-step">';
                    currentSection = 'step';
                }
                formattedHTML += `<div class="solution-text">${trimmedLine}</div>`;
            }
        });

        if (currentSection) formattedHTML += '</div>';

        return formattedHTML || `<div class="solution-step">${formattedSolution.replace(/\n/g, '<br>')}</div>`;
    }

    // Export functions immediately for use by main AI features module
    window.AcademicSolver = {
        detectAcademicProblems,
        solveAcademicProblems,
        formatEnhancedSolution,
        formatSolution,
        formatSolutionSteps,
        ACADEMIC_CONFIG,

        // Simple ready check
        isReady: function() {
            return true; // Always ready since functions are defined
        },

        // Get module status
        getStatus: function() {
            return {
                initialized: true,
                ready: true,
                configValid: !!(ACADEMIC_CONFIG && ACADEMIC_CONFIG.subjects),
                subjectCount: ACADEMIC_CONFIG ? Object.keys(ACADEMIC_CONFIG.subjects).length : 0
            };
        }
    };

    console.log('Academic Solver: Module loaded and ready');

})();
