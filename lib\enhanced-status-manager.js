/**
 * Stashy - Enhanced Status Manager
 * Provides comprehensive user feedback and error handling
 */

// Make sure we don't redefine functions if the script is injected multiple times
if (typeof window.SB_ENHANCED_STATUS_MANAGER_LOADED === 'undefined') {
    window.SB_ENHANCED_STATUS_MANAGER_LOADED = true;

    console.log("Stashy: Loading Enhanced Status Manager...");

    // Status configuration
    const statusConfig = {
        duration: {
            success: 3000,
            info: 4000,
            warning: 5000,
            error: 6000
        },
        maxVisible: 3,
        position: 'top-right'
    };

    // Status queue for managing multiple messages
    let statusQueue = [];
    let activeStatuses = [];

    /**
     * Enhanced status display with better UX
     * @param {string} message - The message to display
     * @param {string} type - The type of status (success, info, warning, error)
     * @param {Object} options - Additional options
     */
    window.showEnhancedStatus = function(message, type = 'info', options = {}) {
        const statusItem = {
            id: Date.now() + Math.random(),
            message: message,
            type: type,
            timestamp: Date.now(),
            duration: options.duration || statusConfig.duration[type] || statusConfig.duration.info,
            persistent: options.persistent || false,
            actionable: options.actionable || false,
            action: options.action || null
        };

        // Add to queue
        statusQueue.push(statusItem);
        processStatusQueue();
    };

    /**
     * Process the status queue
     */
    function processStatusQueue() {
        // Remove expired statuses
        activeStatuses = activeStatuses.filter(status => {
            if (!status.persistent && Date.now() - status.timestamp > status.duration) {
                removeStatusElement(status.id);
                return false;
            }
            return true;
        });

        // Add new statuses if there's room
        while (statusQueue.length > 0 && activeStatuses.length < statusConfig.maxVisible) {
            const status = statusQueue.shift();
            activeStatuses.push(status);
            displayStatusElement(status);
        }
    }

    /**
     * Display a status element
     * @param {Object} status - The status object
     */
    function displayStatusElement(status) {
        // Create status container if it doesn't exist
        let container = document.getElementById('Stashy-status-container');
        if (!container) {
            container = createStatusContainer();
        }

        // Create status element
        const statusEl = document.createElement('div');
        statusEl.id = `Stashy-status-${status.id}`;
        statusEl.className = `Stashy-status Stashy-status-${status.type}`;
        
        // Create status content
        const content = document.createElement('div');
        content.className = 'Stashy-status-content';
        
        // Add icon based on type
        const icon = document.createElement('span');
        icon.className = 'Stashy-status-icon';
        icon.innerHTML = getStatusIcon(status.type);
        
        // Add message
        const messageEl = document.createElement('span');
        messageEl.className = 'Stashy-status-message';
        messageEl.textContent = status.message;
        
        content.appendChild(icon);
        content.appendChild(messageEl);
        
        // Add action button if actionable
        if (status.actionable && status.action) {
            const actionBtn = document.createElement('button');
            actionBtn.className = 'Stashy-status-action';
            actionBtn.textContent = status.action.text || 'Action';
            actionBtn.onclick = () => {
                if (typeof status.action.callback === 'function') {
                    status.action.callback();
                }
                removeStatusElement(status.id);
            };
            content.appendChild(actionBtn);
        }
        
        // Add close button for persistent messages
        if (status.persistent || status.type === 'error') {
            const closeBtn = document.createElement('button');
            closeBtn.className = 'Stashy-status-close';
            closeBtn.innerHTML = '×';
            closeBtn.onclick = () => removeStatusElement(status.id);
            content.appendChild(closeBtn);
        }
        
        statusEl.appendChild(content);
        container.appendChild(statusEl);
        
        // Animate in
        requestAnimationFrame(() => {
            statusEl.classList.add('Stashy-status-visible');
        });
        
        // Auto-remove if not persistent
        if (!status.persistent) {
            setTimeout(() => {
                removeStatusElement(status.id);
            }, status.duration);
        }
    }

    /**
     * Remove a status element
     * @param {string} statusId - The status ID
     */
    function removeStatusElement(statusId) {
        const statusEl = document.getElementById(`Stashy-status-${statusId}`);
        if (statusEl) {
            statusEl.classList.add('Stashy-status-removing');
            setTimeout(() => {
                if (statusEl.parentNode) {
                    statusEl.parentNode.removeChild(statusEl);
                }
            }, 300);
        }
        
        // Remove from active statuses
        activeStatuses = activeStatuses.filter(s => s.id !== statusId);
        
        // Process queue for next status
        setTimeout(processStatusQueue, 100);
    }

    /**
     * Create the status container
     */
    function createStatusContainer() {
        const container = document.createElement('div');
        container.id = 'Stashy-status-container';
        container.className = 'Stashy-status-container';
        
        // Add styles
        const styles = `
            .Stashy-status-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 999999;
                pointer-events: none;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            
            .Stashy-status {
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                margin-bottom: 8px;
                max-width: 400px;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                pointer-events: auto;
                border-left: 4px solid #ccc;
            }
            
            .Stashy-status-visible {
                opacity: 1;
                transform: translateX(0);
            }
            
            .Stashy-status-removing {
                opacity: 0;
                transform: translateX(100%);
            }
            
            .Stashy-status-success { border-left-color: #4caf50; }
            .Stashy-status-info { border-left-color: #2196f3; }
            .Stashy-status-warning { border-left-color: #ff9800; }
            .Stashy-status-error { border-left-color: #f44336; }
            
            .Stashy-status-content {
                display: flex;
                align-items: center;
                padding: 12px 16px;
                gap: 8px;
            }
            
            .Stashy-status-icon {
                font-size: 16px;
                flex-shrink: 0;
            }
            
            .Stashy-status-message {
                flex: 1;
                font-size: 14px;
                line-height: 1.4;
                color: #333;
            }
            
            .Stashy-status-action, .Stashy-status-close {
                background: none;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 12px;
                cursor: pointer;
                color: #666;
            }
            
            .Stashy-status-action:hover, .Stashy-status-close:hover {
                background: #f5f5f5;
            }
            
            .Stashy-status-close {
                font-size: 16px;
                font-weight: bold;
                padding: 2px 6px;
            }
        `;
        
        // Add styles to document
        let styleEl = document.getElementById('Stashy-status-styles');
        if (!styleEl) {
            styleEl = document.createElement('style');
            styleEl.id = 'Stashy-status-styles';
            styleEl.textContent = styles;
            document.head.appendChild(styleEl);
        }
        
        document.body.appendChild(container);
        return container;
    }

    /**
     * Get icon for status type
     * @param {string} type - The status type
     */
    function getStatusIcon(type) {
        const icons = {
            success: '✓',
            info: 'ℹ',
            warning: '⚠',
            error: '✕'
        };
        return icons[type] || icons.info;
    }

    /**
     * Clear all status messages
     */
    window.clearAllStatuses = function() {
        statusQueue = [];
        activeStatuses.forEach(status => removeStatusElement(status.id));
        activeStatuses = [];
    };

    /**
     * Backward compatibility with existing showStatus function
     */
    if (typeof window.showStatus !== 'function') {
        window.showStatus = window.showEnhancedStatus;
    }

    console.log("Stashy: Enhanced Status Manager Loaded");
}
