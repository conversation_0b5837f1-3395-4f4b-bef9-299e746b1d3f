/* Modern CSS Variables for Consistent Theming */
:root {
    --primary-color: #2563EB;
    --primary-light: #3B82F6;
    --primary-bg: #DBEAFE;
    --secondary-color: #7C3AED;
    --secondary-light: #8B5CF6;
    --secondary-bg: #EDE9FE;
    --accent-color: #059669;
    --accent-light: #10B981;
    --accent-bg: #D1FAE5;
    --text-primary: #1F2937;
    --text-secondary: #6B7280;
    --text-muted: #9CA3AF;
    --bg-primary: #FFFFFF;
    --bg-secondary: #F9FAFB;
    --bg-tertiary: #F3F4F6;
    --border-light: #E5E7EB;
    --border-medium: #D1D5DB;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
}

/* Enhanced Typography and Layout */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.7;
    max-width: 900px;
    margin: 0 auto;
    padding: 24px;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    font-size: 16px;
}

/* Enhanced Header Styling */
h1 {
    color: var(--primary-color);
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 16px;
    margin-bottom: 32px;
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    color: var(--primary-color);
    margin-top: 48px;
    margin-bottom: 24px;
    border-bottom: 2px solid var(--primary-bg);
    padding-bottom: 12px;
    font-size: 1.875rem;
    font-weight: 600;
    letter-spacing: -0.025em;
    position: relative;
}

h2::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

h3 {
    color: var(--text-primary);
    margin-top: 32px;
    margin-bottom: 16px;
    font-size: 1.5rem;
    font-weight: 600;
}

/* Enhanced Typography */
p {
    margin-bottom: 16px;
    line-height: 1.7;
    color: var(--text-secondary);
}

/* Enhanced Lists */
ul {
    padding-left: 24px;
    margin: 16px 0 24px 0;
}

li {
    margin: 12px 0;
    line-height: 1.6;
    color: var(--text-secondary);
}

li strong {
    color: var(--text-primary);
    font-weight: 600;
}

strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* Enhanced Special Sections */
.contact-info {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: 24px;
    margin: 32px 0;
    box-shadow: var(--shadow-sm);
    border-left: 4px solid var(--accent-color);
}

.contact-info h3 {
    color: var(--accent-color);
    margin-top: 0;
    margin-bottom: 16px;
}

.effective-date {
    font-style: italic;
    color: var(--text-muted);
    margin-bottom: 24px;
    padding: 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--secondary-color);
    font-size: 0.875rem;
}

.last-updated {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    margin-top: 48px;
    padding-top: 24px;
    border-top: 2px solid var(--border-light);
    font-size: 0.875rem;
}

/* Enhanced Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--primary-light);
    text-decoration: underline;
}

/* Enhanced Code */
code {
    background-color: var(--bg-tertiary);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.875em;
    color: var(--text-primary);
    border: 1px solid var(--border-light);
}

/* Privacy-specific styles */
.privacy-section {
    background: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: 24px;
    margin: 24px 0;
    border-left: 4px solid var(--primary-color);
}

.privacy-highlight {
    background: linear-gradient(135deg, var(--accent-bg) 0%, #F0FDF4 100%);
    border: 1px solid var(--accent-light);
    border-radius: var(--radius-md);
    padding: 20px;
    margin: 24px 0;
    border-left: 4px solid var(--accent-color);
    position: relative;
}

.privacy-highlight::before {
    content: '🔒';
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 20px;
}

.privacy-highlight strong {
    color: var(--accent-color);
    margin-left: 32px;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 16px;
        font-size: 14px;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .contact-info,
    .privacy-section,
    .privacy-highlight {
        padding: 16px;
    }
}
