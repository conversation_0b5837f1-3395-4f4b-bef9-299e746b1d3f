/**
 * Favicon Utilities for Stashy Dashboard
 * Handles favicon fetching, caching, and display for notes and highlights
 */

// Favicon cache to prevent repeated requests
const faviconCache = new Map();

// Default fallback icon (data URL for a simple globe icon)
const DEFAULT_FAVICON = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZT0iIzY2NiIgc3Ryb2tlLXdpZHRoPSIxLjUiLz4KPHBhdGggZD0iTTggMUMxMC41IDMgMTEuNSA1IDExLjUgOEMxMS41IDExIDEwLjUgMTMgOCAxNUM1LjUgMTMgNC41IDExIDQuNSA4QzQuNSA1IDUuNSAzIDggMVoiIHN0cm9rZT0iIzY2NiIgc3Ryb2tlLXdpZHRoPSIxLjUiLz4KPHBhdGggZD0iTTEgOEgxNSIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjEuNSIvPgo8L3N2Zz4K';

// Favicon service URLs for fallback
const FAVICON_SERVICES = [
    'https://www.google.com/s2/favicons?domain=',
    'https://icons.duckduckgo.com/ip3/',
    'https://favicons.githubusercontent.com/'
];

/**
 * Extracts domain from URL for favicon fetching
 * @param {string} url - The URL to extract domain from
 * @returns {string|null} The domain or null if invalid
 */
function extractDomainForFavicon(url) {
    if (!url || typeof url !== 'string') {
        return null;
    }

    // Handle special cases
    if (url.startsWith('snippet_') || url === '[No URL]' || url === '[Missing URL]') {
        return null;
    }

    try {
        // Ensure URL has protocol
        let processedUrl = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            processedUrl = 'https://' + url;
        }

        const urlObj = new URL(processedUrl);
        return urlObj.hostname.replace(/^www\./, '');
    } catch (error) {
        console.warn('Stashy Favicon: Error extracting domain from URL:', url, error);
        return null;
    }
}

/**
 * Generates favicon URL using various strategies
 * @param {string} domain - The domain to get favicon for
 * @returns {string[]} Array of potential favicon URLs to try
 */
function generateFaviconUrls(domain) {
    if (!domain) {
        return [];
    }

    const urls = [];
    
    // Try direct favicon.ico from domain
    urls.push(`https://${domain}/favicon.ico`);
    
    // Try favicon services
    FAVICON_SERVICES.forEach(service => {
        urls.push(service + domain);
    });

    return urls;
}

/**
 * Tests if a favicon URL is accessible
 * @param {string} url - The favicon URL to test
 * @returns {Promise<boolean>} Promise that resolves to true if accessible
 */
function testFaviconUrl(url) {
    return new Promise((resolve) => {
        const img = new Image();
        const timeout = setTimeout(() => {
            img.onload = null;
            img.onerror = null;
            resolve(false);
        }, 3000); // 3 second timeout

        img.onload = () => {
            clearTimeout(timeout);
            // Check if image has valid dimensions
            if (img.width > 0 && img.height > 0) {
                resolve(true);
            } else {
                resolve(false);
            }
        };

        img.onerror = () => {
            clearTimeout(timeout);
            resolve(false);
        };

        img.src = url;
    });
}

/**
 * Fetches favicon for a given URL with caching
 * @param {string} url - The URL to get favicon for
 * @returns {Promise<string>} Promise that resolves to favicon URL or default
 */
async function getFaviconUrl(url) {
    const domain = extractDomainForFavicon(url);
    
    if (!domain) {
        return DEFAULT_FAVICON;
    }

    // Check cache first
    if (faviconCache.has(domain)) {
        return faviconCache.get(domain);
    }

    // Generate potential favicon URLs
    const faviconUrls = generateFaviconUrls(domain);
    
    // Try each URL until one works
    for (const faviconUrl of faviconUrls) {
        try {
            const isAccessible = await testFaviconUrl(faviconUrl);
            if (isAccessible) {
                // Cache the successful URL
                faviconCache.set(domain, faviconUrl);
                return faviconUrl;
            }
        } catch (error) {
            console.warn('Stashy Favicon: Error testing favicon URL:', faviconUrl, error);
        }
    }

    // If no favicon found, cache and return default
    faviconCache.set(domain, DEFAULT_FAVICON);
    return DEFAULT_FAVICON;
}

/**
 * Creates a favicon image element
 * @param {string} faviconUrl - The favicon URL
 * @param {string} domain - The domain name for alt text
 * @returns {HTMLImageElement} The favicon image element
 */
function createFaviconElement(faviconUrl, domain) {
    const img = document.createElement('img');
    img.src = faviconUrl;
    img.alt = domain ? `${domain} favicon` : 'Website favicon';
    img.className = 'stashy-favicon';
    img.loading = 'lazy'; // Lazy load for performance
    
    // Handle load errors by falling back to default
    img.onerror = () => {
        if (img.src !== DEFAULT_FAVICON) {
            img.src = DEFAULT_FAVICON;
        }
    };

    return img;
}

/**
 * Gets favicon element for a URL (async)
 * @param {string} url - The URL to get favicon for
 * @returns {Promise<HTMLImageElement>} Promise that resolves to favicon element
 */
async function getFaviconElement(url) {
    const faviconUrl = await getFaviconUrl(url);
    const domain = extractDomainForFavicon(url);
    return createFaviconElement(faviconUrl, domain);
}

/**
 * Gets favicon element synchronously using cached data or default
 * @param {string} url - The URL to get favicon for
 * @returns {HTMLImageElement} Favicon element (may be default initially)
 */
function getFaviconElementSync(url) {
    const domain = extractDomainForFavicon(url);
    
    if (!domain) {
        return createFaviconElement(DEFAULT_FAVICON, null);
    }

    // Check if we have cached favicon
    const cachedFavicon = faviconCache.get(domain);
    if (cachedFavicon) {
        return createFaviconElement(cachedFavicon, domain);
    }

    // Create element with default, then update asynchronously
    const img = createFaviconElement(DEFAULT_FAVICON, domain);
    
    // Asynchronously fetch and update
    getFaviconUrl(url).then(faviconUrl => {
        if (img.src !== faviconUrl) {
            img.src = faviconUrl;
        }
    }).catch(error => {
        console.warn('Stashy Favicon: Error updating favicon:', error);
    });

    return img;
}

/**
 * Preloads favicons for multiple URLs to improve performance
 * @param {string[]} urls - Array of URLs to preload favicons for
 */
async function preloadFavicons(urls) {
    const uniqueDomains = new Set();
    
    // Extract unique domains
    urls.forEach(url => {
        const domain = extractDomainForFavicon(url);
        if (domain && !faviconCache.has(domain)) {
            uniqueDomains.add(domain);
        }
    });

    // Preload favicons for unique domains
    const preloadPromises = Array.from(uniqueDomains).map(domain => {
        return getFaviconUrl(`https://${domain}`);
    });

    try {
        await Promise.allSettled(preloadPromises);
        console.log(`Stashy Favicon: Preloaded favicons for ${uniqueDomains.size} domains`);
    } catch (error) {
        console.warn('Stashy Favicon: Error during favicon preloading:', error);
    }
}

/**
 * Clears the favicon cache
 */
function clearFaviconCache() {
    faviconCache.clear();
    console.log('Stashy Favicon: Cache cleared');
}

/**
 * Gets cache statistics
 * @returns {object} Cache statistics
 */
function getFaviconCacheStats() {
    return {
        size: faviconCache.size,
        domains: Array.from(faviconCache.keys())
    };
}

// Export the favicon utilities
window.StashyFaviconUtils = {
    getFaviconUrl,
    getFaviconElement,
    getFaviconElementSync,
    preloadFavicons,
    clearFaviconCache,
    getFaviconCacheStats,
    extractDomainForFavicon,
    DEFAULT_FAVICON
};

console.log('Stashy Favicon Utils: Module loaded successfully');
