/**
 * Stashy Screenshot Settings Styles
 * External CSS file for screenshot-settings.html
 * Extracted from inline styles for better code organization and CSP compliance
 */

/* Base Styles */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
    color: #333;
}

/* Container */
.container {
    max-width: 600px;
    margin: 0 auto;
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Typography */
h1 {
    color: #4285f4;
    margin-top: 0;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

select, 
input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

/* Checkbox Styles */
.checkbox-group {
    margin-top: 10px;
}

.checkbox-label {
    font-weight: normal;
    display: flex;
    align-items: center;
}

.checkbox-label input {
    width: auto;
    margin-right: 10px;
}

/* Info Text */
.info-text {
    font-size: 0.9em;
    color: #666;
    margin-top: 5px;
}

/* Button Styles */
.buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

button.primary {
    background-color: #4285f4;
    color: white;
}

button.primary:hover {
    background-color: #3367d6;
}

button.secondary {
    background-color: #f1f1f1;
    color: #333;
}

button.secondary:hover {
    background-color: #e8e8e8;
}

/* Utility Classes */
.hidden {
    display: none;
}
