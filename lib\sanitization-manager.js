/**
 * Improved Sanitization Manager
 * Manages HTML sanitization with better error handling and performance
 */

// Create a namespace to avoid global pollution
window.StashySanitizationManager = (function() {

    /**
     * Sanitizes HTML content using the best available method
     * @param {string} html - The HTML content to sanitize
     * @returns {string} The sanitized HTML
     */
    function sanitize(html) {
        // If HTML is empty or not a string, return empty string
        if (!html || typeof html !== 'string') {
            return '';
        }

        try {
            // Use DOMPurify if available (preferred method)
            if (window.DOMPurify && typeof window.DOMPurify.sanitize === 'function') {
                return window.DOMPurify.sanitize(html, {
                    ALLOWED_TAGS: [
                        'p', 'div', 'span', 'br', 'hr', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                        'b', 'strong', 'i', 'em', 'u', 'mark', 'ul', 'ol', 'li',
                        'table', 'thead', 'tbody', 'tr', 'th', 'td', 'blockquote', 'pre', 'code', 'a', 'img'
                    ],
                    ALLOWED_ATTR: ['class', 'id', 'style', 'href', 'src', 'alt', 'title', 'data-latex'],
                    FORBID_TAGS: ['script', 'iframe', 'object', 'embed', 'form', 'input'],
                    FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover'],
                    SANITIZE_DOM: true,
                    KEEP_CONTENT: true
                });
            }

            // Use StashyDOMPurify if available
            if (window.StashyDOMPurify && typeof window.StashyDOMPurify.sanitize === 'function') {
                return window.StashyDOMPurify.sanitize(html);
            }

            // Additional fallback using DOM-based sanitization
            if (typeof document !== 'undefined') {
                try {
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = html;

                    // Remove dangerous elements
                    const dangerousElements = tempDiv.querySelectorAll('script, iframe, object, embed, form, input, textarea');
                    dangerousElements.forEach(el => el.remove());

                    // Remove dangerous attributes
                    const allElements = tempDiv.querySelectorAll('*');
                    allElements.forEach(el => {
                        // Remove event handlers
                        Array.from(el.attributes).forEach(attr => {
                            if (attr.name.startsWith('on') ||
                                (attr.name === 'href' && attr.value.toLowerCase().includes('javascript:')) ||
                                (attr.name === 'src' && attr.value.toLowerCase().includes('javascript:'))) {
                                el.removeAttribute(attr.name);
                            }
                        });
                    });

                    return tempDiv.innerHTML;
                } catch (e) {
                    console.warn('Stashy: Error using DOM-based sanitization, falling back to regex:', e);
                }
            }

            // Simple but effective fallback sanitization
            return simpleSanitize(html);

        } catch (e) {
            console.error('Stashy: Error in sanitization, using simple fallback:', e);
            return simpleSanitize(html);
        }
    }

    /**
     * Simple sanitization function as a fallback
     * @param {string} html - The HTML content to sanitize
     * @returns {string} The sanitized HTML
     */
    function simpleSanitize(html) {
        if (!html || typeof html !== 'string') {
            return '';
        }

        // Remove dangerous elements and attributes
        let sanitized = String(html)
            // Remove script tags and content
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            // Remove iframe tags
            .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
            // Remove object and embed tags
            .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
            .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
            // Remove form elements
            .replace(/<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi, '')
            .replace(/<input\b[^>]*>/gi, '')
            .replace(/<textarea\b[^<]*(?:(?!<\/textarea>)<[^<]*)*<\/textarea>/gi, '')
            // Remove javascript: URLs
            .replace(/javascript:/gi, 'removed:')
            // Remove event handlers
            .replace(/\son\w+\s*=\s*["'][^"']*["']/gi, '')
            .replace(/\son\w+\s*=\s*[^>\s]+/gi, '');

        return sanitized;
    }

    // Return the public API
    return {
        sanitize,
        simpleSanitize
    };
})();

console.log('Stashy: Improved Sanitization Manager Loaded');
