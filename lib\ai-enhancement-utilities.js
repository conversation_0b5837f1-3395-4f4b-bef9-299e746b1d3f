/**
 * AI Enhancement Utilities
 * Provides Chain-of-Thought (CoT) processing and Dynamic Prompts functionality
 * for improved AI response quality across all Stashy AI features
 */

(function() {
    'use strict';

    /**
     * Configuration for AI enhancement features
     */
    const AI_ENHANCEMENT_CONFIG = {
        chainOfThought: {
            planningTokens: 6000, // Significantly increased for Deep Research
            executionTokens: 8000, // Significantly increased for Deep Research
            planningTemperature: 0.3,
            executionTemperature: 0.4
        },
        dynamicPrompts: {
            subjectDetectionTokens: 2500, // Increased by 2000
            subjectDetectionTemperature: 0.2,
            solutionTokens: 4000, // Increased by 2000
            solutionTemperature: 0.4
        },
        subjects: {
            mathematics: {
                keywords: ['equation', 'solve', 'calculate', 'derivative', 'integral', 'algebra', 'geometry', 'trigonometry', 'calculus', 'statistics'],
                patterns: [/\d+[\+\-\*\/\=]/g, /[xy]\s*[\+\-\*\/\=]/g, /\b(sin|cos|tan|log|ln)\b/g],
                template: 'mathematics'
            },
            physics: {
                keywords: ['force', 'velocity', 'acceleration', 'energy', 'momentum', 'wave', 'frequency', 'mass', 'gravity', 'electric', 'magnetic'],
                patterns: [/\b\d+\s*(m\/s|kg|N|J|W|Hz|V|A)\b/g, /F\s*=\s*ma/g, /E\s*=\s*mc/g],
                template: 'physics'
            },
            chemistry: {
                keywords: ['molecule', 'atom', 'reaction', 'bond', 'element', 'compound', 'molar', 'concentration', 'pH', 'oxidation'],
                patterns: [/[A-Z][a-z]?\d*/g, /\b\d+\s*(mol|M|g\/mol|atm|°C|K)\b/g, /→|⇌/g],
                template: 'chemistry'
            },
            biology: {
                keywords: ['cell', 'DNA', 'protein', 'enzyme', 'gene', 'evolution', 'organism', 'metabolism', 'photosynthesis', 'mitosis'],
                patterns: [/DNA|RNA|ATP|ADP/g, /\b(amino acid|nucleotide|chromosome)\b/g],
                template: 'biology'
            },
            engineering: {
                keywords: ['design', 'system', 'circuit', 'load', 'stress', 'strain', 'efficiency', 'optimization', 'control', 'signal'],
                patterns: [/\b\d+\s*(Hz|dB|V|A|Ω|Pa|MPa|kPa)\b/g, /\b(input|output|feedback)\b/g],
                template: 'engineering'
            },
            economics: {
                keywords: ['market', 'price', 'demand', 'supply', 'cost', 'profit', 'revenue', 'elasticity', 'GDP', 'inflation'],
                patterns: [/\$\d+/g, /\b\d+%\b/g, /\b(marginal|average|total)\s+(cost|revenue|profit)\b/g],
                template: 'economics'
            }
        }
    };

    /**
     * Subject-specific prompt templates
     */
    const SUBJECT_TEMPLATES = {
        mathematics: {
            systemPrompt: "You are an expert mathematician with advanced degrees in pure and applied mathematics. You excel at rigorous mathematical proofs, calculations, and explanations.",
            requirements: [
                "Show every mathematical step with proper notation",
                "Use standard mathematical symbols (×, ÷, ², ³, √, ∫, ∂, etc.)",
                "Verify dimensional consistency in all calculations",
                "Provide mathematical reasoning for each step",
                "Check final answers for mathematical validity"
            ],
            structure: [
                "Problem Analysis: [Mathematical interpretation]",
                "Given Information: [Known values and constraints]",
                "Mathematical Approach: [Method and theorems to use]",
                "Step-by-Step Solution: [Detailed calculations]",
                "Final Answer: [Result with proper notation]",
                "Verification: [Check mathematical validity]"
            ]
        },
        physics: {
            systemPrompt: "You are an expert physicist with deep knowledge of classical mechanics, thermodynamics, electromagnetism, quantum mechanics, and relativity.",
            requirements: [
                "Apply relevant physical laws and principles",
                "Include proper units and dimensional analysis",
                "Show vector calculations where applicable",
                "Explain physical reasoning behind each step",
                "Verify results against physical intuition"
            ],
            structure: [
                "Physical System Analysis: [Identify forces, constraints, etc.]",
                "Given Information: [Known quantities with units]",
                "Physical Principles: [Laws and equations to apply]",
                "Mathematical Solution: [Calculations with units]",
                "Final Answer: [Result with proper units]",
                "Physical Interpretation: [Meaning of the result]"
            ]
        },
        chemistry: {
            systemPrompt: "You are an expert chemist with extensive knowledge of organic, inorganic, physical, and analytical chemistry.",
            requirements: [
                "Use proper chemical notation and formulas",
                "Include balanced chemical equations",
                "Show stoichiometric calculations",
                "Consider reaction mechanisms where relevant",
                "Verify chemical feasibility of results"
            ],
            structure: [
                "Chemical System Analysis: [Identify compounds, reactions]",
                "Given Information: [Concentrations, quantities, conditions]",
                "Chemical Principles: [Relevant laws and concepts]",
                "Calculations: [Stoichiometry, equilibrium, etc.]",
                "Final Answer: [Result with proper units]",
                "Chemical Interpretation: [Significance of the result]"
            ]
        },
        biology: {
            systemPrompt: "You are an expert biologist with comprehensive knowledge of molecular biology, genetics, ecology, physiology, and evolution.",
            requirements: [
                "Use accurate biological terminology",
                "Consider biological context and mechanisms",
                "Include relevant biological processes",
                "Explain biological significance",
                "Connect to broader biological principles"
            ],
            structure: [
                "Biological Context: [System or process involved]",
                "Given Information: [Biological data and conditions]",
                "Biological Principles: [Relevant concepts and mechanisms]",
                "Analysis: [Step-by-step biological reasoning]",
                "Conclusion: [Biological interpretation]",
                "Broader Significance: [Connection to biological systems]"
            ]
        },
        engineering: {
            systemPrompt: "You are an expert engineer with broad knowledge across mechanical, electrical, civil, chemical, and systems engineering.",
            requirements: [
                "Apply engineering design principles",
                "Consider practical constraints and limitations",
                "Include safety and efficiency considerations",
                "Show engineering calculations with proper units",
                "Evaluate feasibility and optimization"
            ],
            structure: [
                "Engineering Problem Definition: [System requirements]",
                "Given Parameters: [Specifications and constraints]",
                "Engineering Approach: [Design methodology]",
                "Calculations: [Engineering analysis with units]",
                "Solution: [Design or result]",
                "Engineering Evaluation: [Performance and feasibility]"
            ]
        },
        economics: {
            systemPrompt: "You are an expert economist with deep understanding of microeconomics, macroeconomics, econometrics, and economic theory.",
            requirements: [
                "Apply relevant economic theories and models",
                "Include quantitative analysis where appropriate",
                "Consider market conditions and assumptions",
                "Explain economic reasoning and implications",
                "Evaluate economic efficiency and outcomes"
            ],
            structure: [
                "Economic Context: [Market or system analysis]",
                "Given Information: [Economic data and parameters]",
                "Economic Theory: [Relevant models and concepts]",
                "Analysis: [Economic calculations and reasoning]",
                "Economic Conclusion: [Results and implications]",
                "Policy/Business Implications: [Practical significance]"
            ]
        }
    };

    /**
     * Chain-of-Thought (CoT) Processing
     * Implements two-step AI processing for complex tasks
     */
    class ChainOfThoughtProcessor {
        constructor(aiModule) {
            this.aiModule = aiModule;
        }

        /**
         * Executes Chain-of-Thought processing for research tasks
         * @param {string} content - Input content to research
         * @param {Object} context - Additional context information
         * @returns {Promise<string>} Final research result
         */
        async executeResearchCoT(content, context = {}) {
            try {
                console.log('AI Enhancement: Starting Chain-of-Thought research processing...');

                // Step 1: Create research plan
                const planningPrompt = this.createResearchPlanningPrompt(content, context);
                const researchPlan = await this.aiModule.generateText(planningPrompt, {
                    maxTokens: AI_ENHANCEMENT_CONFIG.chainOfThought.planningTokens,
                    temperature: AI_ENHANCEMENT_CONFIG.chainOfThought.planningTemperature
                });

                console.log('AI Enhancement: Research plan created, executing comprehensive research...');

                // Step 2: Execute research based on plan
                const executionPrompt = this.createResearchExecutionPrompt(content, researchPlan, context);
                const finalResult = await this.aiModule.generateText(executionPrompt, {
                    maxTokens: AI_ENHANCEMENT_CONFIG.chainOfThought.executionTokens,
                    temperature: AI_ENHANCEMENT_CONFIG.chainOfThought.executionTemperature
                });

                return finalResult;

            } catch (error) {
                console.error('AI Enhancement: Chain-of-Thought research error:', error);
                throw error;
            }
        }

        /**
         * Executes Chain-of-Thought processing for topic understanding tasks
         * @param {string} selectedTopic - Selected topic text to understand
         * @param {Object} context - Additional context information
         * @returns {Promise<string>} Final understanding result
         */
        async executeTopicUnderstandingCoT(selectedTopic, context = {}) {
            try {
                console.log('AI Enhancement: Starting Chain-of-Thought topic understanding processing...');

                // Step 1: Create understanding plan
                const planningPrompt = this.createTopicUnderstandingPlanningPrompt(selectedTopic, context);
                const understandingPlan = await this.aiModule.generateText(planningPrompt, {
                    maxTokens: AI_ENHANCEMENT_CONFIG.chainOfThought.planningTokens,
                    temperature: AI_ENHANCEMENT_CONFIG.chainOfThought.planningTemperature
                });

                console.log('AI Enhancement: Understanding plan created, executing comprehensive analysis...');

                // Step 2: Execute understanding based on plan
                const executionPrompt = this.createTopicUnderstandingExecutionPrompt(selectedTopic, understandingPlan, context);
                const finalResult = await this.aiModule.generateText(executionPrompt, {
                    maxTokens: AI_ENHANCEMENT_CONFIG.chainOfThought.executionTokens,
                    temperature: AI_ENHANCEMENT_CONFIG.chainOfThought.executionTemperature
                });

                return finalResult;

            } catch (error) {
                console.error('AI Enhancement: Chain-of-Thought topic understanding error:', error);
                throw error;
            }
        }

        /**
         * Creates the planning prompt for topic understanding CoT
         * @param {string} selectedTopic - Selected topic text
         * @param {Object} context - Context information
         * @returns {string} Planning prompt
         */
        createTopicUnderstandingPlanningPrompt(selectedTopic, context) {
            const { title = 'Selected Topic', url = '' } = context;

            return `You are an expert knowledge analyst. Analyze the following selected topic and create a comprehensive understanding plan.

SELECTED TOPIC TO UNDERSTAND:
${selectedTopic}

CONTEXT:
Source: ${title}
${url ? `URL: ${url}` : ''}

TASK: Create a detailed understanding plan that identifies:

1. **Core Concepts & Definitions**
   - Key terms and concepts that need explanation
   - Fundamental principles involved
   - Essential background knowledge required

2. **Analysis Framework**
   - What aspects need deep exploration
   - How different components relate to each other
   - Critical questions that need answering

3. **Understanding Strategy**
   - Specific areas to investigate thoroughly
   - Types of insights to provide (theoretical, practical, historical, etc.)
   - Prioritized list of understanding objectives

4. **Expected Insights**
   - What deep understanding should be achieved
   - How this knowledge connects to broader concepts
   - Practical applications and significance

Please provide a clear, structured understanding plan that will guide comprehensive analysis of this topic.`;
        }

        /**
         * Creates the execution prompt for topic understanding CoT
         * @param {string} selectedTopic - Selected topic text
         * @param {string} understandingPlan - Generated understanding plan
         * @param {Object} context - Context information
         * @returns {string} Execution prompt
         */
        createTopicUnderstandingExecutionPrompt(selectedTopic, understandingPlan, context) {
            const { title = 'Selected Topic' } = context;

            return `Based on the understanding plan below, provide comprehensive deep understanding and analysis of the selected topic.

UNDERSTANDING PLAN:
${understandingPlan}

SELECTED TOPIC:
${selectedTopic}

Following the understanding plan above, provide a comprehensive analysis with the following structure:

## 🧠 Deep Understanding Analysis: ${title}

### 📋 Topic Overview
- Clear definition and explanation of the topic
- Core concepts and key principles
- Context and background information

### 🔍 Comprehensive Analysis
- Detailed breakdown of important aspects
- How different components relate to each other
- Underlying mechanisms or processes

### 📊 Key Facts & Insights
- Important data, statistics, or evidence
- Significant findings or discoveries
- Current state of knowledge

### 🌐 Broader Context
- Historical development and evolution
- Connections to related fields or topics
- Current trends and future directions

### 💡 Practical Understanding
- Real-world applications and examples
- Why this topic matters
- Implications and significance

### 🔗 Deeper Connections
- Relationships to other concepts
- Interdisciplinary connections
- Areas for further exploration

Provide comprehensive, accurate information that follows the understanding plan and demonstrates deep knowledge of the selected topic.`;
        }

        /**
         * Creates the planning prompt for research CoT
         * @param {string} content - Input content
         * @param {Object} context - Context information
         * @returns {string} Planning prompt
         */
        createResearchPlanningPrompt(content, context) {
            const { title = 'Content', url = '', contentLength = 0 } = context;

            return `You are an expert research strategist. Analyze the following content and create a comprehensive research plan.

CONTENT TO ANALYZE:
Title: ${title}
${url ? `URL: ${url}` : ''}
Content: ${content.substring(0, 3000)}

TASK: Create a detailed research plan that identifies:

1. **Main Topic & Scope**
   - Primary subject area and key themes
   - Specific aspects that need deeper investigation
   - Research boundaries and focus areas

2. **Research Objectives**
   - What additional information would be most valuable
   - Key questions that need to be answered
   - Knowledge gaps to fill

3. **Research Strategy**
   - Specific areas to investigate in depth
   - Types of information to gather (statistics, expert opinions, recent developments, etc.)
   - Prioritized list of research topics

4. **Expected Outcomes**
   - What insights the research should provide
   - How the research will enhance understanding
   - Practical applications of the research

Please provide a clear, structured research plan that will guide comprehensive analysis of this topic.`;
        }

        /**
         * Creates the execution prompt for research CoT
         * @param {string} content - Original content
         * @param {string} researchPlan - Generated research plan
         * @param {Object} context - Context information
         * @returns {string} Execution prompt
         */
        createResearchExecutionPrompt(content, researchPlan, context) {
            const { title = 'Content' } = context;

            return `Based on the research plan below, provide comprehensive research that expands on the original content with valuable additional insights.

RESEARCH PLAN:
${researchPlan}

ORIGINAL CONTENT:
${content.substring(0, 3000)}

Following the research plan above, provide a comprehensive research report with the following structure:

## 🔬 Deep Research Analysis: ${title}

### 📋 Topic Overview
- Comprehensive background information
- Current context and relevance  
- Key definitions and concepts

### 🔍 Additional Insights
- Related information not covered in original content
- Recent developments and trends
- Expert perspectives and analysis

### 📊 Key Statistics & Facts
- Relevant data and statistics
- Important metrics and benchmarks
- Comparative information

### 🌐 Broader Context
- Industry/field connections
- Related topics and concepts
- Historical background

### 💡 Practical Applications
- Real-world implications
- Use cases and examples
- Best practices and recommendations

### 🔗 Research Connections
- Related research areas
- Interdisciplinary connections
- Future research directions

Provide well-researched, accurate information that follows the research plan and significantly expands on the original content.`;
        }
    }

    /**
     * Dynamic Prompts System
     * Automatically detects subject areas and uses appropriate templates
     */
    class DynamicPromptsProcessor {
        constructor(aiModule) {
            this.aiModule = aiModule;
        }

        /**
         * Detects the subject area of academic content
         * @param {string} content - Content to analyze
         * @returns {Promise<Object>} Subject detection results
         */
        async detectSubject(content) {
            try {
                console.log('AI Enhancement: Detecting subject area...');

                // First, use pattern matching for quick detection
                const patternResults = this.analyzeContentPatterns(content);

                // Then use AI for more sophisticated detection
                const aiDetectionPrompt = `Analyze the following academic content and identify the primary subject area.

CONTENT:
${content.substring(0, 1500)}

TASK: Identify the primary academic subject area from these options:
- Mathematics
- Physics
- Chemistry
- Biology
- Engineering
- Economics
- General Academic

Respond with ONLY the subject name (e.g., "Mathematics" or "Physics"). If multiple subjects apply, choose the most prominent one.`;

                const aiSubject = await this.aiModule.generateText(aiDetectionPrompt, {
                    maxTokens: AI_ENHANCEMENT_CONFIG.dynamicPrompts.subjectDetectionTokens,
                    temperature: AI_ENHANCEMENT_CONFIG.dynamicPrompts.subjectDetectionTemperature
                });

                const detectedSubject = aiSubject.trim().toLowerCase();
                const finalSubject = this.validateAndNormalizeSubject(detectedSubject, patternResults);

                console.log(`AI Enhancement: Subject detected as: ${finalSubject}`);

                return {
                    subject: finalSubject,
                    confidence: this.calculateConfidence(finalSubject, patternResults, content),
                    patternMatches: patternResults
                };

            } catch (error) {
                console.error('AI Enhancement: Subject detection error:', error);
                return {
                    subject: 'general',
                    confidence: 0.5,
                    patternMatches: {}
                };
            }
        }

        /**
         * Analyzes content using pattern matching
         * @param {string} content - Content to analyze
         * @returns {Object} Pattern analysis results
         */
        analyzeContentPatterns(content) {
            const results = {};
            const contentLower = content.toLowerCase();

            for (const [subject, config] of Object.entries(AI_ENHANCEMENT_CONFIG.subjects)) {
                let score = 0;

                // Check keywords
                const keywordMatches = config.keywords.filter(keyword =>
                    contentLower.includes(keyword.toLowerCase())
                ).length;

                // Check patterns
                const patternMatches = config.patterns.reduce((count, pattern) => {
                    const matches = content.match(pattern);
                    return count + (matches ? matches.length : 0);
                }, 0);

                score = keywordMatches * 2 + patternMatches * 3;

                if (score > 0) {
                    results[subject] = {
                        score,
                        keywordMatches,
                        patternMatches
                    };
                }
            }

            return results;
        }

        /**
         * Validates and normalizes the detected subject
         * @param {string} aiSubject - AI-detected subject
         * @param {Object} patternResults - Pattern matching results
         * @returns {string} Normalized subject
         */
        validateAndNormalizeSubject(aiSubject, patternResults) {
            const validSubjects = Object.keys(AI_ENHANCEMENT_CONFIG.subjects);

            // Try to match AI result to valid subjects
            for (const subject of validSubjects) {
                if (aiSubject.includes(subject)) {
                    return subject;
                }
            }

            // Fall back to pattern matching if AI result is unclear
            if (Object.keys(patternResults).length > 0) {
                const topSubject = Object.entries(patternResults)
                    .sort(([,a], [,b]) => b.score - a.score)[0][0];
                return topSubject;
            }

            return 'general';
        }

        /**
         * Calculates confidence in subject detection
         * @param {string} subject - Detected subject
         * @param {Object} patternResults - Pattern results
         * @param {string} content - Original content
         * @returns {number} Confidence score (0-1)
         */
        calculateConfidence(subject, patternResults, content) {
            if (subject === 'general') return 0.5;

            const subjectData = patternResults[subject];
            if (!subjectData) return 0.6;

            const contentLength = content.length;
            const normalizedScore = Math.min(subjectData.score / Math.max(contentLength / 100, 1), 1);

            return Math.max(0.6, normalizedScore);
        }

        /**
         * Creates a subject-specific solving prompt
         * @param {string} content - Problem content
         * @param {Object} subjectInfo - Subject detection results
         * @returns {string} Customized prompt
         */
        createSubjectSpecificPrompt(content, subjectInfo) {
            const { subject, confidence } = subjectInfo;
            const template = SUBJECT_TEMPLATES[subject] || SUBJECT_TEMPLATES.mathematics;

            const prompt = `${template.systemPrompt}

CRITICAL REQUIREMENTS:
${template.requirements.map((req, i) => `${i + 1}. **${req.split(':')[0]}**: ${req}`).join('\n')}

RESPONSE STRUCTURE:
${template.structure.map((section, i) => `${i + 1}. ${section}`).join('\n')}

FORMATTING GUIDELINES:
- Use proper notation and symbols for ${subject}
- Include units in every calculation step
- Highlight final answers with clear formatting
- Show intermediate results to maintain clarity
- Structure your response with clear numbered sections
- Use proper line breaks between different steps and concepts
- Ensure each operation is on its own line for clarity

PROBLEM TO SOLVE:
${content}

COMPREHENSIVE SOLUTION:`;

            return prompt;
        }

        /**
         * Solves academic problems using dynamic prompts
         * @param {string} content - Problem content
         * @returns {Promise<string>} Solution
         */
        async solveWithDynamicPrompt(content) {
            try {
                // Step 1: Detect subject area
                const subjectInfo = await this.detectSubject(content);

                // Step 2: Create subject-specific prompt
                const prompt = this.createSubjectSpecificPrompt(content, subjectInfo);

                // Step 3: Generate solution
                const solution = await this.aiModule.generateText(prompt, {
                    maxTokens: AI_ENHANCEMENT_CONFIG.dynamicPrompts.solutionTokens,
                    temperature: AI_ENHANCEMENT_CONFIG.dynamicPrompts.solutionTemperature
                });

                return {
                    solution,
                    subjectInfo,
                    prompt: prompt.substring(0, 200) + '...' // For debugging
                };

            } catch (error) {
                console.error('AI Enhancement: Dynamic prompt solving error:', error);
                throw error;
            }
        }
    }

    /**
     * Main AI Enhancement Utilities API
     */
    const AIEnhancementUtilities = {
        /**
         * Creates a Chain-of-Thought processor
         * @param {Object} aiModule - AI module instance
         * @returns {ChainOfThoughtProcessor} CoT processor
         */
        createCoTProcessor(aiModule) {
            return new ChainOfThoughtProcessor(aiModule);
        },

        /**
         * Creates a Dynamic Prompts processor
         * @param {Object} aiModule - AI module instance
         * @returns {DynamicPromptsProcessor} Dynamic prompts processor
         */
        createDynamicPromptsProcessor(aiModule) {
            return new DynamicPromptsProcessor(aiModule);
        },

        /**
         * Gets available subject templates
         * @returns {Object} Subject templates
         */
        getSubjectTemplates() {
            return SUBJECT_TEMPLATES;
        },

        /**
         * Gets enhancement configuration
         * @returns {Object} Configuration
         */
        getConfig() {
            return AI_ENHANCEMENT_CONFIG;
        }
    };

    // Export to global scope
    window.AIEnhancementUtilities = AIEnhancementUtilities;

    console.log('AI Enhancement Utilities: Module loaded successfully');

})();
