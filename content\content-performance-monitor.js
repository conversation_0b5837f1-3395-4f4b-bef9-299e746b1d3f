/**
 * Stashy - Simplified Performance Monitor Module
 * Basic performance monitoring without aggressive optimizations
 */

// Make sure we don't redefine functions if the script is injected multiple times
if (typeof window.SB_PERFORMANCE_MONITOR_LOADED === 'undefined') {
    window.SB_PERFORMANCE_MONITOR_LOADED = true;

    // Loading Simplified Performance Monitor Module

    // Simplified performance monitoring configuration
    const performanceConfig = {
        enabled: false, // Disabled by default to prevent issues
        debug: false
    };

    /**
     * Simplified performance monitoring - no-op functions to maintain compatibility
     */
    window.startPerformanceMonitoring = function(interactionType) {
        // No-op - performance monitoring disabled
        // Performance monitoring disabled
    };

    /**
     * Stops performance monitoring - no-op
     */
    window.stopPerformanceMonitoring = function() {
        // No-op - performance monitoring disabled
        if (performanceConfig.debug) {
            console.log("Stashy: Performance monitoring disabled");
        }
    };

    /**
     * Gets current performance metrics - simplified
     * @returns {Object} Performance metrics
     */
    window.getPerformanceMetrics = function() {
        return {
            fps: 60,
            memoryUsage: 0,
            optimizationLevel: 0,
            isMonitoring: false
        };
    };

    /**
     * Updates performance configuration - no-op
     * @param {Object} config - New configuration options
     */
    window.updatePerformanceConfig = function(config) {
        // No-op - performance monitoring disabled
        // Performance configuration update ignored (monitoring disabled)
    };

    // Simplified Performance Monitor Module Loaded
}
