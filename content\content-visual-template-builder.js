// --- START OF FILE content-visual-template-builder.js ---

/**
 * Visual Template Builder for Stashy
 * Provides drag-and-drop template creation without HTML knowledge
 */

let visualBuilderModal = null;
let visualBuilderCanvas = null;
let visualBuilderPreview = null;
let currentTemplate = { elements: [], metadata: {} };
let draggedElement = null;
let elementCounter = 0;
let visualBuilderTemplateHistory = [];
let visualBuilderHistoryIndex = -1;
let autoSaveTimer = null;
let isFirstTimeUser = true;

// Template session management for switching between templates
let templateSessions = new Map(); // Store multiple template editing sessions
let currentSessionId = null;
let sessionCounter = 0;
let selectedTemplateInBuilder = null;

// Template building components
const TEMPLATE_COMPONENTS = {
    header1: {
        name: 'Large Header',
        icon: '📝',
        html: '<h1>{{text}}</h1>',
        defaultText: 'Main Title',
        editable: true,
        category: 'headers'
    },
    header2: {
        name: 'Medium Header',
        icon: '📄',
        html: '<h2>{{text}}</h2>',
        defaultText: 'Section Title',
        editable: true,
        category: 'headers'
    },
    header3: {
        name: 'Small Header',
        icon: '📃',
        html: '<h3>{{text}}</h3>',
        defaultText: 'Subsection',
        editable: true,
        category: 'headers'
    },
    paragraph: {
        name: 'Text Paragraph',
        icon: '📝',
        html: '<p>{{text}}</p>',
        defaultText: 'Enter your text here...',
        editable: true,
        category: 'text'
    },
    boldText: {
        name: 'Bold Text',
        icon: '🔤',
        html: '<p><strong>{{text}}</strong></p>',
        defaultText: 'Important text',
        editable: true,
        category: 'text'
    },
    italicText: {
        name: 'Italic Text',
        icon: '🔤',
        html: '<p><em>{{text}}</em></p>',
        defaultText: 'Emphasized text',
        editable: true,
        category: 'text'
    },
    dateField: {
        name: 'Current Date',
        icon: '📅',
        html: '<p><strong>Date:</strong> {DATE}</p>',
        defaultText: null,
        editable: false,
        category: 'smart'
    },
    timeField: {
        name: 'Current Time',
        icon: '🕐',
        html: '<p><strong>Time:</strong> {{CurrentTime}}</p>',
        defaultText: null,
        editable: false,
        category: 'smart'
    },
    urlField: {
        name: 'Page URL',
        icon: '🔗',
        html: '<p><strong>URL:</strong> {URL}</p>',
        defaultText: null,
        editable: false,
        category: 'smart'
    },
    userPrompt: {
        name: 'User Input',
        icon: '❓',
        html: '<p><strong>{{label}}:</strong> {{Prompt:{{question}}|{{defaultValue}}}}</p>',
        defaultText: null,
        editable: true,
        category: 'interactive',
        customFields: {
            label: 'Label',
            question: 'What would you like to enter?',
            defaultValue: 'Default value'
        }
    },
    divider: {
        name: 'Divider Line',
        icon: '➖',
        html: '<hr style="margin: 15px 0; border: 1px solid #ddd;">',
        defaultText: null,
        editable: false,
        category: 'layout'
    },
    spacer: {
        name: 'Blank Space',
        icon: '⬜',
        html: '<div style="height: 20px;"></div>',
        defaultText: null,
        editable: false,
        category: 'layout'
    },
    bulletList: {
        name: 'Bullet List',
        icon: '📋',
        html: '<ul><li>{{item1}}</li><li>{{item2}}</li><li>{{item3}}</li></ul>',
        defaultText: null,
        editable: true,
        category: 'lists',
        customFields: {
            item1: 'First item',
            item2: 'Second item',
            item3: 'Third item'
        }
    },
    numberedList: {
        name: 'Numbered List',
        icon: '🔢',
        html: '<ol><li>{{item1}}</li><li>{{item2}}</li><li>{{item3}}</li></ol>',
        defaultText: null,
        editable: true,
        category: 'lists',
        customFields: {
            item1: 'First step',
            item2: 'Second step',
            item3: 'Third step'
        }
    },
    checkbox: {
        name: 'Checkbox',
        icon: '☑️',
        html: '<p><input type="checkbox"> {{text}}</p>',
        defaultText: 'Checkbox item',
        editable: true,
        category: 'interactive'
    },
    checkboxList: {
        name: 'Checkbox List',
        icon: '✅',
        html: '<div><p><input type="checkbox"> {{item1}}</p><p><input type="checkbox"> {{item2}}</p><p><input type="checkbox"> {{item3}}</p></div>',
        defaultText: null,
        editable: true,
        category: 'interactive',
        customFields: {
            item1: 'Task 1',
            item2: 'Task 2',
            item3: 'Task 3'
        }
    },
    imagePlaceholder: {
        name: 'Image Placeholder',
        icon: '🖼️',
        html: '<div style="border: 2px dashed #ccc; padding: 20px; text-align: center; margin: 10px 0; background: #f9f9f9;"><p>📷 {{text}}</p></div>',
        defaultText: 'Image will be placed here',
        editable: true,
        category: 'media'
    },
    signatureField: {
        name: 'Signature Field',
        icon: '✍️',
        html: '<div style="border-bottom: 1px solid #333; margin: 20px 0; padding-bottom: 5px;"><p style="margin: 0; font-size: 12px; color: #666;">{{label}}</p><div style="height: 40px;"></div></div>',
        defaultText: null,
        editable: true,
        category: 'interactive',
        customFields: {
            label: 'Signature:'
        }
    },
    dateInput: {
        name: 'Date Input Field',
        icon: '📅',
        html: '<p><strong>{{label}}:</strong> {{Prompt:{{question}}|{{defaultValue}}}}</p>',
        defaultText: null,
        editable: true,
        category: 'interactive',
        customFields: {
            label: 'Date',
            question: 'Enter date (MM/DD/YYYY)',
            defaultValue: 'MM/DD/YYYY'
        }
    },
    textArea: {
        name: 'Text Area',
        icon: '📝',
        html: '<div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0; min-height: 80px; background: #fafafa;"><p style="margin: 0; font-size: 12px; color: #666;">{{label}}</p><p style="margin: 5px 0 0 0; color: #999;">{{placeholder}}</p></div>',
        defaultText: null,
        editable: true,
        category: 'interactive',
        customFields: {
            label: 'Notes:',
            placeholder: 'Enter your notes here...'
        }
    },
    linkButton: {
        name: 'Link Button',
        icon: '🔗',
        html: '<p><a href="{{url}}" style="background: #4285f4; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; display: inline-block;">{{text}}</a></p>',
        defaultText: null,
        editable: true,
        category: 'interactive',
        customFields: {
            text: 'Click Here',
            url: 'https://example.com'
        }
    },
    contactInfo: {
        name: 'Contact Info',
        icon: '📞',
        html: '<div style="border: 1px solid #e0e0e0; padding: 15px; margin: 10px 0; border-radius: 6px; background: #f9f9f9;"><p><strong>{{name}}</strong></p><p>📧 {{email}}</p><p>📞 {{phone}}</p></div>',
        defaultText: null,
        editable: true,
        category: 'business',
        customFields: {
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '(*************'
        }
    },

    // Table Components
    simpleTable: {
        name: 'Simple Table',
        icon: '📊',
        html: '<table style="width: 100%; border-collapse: collapse; margin: 10px 0; border: 1px solid #ddd;"><thead><tr style="background: #f5f5f5;"><th style="border: 1px solid #ddd; padding: 8px;">Header 1</th><th style="border: 1px solid #ddd; padding: 8px;">Header 2</th><th style="border: 1px solid #ddd; padding: 8px;">Header 3</th></tr></thead><tbody><tr><td style="border: 1px solid #ddd; padding: 8px;">Cell 1-1</td><td style="border: 1px solid #ddd; padding: 8px;">Cell 1-2</td><td style="border: 1px solid #ddd; padding: 8px;">Cell 1-3</td></tr><tr><td style="border: 1px solid #ddd; padding: 8px;">Cell 2-1</td><td style="border: 1px solid #ddd; padding: 8px;">Cell 2-2</td><td style="border: 1px solid #ddd; padding: 8px;">Cell 2-3</td></tr></tbody></table>',
        defaultText: null,
        editable: true,
        category: 'tables',
        customFields: {
            title: 'Data Table'
        }
    },

    dataTable: {
        name: 'Data Table',
        icon: '📈',
        html: '<div style="margin: 15px 0;"><h4 style="margin: 0 0 10px 0; color: #333;">{{title}}</h4><table style="width: 100%; border-collapse: collapse; border: 2px solid #ddd;"><thead><tr style="background: #f8f9fa;"><th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Item</th><th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Value</th><th style="border: 1px solid #ddd; padding: 10px; text-align: left;">Status</th></tr></thead><tbody><tr><td style="border: 1px solid #ddd; padding: 8px;">Sample Item 1</td><td style="border: 1px solid #ddd; padding: 8px;">100</td><td style="border: 1px solid #ddd; padding: 8px;">✅ Complete</td></tr><tr><td style="border: 1px solid #ddd; padding: 8px;">Sample Item 2</td><td style="border: 1px solid #ddd; padding: 8px;">75</td><td style="border: 1px solid #ddd; padding: 8px;">🔄 In Progress</td></tr><tr><td style="border: 1px solid #ddd; padding: 8px;">Sample Item 3</td><td style="border: 1px solid #ddd; padding: 8px;">50</td><td style="border: 1px solid #ddd; padding: 8px;">⏳ Pending</td></tr></tbody></table></div>',
        defaultText: null,
        editable: true,
        category: 'tables',
        customFields: {
            title: 'Data Summary'
        }
    },

    scheduleTable: {
        name: 'Schedule Table',
        icon: '📅',
        html: '<div style="margin: 15px 0;"><h4 style="margin: 0 0 10px 0; color: #333;">{{title}}</h4><table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;"><thead><tr style="background: #667eea; color: white;"><th style="border: 1px solid #ddd; padding: 10px;">Time</th><th style="border: 1px solid #ddd; padding: 10px;">Monday</th><th style="border: 1px solid #ddd; padding: 10px;">Tuesday</th><th style="border: 1px solid #ddd; padding: 10px;">Wednesday</th><th style="border: 1px solid #ddd; padding: 10px;">Thursday</th><th style="border: 1px solid #ddd; padding: 10px;">Friday</th></tr></thead><tbody><tr><td style="border: 1px solid #ddd; padding: 8px; font-weight: bold; background: #f8f9fa;">9:00 AM</td><td style="border: 1px solid #ddd; padding: 8px;">Meeting</td><td style="border: 1px solid #ddd; padding: 8px;">Project Work</td><td style="border: 1px solid #ddd; padding: 8px;">Review</td><td style="border: 1px solid #ddd; padding: 8px;">Planning</td><td style="border: 1px solid #ddd; padding: 8px;">Team Sync</td></tr><tr><td style="border: 1px solid #ddd; padding: 8px; font-weight: bold; background: #f8f9fa;">11:00 AM</td><td style="border: 1px solid #ddd; padding: 8px;">Development</td><td style="border: 1px solid #ddd; padding: 8px;">Testing</td><td style="border: 1px solid #ddd; padding: 8px;">Documentation</td><td style="border: 1px solid #ddd; padding: 8px;">Code Review</td><td style="border: 1px solid #ddd; padding: 8px;">Deployment</td></tr></tbody></table></div>',
        defaultText: null,
        editable: true,
        category: 'tables',
        customFields: {
            title: 'Weekly Schedule'
        }
    },

    comparisonTable: {
        name: 'Comparison Table',
        icon: '⚖️',
        html: '<div style="margin: 15px 0;"><h4 style="margin: 0 0 10px 0; color: #333;">{{title}}</h4><table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;"><thead><tr style="background: #28a745; color: white;"><th style="border: 1px solid #ddd; padding: 12px; text-align: left;">Feature</th><th style="border: 1px solid #ddd; padding: 12px; text-align: center;">Basic</th><th style="border: 1px solid #ddd; padding: 12px; text-align: center;">Pro</th><th style="border: 1px solid #ddd; padding: 12px; text-align: center;">Enterprise</th></tr></thead><tbody><tr><td style="border: 1px solid #ddd; padding: 10px; font-weight: bold;">Storage</td><td style="border: 1px solid #ddd; padding: 10px; text-align: center;">1GB</td><td style="border: 1px solid #ddd; padding: 10px; text-align: center;">10GB</td><td style="border: 1px solid #ddd; padding: 10px; text-align: center;">Unlimited</td></tr><tr style="background: #f8f9fa;"><td style="border: 1px solid #ddd; padding: 10px; font-weight: bold;">Users</td><td style="border: 1px solid #ddd; padding: 10px; text-align: center;">1</td><td style="border: 1px solid #ddd; padding: 10px; text-align: center;">5</td><td style="border: 1px solid #ddd; padding: 10px; text-align: center;">Unlimited</td></tr><tr><td style="border: 1px solid #ddd; padding: 10px; font-weight: bold;">Support</td><td style="border: 1px solid #ddd; padding: 10px; text-align: center;">❌</td><td style="border: 1px solid #ddd; padding: 10px; text-align: center;">✅</td><td style="border: 1px solid #ddd; padding: 10px; text-align: center;">✅ Priority</td></tr></tbody></table></div>',
        defaultText: null,
        editable: true,
        category: 'tables',
        customFields: {
            title: 'Feature Comparison'
        }
    },

    tableBuilder: {
        name: 'Table Builder',
        icon: '🔧',
        html: '<div style="margin: 15px 0; padding: 20px; border: 2px dashed #ddd; border-radius: 8px; text-align: center; background: #f9f9f9;"><h4 style="margin: 0 0 10px 0; color: #333;">🔧 Interactive Table Builder</h4><p style="margin: 0 0 15px 0; color: #666;">Click "Edit" to create a custom table with your own rows and columns</p><button style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">Build Custom Table</button></div>',
        defaultText: null,
        editable: true,
        category: 'tables',
        customFields: {
            rows: '3',
            columns: '3',
            hasHeaders: 'true',
            title: 'Custom Table'
        }
    },

    dynamicTable: {
        name: 'Dynamic Table',
        icon: '📋',
        html: '<div id="dynamic-table-container" style="margin: 15px 0;"><h4 style="margin: 0 0 10px 0; color: #333;">{{title}}</h4><div style="margin-bottom: 10px;"><button onclick="addTableRow(this)" style="background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin-right: 5px; cursor: pointer;">+ Add Row</button><button onclick="addTableColumn(this)" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin-right: 5px; cursor: pointer;">+ Add Column</button><button onclick="removeTableRow(this)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; margin-right: 5px; cursor: pointer;">- Remove Row</button><button onclick="removeTableColumn(this)" style="background: #fd7e14; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">- Remove Column</button></div><table class="dynamic-table" style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;"><thead><tr style="background: #f8f9fa;"><th contenteditable="true" style="border: 1px solid #ddd; padding: 8px; min-width: 100px;">Header 1</th><th contenteditable="true" style="border: 1px solid #ddd; padding: 8px; min-width: 100px;">Header 2</th><th contenteditable="true" style="border: 1px solid #ddd; padding: 8px; min-width: 100px;">Header 3</th></tr></thead><tbody><tr><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;">Cell 1-1</td><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;">Cell 1-2</td><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;">Cell 1-3</td></tr><tr><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;">Cell 2-1</td><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;">Cell 2-2</td><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;">Cell 2-3</td></tr></tbody></table></div>',
        defaultText: null,
        editable: true,
        category: 'tables',
        customFields: {
            title: 'Dynamic Table'
        }
    },

    smartTable: {
        name: 'Smart Table',
        icon: '🧠',
        html: '<div style="margin: 15px 0;"><h4 style="margin: 0 0 10px 0; color: #333;">{{title}}</h4><div style="margin-bottom: 10px; padding: 10px; background: #e3f2fd; border-radius: 5px; font-size: 12px;"><strong>Smart Features:</strong> Auto-expand when typing in last cell • Double-click headers to sort • Right-click for options</div><table class="smart-table" style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;" data-smart="true"><thead><tr style="background: #667eea; color: white;"><th contenteditable="true" style="border: 1px solid #ddd; padding: 10px; cursor: pointer;" onclick="sortTable(this, 0)">Name ↕️</th><th contenteditable="true" style="border: 1px solid #ddd; padding: 10px; cursor: pointer;" onclick="sortTable(this, 1)">Value ↕️</th><th contenteditable="true" style="border: 1px solid #ddd; padding: 10px; cursor: pointer;" onclick="sortTable(this, 2)">Status ↕️</th></tr></thead><tbody><tr><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;" onblur="checkAutoExpand(this)">Item 1</td><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;" onblur="checkAutoExpand(this)">100</td><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;" onblur="checkAutoExpand(this)">✅ Active</td></tr><tr><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;" onblur="checkAutoExpand(this)">Item 2</td><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;" onblur="checkAutoExpand(this)">75</td><td contenteditable="true" style="border: 1px solid #ddd; padding: 8px;" onblur="checkAutoExpand(this)">🔄 Pending</td></tr></tbody></table></div>',
        defaultText: null,
        editable: true,
        category: 'tables',
        customFields: {
            title: 'Smart Data Table'
        }
    },

    // Enhanced Text Formatting Components
    blockquote: {
        name: 'Blockquote',
        icon: '💬',
        html: '<blockquote style="border-left: 4px solid #007bff; padding: 15px 20px; margin: 20px 0; background: #f8f9fa; font-style: italic; color: #555;">{{text}}</blockquote>',
        defaultText: 'This is an important quote or highlighted text that stands out from the rest of the content.',
        editable: true,
        category: 'text'
    },
    codeBlock: {
        name: 'Code Block',
        icon: '💻',
        html: '<pre style="background: #f4f4f4; border: 1px solid #ddd; border-radius: 6px; padding: 15px; overflow-x: auto; font-family: \'Courier New\', monospace; font-size: 14px; line-height: 1.4;"><code>{{text}}</code></pre>',
        defaultText: 'function example() {\n    console.log("Hello World!");\n    return true;\n}',
        editable: true,
        category: 'text'
    },
    calloutInfo: {
        name: 'Info Callout',
        icon: '💡',
        html: '<div style="background: #e3f2fd; border: 1px solid #2196f3; border-left: 4px solid #2196f3; border-radius: 6px; padding: 15px; margin: 15px 0;"><div style="font-weight: bold; color: #1976d2; margin-bottom: 8px; display: flex; align-items: center;"><span style="margin-right: 8px;">💡</span>{{title}}</div><div style="color: #333;">{{text}}</div></div>',
        defaultText: 'This is important information that needs attention.',
        customFields: {
            title: 'Important Note'
        },
        editable: true,
        category: 'text'
    },
    calloutWarning: {
        name: 'Warning Callout',
        icon: '⚠️',
        html: '<div style="background: #fff3cd; border: 1px solid #ffc107; border-left: 4px solid #ffc107; border-radius: 6px; padding: 15px; margin: 15px 0;"><div style="font-weight: bold; color: #856404; margin-bottom: 8px; display: flex; align-items: center;"><span style="margin-right: 8px;">⚠️</span>{{title}}</div><div style="color: #333;">{{text}}</div></div>',
        defaultText: 'Please pay attention to this warning message.',
        customFields: {
            title: 'Warning'
        },
        editable: true,
        category: 'text'
    },
    calloutSuccess: {
        name: 'Success Callout',
        icon: '✅',
        html: '<div style="background: #d4edda; border: 1px solid #28a745; border-left: 4px solid #28a745; border-radius: 6px; padding: 15px; margin: 15px 0;"><div style="font-weight: bold; color: #155724; margin-bottom: 8px; display: flex; align-items: center;"><span style="margin-right: 8px;">✅</span>{{title}}</div><div style="color: #333;">{{text}}</div></div>',
        defaultText: 'This indicates a successful operation or positive outcome.',
        customFields: {
            title: 'Success'
        },
        editable: true,
        category: 'text'
    },
    calloutError: {
        name: 'Error Callout',
        icon: '❌',
        html: '<div style="background: #f8d7da; border: 1px solid #dc3545; border-left: 4px solid #dc3545; border-radius: 6px; padding: 15px; margin: 15px 0;"><div style="font-weight: bold; color: #721c24; margin-bottom: 8px; display: flex; align-items: center;"><span style="margin-right: 8px;">❌</span>{{title}}</div><div style="color: #333;">{{text}}</div></div>',
        defaultText: 'This indicates an error or problem that needs to be addressed.',
        customFields: {
            title: 'Error'
        },
        editable: true,
        category: 'text'
    },

    // Enhanced Form Elements
    textInput: {
        name: 'Text Input',
        icon: '📝',
        html: '<div style="margin: 15px 0;"><label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">{{label}}</label><input type="text" placeholder="{{placeholder}}" style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; box-sizing: border-box;" /></div>',
        customFields: {
            label: 'Input Label',
            placeholder: 'Enter text here...'
        },
        category: 'forms'
    },
    emailInput: {
        name: 'Email Input',
        icon: '📧',
        html: '<div style="margin: 15px 0;"><label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">{{label}}</label><input type="email" placeholder="{{placeholder}}" style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; box-sizing: border-box;" /></div>',
        customFields: {
            label: 'Email Address',
            placeholder: 'Enter your email...'
        },
        category: 'forms'
    },
    numberInput: {
        name: 'Number Input',
        icon: '🔢',
        html: '<div style="margin: 15px 0;"><label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">{{label}}</label><input type="number" placeholder="{{placeholder}}" style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; box-sizing: border-box;" /></div>',
        customFields: {
            label: 'Number',
            placeholder: 'Enter number...'
        },
        category: 'forms'
    },
    radioGroup: {
        name: 'Radio Group',
        icon: '🔘',
        html: '<div style="margin: 15px 0;"><div style="font-weight: 500; margin-bottom: 10px; color: #333;">{{question}}</div><label style="display: flex; align-items: center; margin: 8px 0; cursor: pointer;"><input type="radio" name="{{name}}" value="option1" style="margin-right: 8px;" /> <span>{{option1}}</span></label><label style="display: flex; align-items: center; margin: 8px 0; cursor: pointer;"><input type="radio" name="{{name}}" value="option2" style="margin-right: 8px;" /> <span>{{option2}}</span></label><label style="display: flex; align-items: center; margin: 8px 0; cursor: pointer;"><input type="radio" name="{{name}}" value="option3" style="margin-right: 8px;" /> <span>{{option3}}</span></label></div>',
        customFields: {
            question: 'Select an option:',
            name: 'radioGroup1',
            option1: 'Option 1',
            option2: 'Option 2',
            option3: 'Option 3'
        },
        category: 'forms'
    },
    dropdown: {
        name: 'Dropdown Select',
        icon: '📋',
        html: '<div style="margin: 15px 0;"><label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">{{label}}</label><select style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; box-sizing: border-box; background: white;"><option value="">{{placeholder}}</option><option value="option1">{{option1}}</option><option value="option2">{{option2}}</option><option value="option3">{{option3}}</option></select></div>',
        customFields: {
            label: 'Select Option',
            placeholder: 'Choose an option...',
            option1: 'Option 1',
            option2: 'Option 2',
            option3: 'Option 3'
        },
        category: 'forms'
    },
    textareaField: {
        name: 'Textarea Field',
        icon: '📄',
        html: '<div style="margin: 15px 0;"><label style="display: block; margin-bottom: 5px; font-weight: 500; color: #333;">{{label}}</label><textarea placeholder="{{placeholder}}" rows="4" style="width: 100%; padding: 10px 12px; border: 1px solid #ddd; border-radius: 6px; font-size: 14px; box-sizing: border-box; resize: vertical; font-family: inherit;"></textarea></div>',
        customFields: {
            label: 'Comments',
            placeholder: 'Enter your comments here...'
        },
        category: 'forms'
    },

    // Media Components
    videoEmbed: {
        name: 'Video Embed',
        icon: '🎥',
        html: '<div style="margin: 20px 0;"><div style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden; border-radius: 8px; background: #f0f0f0;"><div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: #666;"><div style="font-size: 48px; margin-bottom: 10px;">🎥</div><div style="font-size: 16px; font-weight: 500;">{{title}}</div><div style="font-size: 14px; margin-top: 5px;">{{description}}</div><div style="font-size: 12px; margin-top: 10px; color: #999;">Video URL: {{url}}</div></div></div></div>',
        customFields: {
            title: 'Video Title',
            description: 'Video description or embed code will be placed here',
            url: 'https://youtube.com/watch?v=...'
        },
        category: 'media'
    },
    audioPlayer: {
        name: 'Audio Player',
        icon: '🎵',
        html: '<div style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9;"><div style="display: flex; align-items: center; margin-bottom: 15px;"><span style="font-size: 24px; margin-right: 10px;">🎵</span><div><div style="font-weight: 500; color: #333;">{{title}}</div><div style="font-size: 14px; color: #666;">{{artist}}</div></div></div><div style="background: #e0e0e0; height: 6px; border-radius: 3px; margin-bottom: 10px;"><div style="background: #007bff; height: 6px; border-radius: 3px; width: 30%;"></div></div><div style="display: flex; align-items: center; justify-content: space-between; font-size: 12px; color: #666;"><span>0:30</span><div style="display: flex; gap: 10px;"><button style="background: none; border: none; font-size: 16px; cursor: pointer;">⏮️</button><button style="background: none; border: none; font-size: 20px; cursor: pointer;">▶️</button><button style="background: none; border: none; font-size: 16px; cursor: pointer;">⏭️</button></div><span>{{duration}}</span></div></div>',
        customFields: {
            title: 'Audio Track Title',
            artist: 'Artist Name',
            duration: '3:45'
        },
        category: 'media'
    },
    imageGallery: {
        name: 'Image Gallery',
        icon: '🖼️',
        html: '<div style="margin: 20px 0;"><h4 style="margin: 0 0 15px 0; color: #333;">{{title}}</h4><div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;"><div style="aspect-ratio: 1; background: #f0f0f0; border: 2px dashed #ccc; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 12px; text-align: center;">📷<br>Image 1</div><div style="aspect-ratio: 1; background: #f0f0f0; border: 2px dashed #ccc; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 12px; text-align: center;">📷<br>Image 2</div><div style="aspect-ratio: 1; background: #f0f0f0; border: 2px dashed #ccc; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 12px; text-align: center;">📷<br>Image 3</div></div></div>',
        customFields: {
            title: 'Photo Gallery'
        },
        category: 'media'
    },

    // Layout Components
    dividerLine: {
        name: 'Divider Line',
        icon: '➖',
        html: '<div style="margin: 25px 0;"><hr style="border: none; height: 1px; background: {{color}}; margin: 0;" /></div>',
        customFields: {
            color: '#ddd'
        },
        category: 'layout'
    },
    spacer: {
        name: 'Spacer',
        icon: '⬜',
        html: '<div style="height: {{height}}px; margin: 10px 0;"></div>',
        customFields: {
            height: '30'
        },
        category: 'layout'
    },
    twoColumns: {
        name: 'Two Columns',
        icon: '📰',
        html: '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;"><div style="padding: 15px; border: 1px solid #e0e0e0; border-radius: 6px; background: #f9f9f9;"><h4 style="margin: 0 0 10px 0; color: #333;">{{leftTitle}}</h4><p style="margin: 0; color: #666;">{{leftContent}}</p></div><div style="padding: 15px; border: 1px solid #e0e0e0; border-radius: 6px; background: #f9f9f9;"><h4 style="margin: 0 0 10px 0; color: #333;">{{rightTitle}}</h4><p style="margin: 0; color: #666;">{{rightContent}}</p></div></div>',
        customFields: {
            leftTitle: 'Left Column',
            leftContent: 'Content for the left column goes here.',
            rightTitle: 'Right Column',
            rightContent: 'Content for the right column goes here.'
        },
        category: 'layout'
    },
    threeColumns: {
        name: 'Three Columns',
        icon: '📊',
        html: '<div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin: 20px 0;"><div style="padding: 15px; border: 1px solid #e0e0e0; border-radius: 6px; background: #f9f9f9; text-align: center;"><h4 style="margin: 0 0 10px 0; color: #333;">{{col1Title}}</h4><p style="margin: 0; color: #666; font-size: 14px;">{{col1Content}}</p></div><div style="padding: 15px; border: 1px solid #e0e0e0; border-radius: 6px; background: #f9f9f9; text-align: center;"><h4 style="margin: 0 0 10px 0; color: #333;">{{col2Title}}</h4><p style="margin: 0; color: #666; font-size: 14px;">{{col2Content}}</p></div><div style="padding: 15px; border: 1px solid #e0e0e0; border-radius: 6px; background: #f9f9f9; text-align: center;"><h4 style="margin: 0 0 10px 0; color: #333;">{{col3Title}}</h4><p style="margin: 0; color: #666; font-size: 14px;">{{col3Content}}</p></div></div>',
        customFields: {
            col1Title: 'Column 1',
            col1Content: 'First column content',
            col2Title: 'Column 2',
            col2Content: 'Second column content',
            col3Title: 'Column 3',
            col3Content: 'Third column content'
        },
        category: 'layout'
    },
    container: {
        name: 'Container Box',
        icon: '📦',
        html: '<div style="margin: 20px 0; padding: 20px; border: 1px solid {{borderColor}}; border-radius: 8px; background: {{bgColor}};"><h4 style="margin: 0 0 15px 0; color: #333;">{{title}}</h4><div style="color: #666;">{{content}}</div></div>',
        customFields: {
            title: 'Container Title',
            content: 'Container content goes here. You can add any text or information.',
            bgColor: '#f8f9fa',
            borderColor: '#e0e0e0'
        },
        category: 'layout'
    },

    // Interactive Elements
    primaryButton: {
        name: 'Primary Button',
        icon: '🔵',
        html: '<div style="margin: 15px 0; text-align: {{align}};"><button style="background: {{bgColor}}; color: {{textColor}}; border: none; padding: 12px 24px; border-radius: 6px; font-size: 16px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.opacity=\'0.9\'" onmouseout="this.style.opacity=\'1\'">{{text}}</button></div>',
        customFields: {
            text: 'Click Here',
            bgColor: '#007bff',
            textColor: 'white',
            align: 'left'
        },
        category: 'interactive'
    },
    secondaryButton: {
        name: 'Secondary Button',
        icon: '⚪',
        html: '<div style="margin: 15px 0; text-align: {{align}};"><button style="background: transparent; color: {{textColor}}; border: 2px solid {{borderColor}}; padding: 10px 22px; border-radius: 6px; font-size: 16px; font-weight: 500; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background=\'{{hoverBg}}\'" onmouseout="this.style.background=\'transparent\'">{{text}}</button></div>',
        customFields: {
            text: 'Learn More',
            textColor: '#007bff',
            borderColor: '#007bff',
            hoverBg: '#f8f9fa',
            align: 'left'
        },
        category: 'interactive'
    },
    linkText: {
        name: 'Link Text',
        icon: '🔗',
        html: '<p style="margin: 10px 0;"><a href="{{url}}" style="color: {{color}}; text-decoration: {{decoration}}; font-weight: {{weight}};" onmouseover="this.style.textDecoration=\'underline\'" onmouseout="this.style.textDecoration=\'{{decoration}}\'">{{text}}</a></p>',
        customFields: {
            text: 'Click here to learn more',
            url: 'https://example.com',
            color: '#007bff',
            decoration: 'none',
            weight: 'normal'
        },
        category: 'interactive'
    },

    // Data Visualization Components
    progressBar: {
        name: 'Progress Bar',
        icon: '📊',
        html: '<div style="margin: 20px 0;"><div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;"><span style="font-weight: 500; color: #333;">{{label}}</span><span style="font-size: 14px; color: #666;">{{percentage}}%</span></div><div style="background: #e0e0e0; height: 8px; border-radius: 4px; overflow: hidden;"><div style="background: {{color}}; height: 100%; width: {{percentage}}%; transition: width 0.3s ease; border-radius: 4px;"></div></div></div>',
        customFields: {
            label: 'Progress',
            percentage: '75',
            color: '#28a745'
        },
        category: 'data'
    },
    chartPlaceholder: {
        name: 'Chart Placeholder',
        icon: '📈',
        html: '<div style="margin: 20px 0; padding: 30px; border: 2px dashed #ccc; border-radius: 8px; text-align: center; background: #f9f9f9;"><div style="font-size: 48px; margin-bottom: 15px; color: #999;">📈</div><h4 style="margin: 0 0 10px 0; color: #333;">{{title}}</h4><p style="margin: 0; color: #666; font-size: 14px;">{{description}}</p><div style="margin-top: 15px; font-size: 12px; color: #999;">Chart Type: {{chartType}}</div></div>',
        customFields: {
            title: 'Chart Title',
            description: 'Chart description and data visualization will be displayed here',
            chartType: 'Bar Chart'
        },
        category: 'data'
    },
    badge: {
        name: 'Badge',
        icon: '🏷️',
        html: '<span style="display: inline-block; background: {{bgColor}}; color: {{textColor}}; padding: 4px 12px; border-radius: 12px; font-size: 12px; font-weight: 500; margin: 2px;">{{text}}</span>',
        customFields: {
            text: 'Badge',
            bgColor: '#007bff',
            textColor: 'white'
        },
        category: 'data'
    },
    statusIndicator: {
        name: 'Status Indicator',
        icon: '🚦',
        html: '<div style="display: inline-flex; align-items: center; margin: 5px 0;"><div style="width: 12px; height: 12px; border-radius: 50%; background: {{color}}; margin-right: 8px;"></div><span style="font-size: 14px; color: #333;">{{text}}</span></div>',
        customFields: {
            text: 'Active',
            color: '#28a745'
        },
        category: 'data'
    },
    metricCard: {
        name: 'Metric Card',
        icon: '📊',
        html: '<div style="margin: 15px 0; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; background: {{bgColor}}; text-align: center;"><div style="font-size: 32px; font-weight: bold; color: {{valueColor}}; margin-bottom: 8px;">{{value}}</div><div style="font-size: 14px; color: #666; margin-bottom: 4px;">{{label}}</div><div style="font-size: 12px; color: {{changeColor}};">{{change}}</div></div>',
        customFields: {
            value: '1,234',
            label: 'Total Users',
            change: '+12% from last month',
            bgColor: '#f8f9fa',
            valueColor: '#333',
            changeColor: '#28a745'
        },
        category: 'data'
    },
    ratingStars: {
        name: 'Rating Stars',
        icon: '⭐',
        html: '<div style="margin: 15px 0; display: flex; align-items: center; gap: 8px;"><div style="display: flex; gap: 2px;">{{stars}}</div><span style="font-size: 14px; color: #666;">{{rating}} ({{reviews}} reviews)</span></div>',
        customFields: {
            stars: '⭐⭐⭐⭐⭐',
            rating: '4.8',
            reviews: '127'
        },
        category: 'data'
    }
};

// Pre-designed template sections
const TEMPLATE_SECTIONS = {
    dailyJournal: {
        name: 'Daily Journal',
        icon: '📔',
        elements: [
            { type: 'header1', text: 'Daily Journal' },
            { type: 'dateField' },
            { type: 'divider' },
            { type: 'header3', text: 'Today\'s Goals' },
            { type: 'bulletList', item1: 'Goal 1', item2: 'Goal 2', item3: 'Goal 3' },
            { type: 'header3', text: 'Reflection' },
            { type: 'userPrompt', label: 'How was your day?', question: 'Describe your day', defaultValue: 'It was...' }
        ]
    },
    meetingNotes: {
        name: 'Meeting Notes',
        icon: '👥',
        elements: [
            { type: 'header1', text: 'Meeting Notes' },
            { type: 'dateField' },
            { type: 'timeField' },
            { type: 'userPrompt', label: 'Meeting Title', question: 'Enter meeting title', defaultValue: 'Weekly Team Meeting' },
            { type: 'userPrompt', label: 'Attendees', question: 'Who attended?', defaultValue: 'List attendees' },
            { type: 'divider' },
            { type: 'header3', text: 'Agenda' },
            { type: 'numberedList', item1: 'Topic 1', item2: 'Topic 2', item3: 'Topic 3' },
            { type: 'header3', text: 'Action Items' },
            { type: 'bulletList', item1: 'Action 1', item2: 'Action 2', item3: 'Action 3' }
        ]
    },
    projectPlanning: {
        name: 'Project Planning',
        icon: '📊',
        elements: [
            { type: 'header1', text: 'Project Plan' },
            { type: 'userPrompt', label: 'Project Name', question: 'Enter project name', defaultValue: 'New Project' },
            { type: 'dateField' },
            { type: 'divider' },
            { type: 'header3', text: 'Objectives' },
            { type: 'userPrompt', label: 'Main Goal', question: 'What is the main objective?', defaultValue: 'Describe the goal' },
            { type: 'header3', text: 'Timeline' },
            { type: 'userPrompt', label: 'Start Date', question: 'When does this start?', defaultValue: 'MM/DD/YYYY' },
            { type: 'userPrompt', label: 'End Date', question: 'When should this be completed?', defaultValue: 'MM/DD/YYYY' },
            { type: 'header3', text: 'Key Milestones' },
            { type: 'numberedList', item1: 'Milestone 1', item2: 'Milestone 2', item3: 'Milestone 3' }
        ]
    },
    taskList: {
        name: 'Task List',
        icon: '✅',
        elements: [
            { type: 'header2', text: 'Task List' },
            { type: 'dateField' },
            { type: 'checkboxList', item1: 'Complete project proposal', item2: 'Review team feedback', item3: 'Schedule follow-up meeting' },
            { type: 'divider' },
            { type: 'header3', text: 'Priority Tasks' },
            { type: 'checkbox', text: 'High priority item' },
            { type: 'textArea', label: 'Additional Notes', placeholder: 'Add any additional context or notes here...' }
        ]
    },
    contactForm: {
        name: 'Contact Form',
        icon: '📋',
        elements: [
            { type: 'header2', text: 'Contact Information' },
            { type: 'userPrompt', label: 'Full Name', question: 'Enter your full name', defaultValue: 'John Doe' },
            { type: 'userPrompt', label: 'Email', question: 'Enter your email address', defaultValue: '<EMAIL>' },
            { type: 'userPrompt', label: 'Phone', question: 'Enter your phone number', defaultValue: '(*************' },
            { type: 'textArea', label: 'Message', placeholder: 'Enter your message here...' },
            { type: 'signatureField', label: 'Signature:' }
        ]
    },
    eventPlanning: {
        name: 'Event Planning',
        icon: '🎉',
        elements: [
            { type: 'header1', text: 'Event Planning' },
            { type: 'userPrompt', label: 'Event Name', question: 'What is the event called?', defaultValue: 'Annual Conference' },
            { type: 'dateInput', label: 'Event Date', question: 'When is the event?', defaultValue: 'MM/DD/YYYY' },
            { type: 'userPrompt', label: 'Location', question: 'Where will it be held?', defaultValue: 'Convention Center' },
            { type: 'divider' },
            { type: 'header3', text: 'Planning Checklist' },
            { type: 'checkboxList', item1: 'Book venue', item2: 'Send invitations', item3: 'Arrange catering' },
            { type: 'header3', text: 'Contact Information' },
            { type: 'contactInfo', name: 'Event Coordinator', email: '<EMAIL>', phone: '(*************' }
        ]
    },
    weeklyReport: {
        name: 'Weekly Report',
        icon: '📈',
        elements: [
            { type: 'header1', text: 'Weekly Report' },
            { type: 'dateField' },
            { type: 'userPrompt', label: 'Week Ending', question: 'Enter the week ending date', defaultValue: 'MM/DD/YYYY' },
            { type: 'divider' },
            { type: 'header3', text: 'Accomplishments' },
            { type: 'bulletList', item1: 'Completed project milestone', item2: 'Attended team meetings', item3: 'Reviewed quarterly goals' },
            { type: 'header3', text: 'Challenges' },
            { type: 'textArea', label: 'Challenges Faced', placeholder: 'Describe any challenges encountered this week...' },
            { type: 'header3', text: 'Next Week Goals' },
            { type: 'numberedList', item1: 'Priority goal 1', item2: 'Priority goal 2', item3: 'Priority goal 3' }
        ]
    },
    invoiceTemplate: {
        name: 'Invoice Template',
        icon: '💰',
        elements: [
            { type: 'header1', text: 'INVOICE' },
            { type: 'dateField' },
            { type: 'userPrompt', label: 'Invoice Number', question: 'Enter invoice number', defaultValue: 'INV-001' },
            { type: 'divider' },
            { type: 'header3', text: 'Bill To:' },
            { type: 'contactInfo', name: 'Client Name', email: '<EMAIL>', phone: '(*************' },
            { type: 'header3', text: 'Services' },
            { type: 'textArea', label: 'Description of Services', placeholder: 'List services provided...' },
            { type: 'userPrompt', label: 'Total Amount', question: 'Enter total amount due', defaultValue: '$0.00' },
            { type: 'userPrompt', label: 'Due Date', question: 'When is payment due?', defaultValue: 'MM/DD/YYYY' }
        ]
    },

    budgetPlanner: {
        name: 'Budget Planner',
        icon: '💸',
        elements: [
            { type: 'header1', text: 'Budget Planner' },
            { type: 'dateField' },
            { type: 'userPrompt', label: 'Budget Period', question: 'Enter budget period', defaultValue: 'Monthly' },
            { type: 'divider' },
            { type: 'header3', text: 'Income' },
            { type: 'dataTable', title: 'Income Sources' },
            { type: 'header3', text: 'Expenses' },
            { type: 'comparisonTable', title: 'Expense Categories' },
            { type: 'header3', text: 'Summary' },
            { type: 'userPrompt', label: 'Total Income', question: 'Enter total income', defaultValue: '$0.00' },
            { type: 'userPrompt', label: 'Total Expenses', question: 'Enter total expenses', defaultValue: '$0.00' },
            { type: 'userPrompt', label: 'Net Balance', question: 'Calculate net balance', defaultValue: '$0.00' }
        ]
    },

    classSchedule: {
        name: 'Class Schedule',
        icon: '🎓',
        elements: [
            { type: 'header1', text: 'Class Schedule' },
            { type: 'userPrompt', label: 'Semester', question: 'Enter semester', defaultValue: 'Fall 2024' },
            { type: 'userPrompt', label: 'Student Name', question: 'Enter student name', defaultValue: 'Student Name' },
            { type: 'divider' },
            { type: 'scheduleTable', title: 'Weekly Class Schedule' },
            { type: 'header3', text: 'Important Dates' },
            { type: 'bulletList', item1: 'Midterm Exams: TBD', item2: 'Final Exams: TBD', item3: 'Holidays: TBD' },
            { type: 'header3', text: 'Contact Information' },
            { type: 'contactInfo', name: 'Academic Advisor', email: '<EMAIL>', phone: '(*************' }
        ]
    },

    productComparison: {
        name: 'Product Comparison',
        icon: '🔍',
        elements: [
            { type: 'header1', text: 'Product Comparison' },
            { type: 'dateField' },
            { type: 'userPrompt', label: 'Comparison Category', question: 'What are you comparing?', defaultValue: 'Software Plans' },
            { type: 'divider' },
            { type: 'comparisonTable', title: 'Feature Comparison' },
            { type: 'header3', text: 'Recommendations' },
            { type: 'textArea', label: 'Best Choice', placeholder: 'Based on the comparison, which option is recommended and why?' },
            { type: 'header3', text: 'Additional Notes' },
            { type: 'bulletList', item1: 'Consider your budget', item2: 'Think about future needs', item3: 'Check customer reviews' }
        ]
    },

    dataAnalysis: {
        name: 'Data Analysis Report',
        icon: '📊',
        elements: [
            { type: 'header1', text: 'Data Analysis Report' },
            { type: 'dateField' },
            { type: 'userPrompt', label: 'Report Title', question: 'Enter report title', defaultValue: 'Monthly Data Analysis' },
            { type: 'divider' },
            { type: 'header3', text: 'Executive Summary' },
            { type: 'textArea', label: 'Key Findings', placeholder: 'Summarize the main insights from your data analysis...' },
            { type: 'header3', text: 'Data Overview' },
            { type: 'dataTable', title: 'Key Metrics' },
            { type: 'header3', text: 'Detailed Analysis' },
            { type: 'simpleTable', title: 'Detailed Breakdown' },
            { type: 'header3', text: 'Conclusions' },
            { type: 'numberedList', item1: 'Primary conclusion', item2: 'Secondary finding', item3: 'Recommendation' }
        ]
    },

    inventoryTracker: {
        name: 'Inventory Tracker',
        icon: '📦',
        elements: [
            { type: 'header1', text: 'Inventory Tracker' },
            { type: 'dateField' },
            { type: 'userPrompt', label: 'Location', question: 'Enter inventory location', defaultValue: 'Warehouse A' },
            { type: 'divider' },
            { type: 'header3', text: 'Current Inventory' },
            { type: 'dataTable', title: 'Inventory Status' },
            { type: 'header3', text: 'Low Stock Alerts' },
            { type: 'checkboxList', item1: 'Item A - Reorder needed', item2: 'Item B - Critical level', item3: 'Item C - Monitor closely' },
            { type: 'header3', text: 'Notes' },
            { type: 'textArea', label: 'Additional Notes', placeholder: 'Any special notes about inventory status...' }
        ]
    },

    performanceReview: {
        name: 'Performance Review',
        icon: '⭐',
        elements: [
            { type: 'header1', text: 'Performance Review' },
            { type: 'dateField' },
            { type: 'userPrompt', label: 'Employee Name', question: 'Enter employee name', defaultValue: 'Employee Name' },
            { type: 'userPrompt', label: 'Review Period', question: 'Enter review period', defaultValue: 'Q4 2024' },
            { type: 'divider' },
            { type: 'header3', text: 'Performance Metrics' },
            { type: 'comparisonTable', title: 'Goals vs. Achievements' },
            { type: 'header3', text: 'Strengths' },
            { type: 'bulletList', item1: 'Strong communication skills', item2: 'Excellent problem-solving', item3: 'Team collaboration' },
            { type: 'header3', text: 'Areas for Improvement' },
            { type: 'numberedList', item1: 'Time management', item2: 'Technical skills', item3: 'Leadership development' },
            { type: 'header3', text: 'Goals for Next Period' },
            { type: 'checkboxList', item1: 'Complete training program', item2: 'Lead a project', item3: 'Improve efficiency by 15%' }
        ]
    },

    // Healthcare Templates
    patientIntake: {
        name: 'Patient Intake Form',
        icon: '🏥',
        elements: [
            { type: 'header1', text: 'Patient Intake Form' },
            { type: 'dateField' },
            { type: 'divider' },
            { type: 'header3', text: 'Patient Information' },
            { type: 'userPrompt', label: 'Patient Name', question: 'Enter patient full name', defaultValue: 'Patient Name' },
            { type: 'userPrompt', label: 'Date of Birth', question: 'Enter date of birth', defaultValue: 'MM/DD/YYYY' },
            { type: 'userPrompt', label: 'Phone Number', question: 'Enter phone number', defaultValue: '(*************' },
            { type: 'userPrompt', label: 'Emergency Contact', question: 'Enter emergency contact', defaultValue: 'Contact Name & Phone' },
            { type: 'header3', text: 'Medical History' },
            { type: 'checkboxList', item1: 'Diabetes', item2: 'Hypertension', item3: 'Heart Disease', item4: 'Allergies', item5: 'Other conditions' },
            { type: 'header3', text: 'Current Medications' },
            { type: 'dynamicTable', title: 'Medications List' },
            { type: 'header3', text: 'Reason for Visit' },
            { type: 'textArea', label: 'Chief Complaint', placeholder: 'Describe the main reason for today\'s visit...' }
        ]
    },

    medicalReport: {
        name: 'Medical Report',
        icon: '📋',
        elements: [
            { type: 'header1', text: 'Medical Report' },
            { type: 'dateField' },
            { type: 'userPrompt', label: 'Patient Name', question: 'Enter patient name', defaultValue: 'Patient Name' },
            { type: 'userPrompt', label: 'Medical Record Number', question: 'Enter MRN', defaultValue: 'MRN-000000' },
            { type: 'divider' },
            { type: 'header3', text: 'Vital Signs' },
            { type: 'dataTable', title: 'Current Vital Signs' },
            { type: 'header3', text: 'Assessment' },
            { type: 'textArea', label: 'Clinical Assessment', placeholder: 'Enter clinical assessment and findings...' },
            { type: 'header3', text: 'Treatment Plan' },
            { type: 'numberedList', item1: 'Prescribed medication', item2: 'Follow-up appointment', item3: 'Lifestyle recommendations' },
            { type: 'header3', text: 'Provider Information' },
            { type: 'contactInfo', name: 'Dr. Provider Name', email: '<EMAIL>', phone: '(*************' }
        ]
    },

    // Legal Templates
    contractTemplate: {
        name: 'Contract Template',
        icon: '📜',
        elements: [
            { type: 'header1', text: 'CONTRACT AGREEMENT' },
            { type: 'dateField' },
            { type: 'divider' },
            { type: 'header3', text: 'Parties' },
            { type: 'userPrompt', label: 'Party A', question: 'Enter first party name', defaultValue: 'Company/Individual A' },
            { type: 'userPrompt', label: 'Party B', question: 'Enter second party name', defaultValue: 'Company/Individual B' },
            { type: 'header3', text: 'Terms and Conditions' },
            { type: 'numberedList', item1: 'Scope of work/services', item2: 'Payment terms and schedule', item3: 'Duration and termination', item4: 'Confidentiality requirements', item5: 'Dispute resolution' },
            { type: 'header3', text: 'Payment Schedule' },
            { type: 'comparisonTable', title: 'Payment Breakdown' },
            { type: 'header3', text: 'Signatures' },
            { type: 'signatureField', label: 'Party A Signature' },
            { type: 'signatureField', label: 'Party B Signature' },
            { type: 'userPrompt', label: 'Witness', question: 'Enter witness name (if required)', defaultValue: 'Witness Name' }
        ]
    },

    // Marketing Templates
    campaignPlan: {
        name: 'Marketing Campaign Plan',
        icon: '📢',
        elements: [
            { type: 'header1', text: 'Marketing Campaign Plan' },
            { type: 'dateField' },
            { type: 'userPrompt', label: 'Campaign Name', question: 'Enter campaign name', defaultValue: 'Campaign Title' },
            { type: 'divider' },
            { type: 'header3', text: 'Campaign Overview' },
            { type: 'textArea', label: 'Campaign Objectives', placeholder: 'Define the main goals and objectives...' },
            { type: 'userPrompt', label: 'Target Audience', question: 'Describe target audience', defaultValue: 'Demographics and characteristics' },
            { type: 'header3', text: 'Campaign Timeline' },
            { type: 'scheduleTable', title: 'Campaign Schedule' },
            { type: 'header3', text: 'Budget Allocation' },
            { type: 'dataTable', title: 'Budget Breakdown' },
            { type: 'header3', text: 'Success Metrics' },
            { type: 'checkboxList', item1: 'Reach and impressions', item2: 'Engagement rate', item3: 'Conversion rate', item4: 'ROI measurement', item5: 'Brand awareness' }
        ]
    }
};

/**
 * Open the visual template builder
 */
function openVisualTemplateBuilder() {
    if (visualBuilderModal) {
        visualBuilderModal.style.display = 'flex';

        // Initialize session if none exists
        if (!currentSessionId) {
            const sessionId = createTemplateSession();
            switchTemplateSession(sessionId);
        }

        return;
    }

    createVisualBuilderModal();
    visualBuilderModal.style.display = 'flex';

    // Initialize first session
    const sessionId = createTemplateSession();
    switchTemplateSession(sessionId);
}

/**
 * Close the visual template builder
 */
function closeVisualTemplateBuilder() {
    if (visualBuilderModal) {
        visualBuilderModal.style.display = 'none';
    }
}

/**
 * Create the visual template builder modal
 */
function createVisualBuilderModal() {
    visualBuilderModal = document.createElement('div');
    visualBuilderModal.id = 'Stashy-visual-builder-modal';
    visualBuilderModal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 2147483649;
        display: none;
        justify-content: center;
        align-items: center;
        font-family: Arial, sans-serif;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: white;
        border-radius: 12px;
        width: 95vw;
        height: 90vh;
        display: flex;
        flex-direction: column;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        overflow: hidden;
    `;

    // Create header
    const header = createBuilderHeader();

    // Create main content area
    const mainContent = createBuilderMainContent();

    // Create footer
    const footer = createBuilderFooter();

    modalContent.appendChild(header);
    modalContent.appendChild(mainContent);
    modalContent.appendChild(footer);
    visualBuilderModal.appendChild(modalContent);

    // Add event listeners
    setupBuilderEventListeners();

    // Setup keyboard shortcuts
    setupKeyboardShortcuts();

    // Setup template management event listeners
    setupTemplateManagementEvents();

    // Close on backdrop click
    visualBuilderModal.addEventListener('click', (e) => {
        if (e.target === visualBuilderModal) {
            closeVisualTemplateBuilder();
        }
    });

    document.body.appendChild(visualBuilderModal);

    // Initialize with empty template
    resetTemplate();

    // Initialize manage tab templates
    setTimeout(() => {
        loadTemplatesInBuilder();
    }, 100);

    // Ensure components tab is shown initially
    setTimeout(() => {
        switchBuilderTab('components');
    }, 150);

    // Show onboarding for first-time users
    if (isFirstTimeUser) {
        setTimeout(showOnboardingTour, 500);
    }

    // Start auto-save
    startAutoSave();
}

/**
 * Create the builder header
 */
function createBuilderHeader() {
    const header = document.createElement('div');
    header.style.cssText = `
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 16px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-weight: bold;
        font-size: 18px;
    `;

    header.innerHTML = `
        <div style="display: flex; align-items: center; gap: 12px;">
            <span>🎨 Template Builder</span>
            <div style="font-size: 12px; font-weight: normal; background: rgba(255,255,255,0.2); padding: 4px 8px; border-radius: 12px;">
                No HTML Required
            </div>
        </div>
        <div style="display: flex; align-items: center; gap: 8px;">
            <button id="visual-builder-help" style="
                background: rgba(255, 255, 255, 0.2);
                border: none;
                color: white;
                font-size: 14px;
                cursor: pointer;
                padding: 6px 12px;
                border-radius: 6px;
            ">❓ Help</button>
            <button id="visual-builder-close" style="
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            ">×</button>
        </div>
    `;

    return header;
}

/**
 * Create the main content area with three panels
 */
function createBuilderMainContent() {
    const mainContent = document.createElement('div');
    mainContent.style.cssText = `
        flex: 1;
        display: flex;
        overflow: hidden;
    `;

    // Left panel - Component library
    const leftPanel = createComponentLibrary();

    // Center panel - Canvas
    const centerPanel = createBuilderCanvas();

    // Right panel - Preview
    const rightPanel = createBuilderPreview();

    mainContent.appendChild(leftPanel);
    mainContent.appendChild(centerPanel);
    mainContent.appendChild(rightPanel);

    return mainContent;
}

/**
 * Create the component library panel
 */
function createComponentLibrary() {
    const panel = document.createElement('div');
    panel.style.cssText = `
        width: 280px;
        background: #f8f9fa;
        border-right: 1px solid #e0e0e0;
        display: flex;
        flex-direction: column;
        overflow-y: auto;
    `;

    // Panel header
    const header = document.createElement('div');
    header.style.cssText = `
        padding: 16px;
        background: white;
        border-bottom: 1px solid #e0e0e0;
        font-weight: bold;
        color: #333;
    `;
    // Create header content programmatically
    const headerTop = document.createElement('div');
    headerTop.style.cssText = 'display: flex; align-items: center; justify-content: space-between; margin-bottom: 12px;';

    const headerTitle = document.createElement('div');
    headerTitle.style.cssText = 'display: flex; align-items: center; gap: 8px;';
    headerTitle.textContent = '🧩 Template Builder';

    const switchBtn = document.createElement('button');
    switchBtn.id = 'switch-template-btn';
    switchBtn.style.cssText = 'background: #667eea; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer;';
    switchBtn.title = 'Switch Template';
    switchBtn.textContent = '🔄 Switch';

    headerTop.appendChild(headerTitle);
    headerTop.appendChild(switchBtn);

    const sessionIndicator = document.createElement('div');
    sessionIndicator.id = 'session-indicator';
    sessionIndicator.style.cssText = 'font-size: 12px; color: #666; margin-bottom: 8px; display: none;';

    const tabContainer = document.createElement('div');
    tabContainer.style.cssText = 'display: flex; gap: 2px;';

    // Create tab buttons programmatically
    const componentsBtn = document.createElement('button');
    componentsBtn.className = 'component-tab active';
    componentsBtn.setAttribute('data-tab', 'components');
    componentsBtn.textContent = 'Components';

    const sectionsBtn = document.createElement('button');
    sectionsBtn.className = 'component-tab';
    sectionsBtn.setAttribute('data-tab', 'sections');
    sectionsBtn.textContent = 'Sections';

    const manageBtn = document.createElement('button');
    manageBtn.className = 'component-tab';
    manageBtn.setAttribute('data-tab', 'manage');
    manageBtn.textContent = 'Manage';

    // Add direct event listeners to tab buttons
    componentsBtn.addEventListener('click', () => switchBuilderTab('components'));
    sectionsBtn.addEventListener('click', () => switchBuilderTab('sections'));
    manageBtn.addEventListener('click', () => switchBuilderTab('manage'));

    tabContainer.appendChild(componentsBtn);
    tabContainer.appendChild(sectionsBtn);
    tabContainer.appendChild(manageBtn);

    header.appendChild(headerTop);
    header.appendChild(sessionIndicator);
    header.appendChild(tabContainer);

    // Panel content
    const content = document.createElement('div');
    content.style.cssText = `
        flex: 1;
        padding: 16px;
    `;

    // Components tab
    const componentsTab = createComponentsTab();
    componentsTab.id = 'components-tab';

    // Sections tab
    const sectionsTab = createSectionsTab();
    sectionsTab.id = 'sections-tab';
    sectionsTab.style.display = 'none';

    // Template Management tab
    const manageTab = createTemplateManagementTab();
    manageTab.id = 'manage-tab';
    manageTab.style.display = 'none';

    content.appendChild(componentsTab);
    content.appendChild(sectionsTab);
    content.appendChild(manageTab);

    panel.appendChild(header);
    panel.appendChild(content);

    return panel;
}

/**
 * Create the components tab content
 */
function createComponentsTab() {
    const tab = document.createElement('div');

    // Group components by category
    const categories = {
        headers: 'Headers',
        text: 'Text & Formatting',
        forms: 'Form Elements',
        media: 'Media & Content',
        layout: 'Layout & Structure',
        interactive: 'Interactive Elements',
        data: 'Data & Metrics',
        lists: 'Lists',
        tables: 'Tables',
        smart: 'Smart Fields',
        business: 'Business'
    };

    Object.entries(categories).forEach(([categoryKey, categoryName]) => {
        const categorySection = document.createElement('div');
        categorySection.style.marginBottom = '20px';

        const categoryHeader = document.createElement('div');
        categoryHeader.style.cssText = `
            font-size: 12px;
            font-weight: bold;
            color: #666;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        `;
        categoryHeader.textContent = categoryName;

        const categoryItems = document.createElement('div');
        categoryItems.style.cssText = `
            display: flex;
            flex-direction: column;
            gap: 6px;
        `;

        // Add components for this category
        Object.entries(TEMPLATE_COMPONENTS).forEach(([key, component]) => {
            if (component.category === categoryKey) {
                const componentItem = createComponentItem(key, component);
                categoryItems.appendChild(componentItem);
            }
        });

        categorySection.appendChild(categoryHeader);
        categorySection.appendChild(categoryItems);
        tab.appendChild(categorySection);
    });

    return tab;
}

/**
 * Create a draggable component item
 */
function createComponentItem(key, component) {
    const item = document.createElement('div');
    item.className = 'component-item';
    item.draggable = true;
    item.dataset.componentType = key;
    item.style.cssText = `
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        padding: 10px;
        cursor: grab;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    `;

    item.innerHTML = `
        <span style="font-size: 16px;">${component.icon}</span>
        <span style="font-size: 13px; font-weight: 500;">${component.name}</span>
    `;

    // Add hover effects
    item.addEventListener('mouseenter', () => {
        item.style.borderColor = '#4285f4';
        item.style.boxShadow = '0 2px 8px rgba(66, 133, 244, 0.2)';
    });

    item.addEventListener('mouseleave', () => {
        item.style.borderColor = '#e0e0e0';
        item.style.boxShadow = 'none';
    });

    return item;
}

/**
 * Create the sections tab content
 */
function createSectionsTab() {
    const tab = document.createElement('div');

    const header = document.createElement('div');
    header.style.cssText = `
        font-size: 12px;
        color: #666;
        margin-bottom: 12px;
        line-height: 1.4;
    `;
    header.textContent = 'Pre-designed template sections you can add with one click:';

    const sectionsContainer = document.createElement('div');
    sectionsContainer.style.cssText = `
        display: flex;
        flex-direction: column;
        gap: 8px;
    `;

    Object.entries(TEMPLATE_SECTIONS).forEach(([key, section]) => {
        const sectionItem = document.createElement('div');
        sectionItem.className = 'section-item';
        sectionItem.dataset.sectionType = key;
        sectionItem.style.cssText = `
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        `;

        sectionItem.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                <span style="font-size: 16px;">${section.icon}</span>
                <span style="font-size: 13px; font-weight: 600;">${section.name}</span>
            </div>
            <div style="font-size: 11px; color: #666;">
                ${section.elements.length} components
            </div>
        `;

        // Add hover effects
        sectionItem.addEventListener('mouseenter', () => {
            sectionItem.style.borderColor = '#34a853';
            sectionItem.style.boxShadow = '0 2px 8px rgba(52, 168, 83, 0.2)';
        });

        sectionItem.addEventListener('mouseleave', () => {
            sectionItem.style.borderColor = '#e0e0e0';
            sectionItem.style.boxShadow = 'none';
        });

        sectionsContainer.appendChild(sectionItem);
    });

    tab.appendChild(header);
    tab.appendChild(sectionsContainer);

    return tab;
}

/**
 * Create the template management tab content
 */
function createTemplateManagementTab() {
    const tab = document.createElement('div');

    // Search and filter section
    const searchSection = document.createElement('div');
    searchSection.style.cssText = `
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #e0e0e0;
    `;

    searchSection.innerHTML = `
        <input type="text" id="template-search-builder" placeholder="Search templates..." style="
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 12px;
            margin-bottom: 8px;
        ">
        <select id="template-category-filter-builder" style="
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 12px;
        ">
            <option value="">All Categories</option>
            <option value="general">General</option>
            <option value="work">Work</option>
            <option value="study">Study</option>
            <option value="personal">Personal</option>
            <option value="project">Project</option>
        </select>
    `;

    // Template list section
    const templateListSection = document.createElement('div');
    templateListSection.style.cssText = `
        flex: 1;
        overflow-y: auto;
        max-height: 400px;
    `;

    const templateList = document.createElement('div');
    templateList.id = 'template-list-builder';
    templateList.style.cssText = `
        display: flex;
        flex-direction: column;
        gap: 8px;
    `;

    templateListSection.appendChild(templateList);

    // Action buttons section
    const actionsSection = document.createElement('div');
    actionsSection.style.cssText = `
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #e0e0e0;
        display: flex;
        flex-direction: column;
        gap: 8px;
    `;

    actionsSection.innerHTML = `
        <button id="edit-template-btn-builder" style="
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            width: 100%;
        " disabled>✏️ Edit Selected</button>
        <button id="rename-template-btn-builder" style="
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            width: 100%;
        " disabled>📝 Rename</button>
        <button id="backup-template-btn-builder" style="
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            width: 100%;
        " disabled>📥 Backup</button>
        <button id="delete-template-btn-builder" style="
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            width: 100%;
        " disabled>🗑️ Delete</button>
    `;

    tab.appendChild(searchSection);
    tab.appendChild(templateListSection);
    tab.appendChild(actionsSection);

    return tab;
}

/**
 * Create the builder canvas (center panel)
 */
function createBuilderCanvas() {
    const panel = document.createElement('div');
    panel.style.cssText = `
        flex: 1;
        background: white;
        border-right: 1px solid #e0e0e0;
        display: flex;
        flex-direction: column;
    `;

    // Canvas header
    const header = document.createElement('div');
    header.style.cssText = `
        padding: 16px;
        background: #f8f9fa;
        border-bottom: 1px solid #e0e0e0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    `;

    header.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <span style="font-weight: bold; color: #333;">🎯 Template Builder</span>
            <span style="font-size: 12px; color: #666;">Drag components here</span>
        </div>
        <div style="display: flex; gap: 8px;">
            <button id="clear-canvas" style="
                background: #dc3545;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            ">🗑️ Clear</button>
            <button id="undo-action" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
            ">↶ Undo</button>
        </div>
    `;

    // Canvas area
    visualBuilderCanvas = document.createElement('div');
    visualBuilderCanvas.id = 'visual-builder-canvas';
    visualBuilderCanvas.style.cssText = `
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        background: #fafafa;
        position: relative;
    `;

    // Drop zone
    const dropZone = document.createElement('div');
    dropZone.id = 'canvas-drop-zone';
    dropZone.style.cssText = `
        min-height: 400px;
        border: 2px dashed #ddd;
        border-radius: 8px;
        background: white;
        padding: 20px;
        position: relative;
    `;

    // Empty state
    const emptyState = document.createElement('div');
    emptyState.id = 'canvas-empty-state';
    emptyState.style.cssText = `
        text-align: center;
        color: #999;
        padding: 60px 20px;
        font-size: 16px;
    `;
    emptyState.innerHTML = `
        <div style="font-size: 48px; margin-bottom: 16px;">📝</div>
        <div style="font-weight: bold; margin-bottom: 8px;">Start Building Your Template</div>
        <div style="font-size: 14px;">Drag components from the left panel or click a pre-designed section</div>
    `;

    dropZone.appendChild(emptyState);
    visualBuilderCanvas.appendChild(dropZone);

    panel.appendChild(header);
    panel.appendChild(visualBuilderCanvas);

    return panel;
}

/**
 * Create the preview panel (right panel)
 */
function createBuilderPreview() {
    const panel = document.createElement('div');
    panel.style.cssText = `
        width: 350px;
        background: #f8f9fa;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    `;

    // Preview header
    const header = document.createElement('div');
    header.style.cssText = `
        padding: 16px;
        background: white;
        border-bottom: 1px solid #e0e0e0;
        font-weight: bold;
        color: #333;
    `;
    header.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px;">
            👁️ Live Preview
        </div>
        <div style="font-size: 12px; font-weight: normal; color: #666;">
            See how your template will look
        </div>
    `;

    // Preview content
    visualBuilderPreview = document.createElement('div');
    visualBuilderPreview.id = 'visual-builder-preview';
    visualBuilderPreview.style.cssText = `
        flex: 1;
        padding: 16px;
        overflow-y: auto;
        background: white;
        margin: 16px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
    `;

    // Preview empty state
    const previewEmpty = document.createElement('div');
    previewEmpty.id = 'preview-empty-state';
    previewEmpty.style.cssText = `
        text-align: center;
        color: #999;
        padding: 40px 20px;
        font-size: 14px;
    `;
    previewEmpty.innerHTML = `
        <div style="font-size: 32px; margin-bottom: 12px;">👁️</div>
        <div>Preview will appear here as you build your template</div>
    `;

    visualBuilderPreview.appendChild(previewEmpty);

    panel.appendChild(header);
    panel.appendChild(visualBuilderPreview);

    return panel;
}

/**
 * Create the builder footer
 */
function createBuilderFooter() {
    const footer = document.createElement('div');
    footer.style.cssText = `
        padding: 16px 20px;
        background: #f8f9fa;
        border-top: 1px solid #e0e0e0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    `;

    footer.innerHTML = `
        <div style="display: flex; align-items: center; gap: 16px;">
            <input type="text" id="template-name-input" placeholder="Enter template name..." style="
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                width: 200px;
            ">
            <select id="template-category-select" style="
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
            ">
                <option value="custom">Custom</option>
                <option value="general">General</option>
                <option value="work">Work</option>
                <option value="study">Study</option>
                <option value="personal">Personal</option>
                <option value="project">Project</option>
                <option value="meeting">Meeting</option>
            </select>
        </div>
        <div style="display: flex; gap: 8px;">
            <button id="preview-template" style="
                background: #17a2b8;
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
            ">👁️ Test Template</button>
            <button id="save-visual-template" style="
                background: #28a745;
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                font-weight: bold;
            ">💾 Save Template</button>
        </div>
    `;

    return footer;
}

/**
 * Setup event listeners for the visual builder
 */
function setupBuilderEventListeners() {
    // Close button
    document.addEventListener('click', (e) => {
        if (e.target.id === 'visual-builder-close') {
            closeVisualTemplateBuilder();
        }
    });

    // Help button
    document.addEventListener('click', (e) => {
        if (e.target.id === 'visual-builder-help') {
            showVisualBuilderHelp();
        }
    });

    // Tab switching (backup event delegation)
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('component-tab')) {
            const tabName = e.target.dataset.tab;
            switchBuilderTab(tabName);
        }
    });

    // Template switching
    document.addEventListener('click', (e) => {
        if (e.target.id === 'switch-template-btn') {
            openTemplateSwitcher();
        }
    });

    // Section items click
    document.addEventListener('click', (e) => {
        if (e.target.closest('.section-item')) {
            const sectionType = e.target.closest('.section-item').dataset.sectionType;
            addTemplateSection(sectionType);
        }
    });

    // Canvas actions
    document.addEventListener('click', (e) => {
        if (e.target.id === 'clear-canvas') {
            clearCanvas();
        } else if (e.target.id === 'undo-action') {
            undoLastAction();
        }
    });

    // Footer actions
    document.addEventListener('click', (e) => {
        if (e.target.id === 'save-visual-template') {
            saveVisualTemplate();
        } else if (e.target.id === 'preview-template') {
            previewTemplate();
        }
    });

    // Drag and drop
    setupDragAndDrop();
}

/**
 * Setup drag and drop functionality
 */
function setupDragAndDrop() {
    // Component drag start
    document.addEventListener('dragstart', (e) => {
        if (e.target.classList.contains('component-item')) {
            draggedElement = {
                type: 'component',
                componentType: e.target.dataset.componentType
            };
            e.target.style.opacity = '0.5';
        }
    });

    // Component drag end
    document.addEventListener('dragend', (e) => {
        if (e.target.classList.contains('component-item')) {
            e.target.style.opacity = '1';
            draggedElement = null;
        }
    });

    // Canvas drop zone events
    const setupDropZone = () => {
        const dropZone = document.getElementById('canvas-drop-zone');
        if (!dropZone) return;

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#4285f4';
            dropZone.style.backgroundColor = '#f0f7ff';
        });

        dropZone.addEventListener('dragleave', (e) => {
            if (!dropZone.contains(e.relatedTarget)) {
                dropZone.style.borderColor = '#ddd';
                dropZone.style.backgroundColor = 'white';
            }
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#ddd';
            dropZone.style.backgroundColor = 'white';

            if (draggedElement && draggedElement.type === 'component') {
                addComponentToCanvas(draggedElement.componentType);
            }
        });
    };

    // Setup drop zone when modal is created
    setTimeout(setupDropZone, 100);
}

/**
 * Switch between component tabs in the template builder
 */
function switchBuilderTab(tabName) {

    // Update tab buttons
    document.querySelectorAll('.component-tab').forEach(tab => {
        tab.classList.remove('active');
        if (tab.dataset.tab === tabName) {
            tab.classList.add('active');
        }
    });

    // Show/hide tab content with retry mechanism
    const switchTabContent = () => {
        // Try multiple ways to find the elements
        let componentsTab = document.getElementById('components-tab');
        let sectionsTab = document.getElementById('sections-tab');
        let manageTab = document.getElementById('manage-tab');

        // If not found by ID, try finding within the modal
        if (!componentsTab || !sectionsTab || !manageTab) {
            const modal = document.getElementById('Stashy-visual-builder-modal');
            if (modal) {
                componentsTab = modal.querySelector('#components-tab');
                sectionsTab = modal.querySelector('#sections-tab');
                manageTab = modal.querySelector('#manage-tab');
            }
        }



        // Check if elements exist before manipulating them
        if (!componentsTab || !sectionsTab || !manageTab) {
            console.error('One or more tab content elements not found, retrying...');
            // Retry after a short delay, but limit retries
            if (!switchTabContent.retryCount) switchTabContent.retryCount = 0;
            if (switchTabContent.retryCount < 5) {
                switchTabContent.retryCount++;
                setTimeout(switchTabContent, 200);
            } else {
                console.error('Failed to find tab elements after 5 retries');
            }
            return;
        }

        // Reset retry count on success
        switchTabContent.retryCount = 0;

        // Hide all tabs first
        componentsTab.style.display = 'none';
        sectionsTab.style.display = 'none';
        manageTab.style.display = 'none';

        // Show selected tab
        if (tabName === 'components') {
            componentsTab.style.display = 'block';
        } else if (tabName === 'sections') {
            sectionsTab.style.display = 'block';
        } else if (tabName === 'manage') {
            manageTab.style.display = 'block';
            // Load templates when manage tab is opened
            setTimeout(() => {
                loadTemplatesInBuilder();
            }, 50);
        }
    };

    // Try immediately, then retry if needed
    switchTabContent();
}

/**
 * Add a component to the canvas
 */
function addComponentToCanvas(componentType) {
    const component = TEMPLATE_COMPONENTS[componentType];
    if (!component) return;

    const elementId = `element-${++elementCounter}`;
    const element = {
        id: elementId,
        type: componentType,
        component: component,
        data: {}
    };

    // Set default data
    if (component.editable && component.defaultText) {
        element.data.text = component.defaultText;
    }

    if (component.customFields) {
        Object.entries(component.customFields).forEach(([key, defaultValue]) => {
            element.data[key] = defaultValue;
        });
    }

    // Add to template
    currentTemplate.elements.push(element);

    // Save to history for undo/redo
    saveToHistory();

    // Hide empty state
    const emptyState = document.getElementById('canvas-empty-state');
    if (emptyState) {
        emptyState.style.display = 'none';
    }

    // Render element on canvas
    renderCanvasElement(element);

    // Update preview
    updatePreview();
}

/**
 * Add a pre-designed template section
 */
function addTemplateSection(sectionType) {
    const section = TEMPLATE_SECTIONS[sectionType];
    if (!section) return;

    // Add each element in the section
    section.elements.forEach(elementData => {
        const elementId = `element-${++elementCounter}`;
        const component = TEMPLATE_COMPONENTS[elementData.type];

        const element = {
            id: elementId,
            type: elementData.type,
            component: component,
            data: { ...elementData }
        };

        // Remove type from data since it's stored separately
        delete element.data.type;

        currentTemplate.elements.push(element);
    });

    // Hide empty state
    const emptyState = document.getElementById('canvas-empty-state');
    if (emptyState) {
        emptyState.style.display = 'none';
    }

    // Re-render entire canvas
    renderCanvas();

    // Update preview
    updatePreview();

    // Show success message
    showNotification(`Added ${section.name} section with ${section.elements.length} components`, 'success');
}

/**
 * Render a single element on the canvas
 */
function renderCanvasElement(element) {
    const dropZone = document.getElementById('canvas-drop-zone');
    if (!dropZone) return;

    const elementDiv = document.createElement('div');
    elementDiv.className = 'canvas-element';
    elementDiv.dataset.elementId = element.id;
    elementDiv.style.cssText = `
        margin: 8px 0;
        padding: 12px;
        border: 1px solid #e0e0e0;
        border-radius: 6px;
        background: white;
        position: relative;
        transition: all 0.2s ease;
    `;

    // Element content
    const content = document.createElement('div');
    content.style.cssText = `
        margin-bottom: 8px;
    `;

    // Render based on component type
    content.innerHTML = renderElementContent(element);

    // Element controls
    const controls = document.createElement('div');
    controls.className = 'element-controls';
    controls.style.cssText = `
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 8px;
        border-top: 1px solid #f0f0f0;
        font-size: 12px;
        color: #666;
    `;

    controls.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <span>${element.component.icon} ${element.component.name}</span>
            ${element.component.editable ? '<span style="color: #4285f4;">✏️ Editable</span>' : ''}
        </div>
        <div style="display: flex; gap: 4px;">
            <button class="edit-element-btn" data-element-id="${element.id}" style="
                background: #4285f4;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 11px;
            ">Edit</button>
            <button class="move-up-btn" data-element-id="${element.id}" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 4px 6px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 11px;
            ">↑</button>
            <button class="move-down-btn" data-element-id="${element.id}" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 4px 6px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 11px;
            ">↓</button>
            <button class="delete-element-btn" data-element-id="${element.id}" style="
                background: #dc3545;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 11px;
            ">🗑️</button>
        </div>
    `;

    elementDiv.appendChild(content);
    elementDiv.appendChild(controls);

    // Add event listeners for controls
    setupElementControls(elementDiv, element);

    dropZone.appendChild(elementDiv);
}

/**
 * Render the content of an element
 */
function renderElementContent(element) {
    // Safety check for component and component.html
    if (!element.component || !element.component.html) {
        console.warn('Element missing component or component.html:', element);
        // Fallback to element content or data
        if (element.content) {
            return element.content;
        } else if (element.data && element.data.text) {
            return element.data.text;
        } else {
            return '<p>Invalid element</p>';
        }
    }

    let html = element.component.html;

    // Replace placeholders with actual data
    if (element.data && element.data.text) {
        html = html.replace(/\{\{text\}\}/g, element.data.text);
    }

    // Replace custom fields
    if (element.component.customFields && element.data) {
        Object.keys(element.component.customFields).forEach(field => {
            const value = element.data[field] || element.component.customFields[field];
            const regex = new RegExp(`\\{\\{${field}\\}\\}`, 'g');
            html = html.replace(regex, value);
        });
    }

    return html;
}

/**
 * Setup event listeners for element controls
 */
function setupElementControls(elementDiv, element) {
    // Edit button
    const editBtn = elementDiv.querySelector('.edit-element-btn');
    if (editBtn) {
        editBtn.addEventListener('click', () => editElement(element.id));
    }

    // Move up button
    const moveUpBtn = elementDiv.querySelector('.move-up-btn');
    if (moveUpBtn) {
        moveUpBtn.addEventListener('click', () => moveElement(element.id, 'up'));
    }

    // Move down button
    const moveDownBtn = elementDiv.querySelector('.move-down-btn');
    if (moveDownBtn) {
        moveDownBtn.addEventListener('click', () => moveElement(element.id, 'down'));
    }

    // Delete button
    const deleteBtn = elementDiv.querySelector('.delete-element-btn');
    if (deleteBtn) {
        deleteBtn.addEventListener('click', () => deleteElement(element.id));
    }

    // Hover effects
    elementDiv.addEventListener('mouseenter', () => {
        elementDiv.style.borderColor = '#4285f4';
        elementDiv.style.boxShadow = '0 2px 8px rgba(66, 133, 244, 0.2)';
    });

    elementDiv.addEventListener('mouseleave', () => {
        elementDiv.style.borderColor = '#e0e0e0';
        elementDiv.style.boxShadow = 'none';
    });
}

/**
 * Edit an element
 */
function editElement(elementId) {
    const element = currentTemplate.elements.find(el => el.id === elementId);
    if (!element || !element.component.editable) return;

    // Create edit modal
    const editModal = document.createElement('div');
    editModal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 2147483650;
        display: flex;
        justify-content: center;
        align-items: center;
    `;

    const editContent = document.createElement('div');
    editContent.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 20px;
        width: 400px;
        max-width: 90vw;
    `;

    let formHTML = `
        <h3 style="margin: 0 0 16px 0;">Edit ${element.component.name}</h3>
    `;

    // Add form fields based on component type
    if (element.data.text !== undefined) {
        formHTML += `
            <div style="margin-bottom: 12px;">
                <label style="display: block; margin-bottom: 4px; font-weight: bold;">Text:</label>
                <input type="text" id="edit-text" value="${element.data.text || ''}" style="
                    width: 100%;
                    padding: 8px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    box-sizing: border-box;
                ">
            </div>
        `;
    }

    // Add custom fields
    if (element.component.customFields) {
        Object.entries(element.component.customFields).forEach(([field, defaultValue]) => {
            const currentValue = element.data[field] || defaultValue;
            formHTML += `
                <div style="margin-bottom: 12px;">
                    <label style="display: block; margin-bottom: 4px; font-weight: bold;">${field}:</label>
                    <input type="text" id="edit-${field}" value="${currentValue}" style="
                        width: 100%;
                        padding: 8px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        box-sizing: border-box;
                    ">
                </div>
            `;
        });
    }

    formHTML += `
        <div style="display: flex; gap: 8px; justify-content: flex-end; margin-top: 16px;">
            <button id="cancel-edit" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
            ">Cancel</button>
            <button id="save-edit" style="
                background: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
            ">Save</button>
        </div>
    `;

    editContent.innerHTML = formHTML;
    editModal.appendChild(editContent);
    document.body.appendChild(editModal);

    // Event listeners
    document.getElementById('cancel-edit').addEventListener('click', () => {
        document.body.removeChild(editModal);
    });

    document.getElementById('save-edit').addEventListener('click', () => {
        // Save changes
        if (element.data.text !== undefined) {
            element.data.text = document.getElementById('edit-text').value;
        }

        if (element.component.customFields) {
            Object.keys(element.component.customFields).forEach(field => {
                element.data[field] = document.getElementById(`edit-${field}`).value;
            });
        }

        // Re-render canvas and preview
        renderCanvas();
        updatePreview();

        // Close modal
        document.body.removeChild(editModal);

        showNotification('Element updated successfully', 'success');
    });

    // Focus first input
    setTimeout(() => {
        const firstInput = editContent.querySelector('input');
        if (firstInput) firstInput.focus();
    }, 100);
}

/**
 * Move an element up or down
 */
function moveElement(elementId, direction) {
    const index = currentTemplate.elements.findIndex(el => el.id === elementId);
    if (index === -1) return;

    const newIndex = direction === 'up' ? index - 1 : index + 1;

    if (newIndex < 0 || newIndex >= currentTemplate.elements.length) return;

    // Swap elements
    [currentTemplate.elements[index], currentTemplate.elements[newIndex]] =
    [currentTemplate.elements[newIndex], currentTemplate.elements[index]];

    // Re-render
    renderCanvas();
    updatePreview();
}

/**
 * Delete an element
 */
function deleteElement(elementId) {
    if (!confirm('Are you sure you want to delete this element?')) return;

    currentTemplate.elements = currentTemplate.elements.filter(el => el.id !== elementId);

    // Re-render
    renderCanvas();
    updatePreview();

    // Show empty state if no elements
    if (currentTemplate.elements.length === 0) {
        const emptyState = document.getElementById('canvas-empty-state');
        if (emptyState) {
            emptyState.style.display = 'block';
        }
    }
}

/**
 * Clear the entire canvas
 */
function clearCanvas() {
    if (currentTemplate.elements.length === 0) return;

    if (!confirm('Are you sure you want to clear the entire template?')) return;

    resetTemplate();
    renderCanvas();
    updatePreview();
}

/**
 * Undo last action (simplified version)
 */
function undoLastAction() {
    if (currentTemplate.elements.length === 0) return;

    // Remove last element
    currentTemplate.elements.pop();

    // Re-render
    renderCanvas();
    updatePreview();

    // Show empty state if no elements
    if (currentTemplate.elements.length === 0) {
        const emptyState = document.getElementById('canvas-empty-state');
        if (emptyState) {
            emptyState.style.display = 'block';
        }
    }

    showNotification('Last action undone', 'info');
}

/**
 * Render the entire canvas
 */
function renderCanvas() {
    const dropZone = document.getElementById('canvas-drop-zone');
    if (!dropZone) return;

    // Clear existing elements (except empty state)
    const existingElements = dropZone.querySelectorAll('.canvas-element');
    existingElements.forEach(el => el.remove());

    // Render all elements
    currentTemplate.elements.forEach(element => {
        renderCanvasElement(element);
    });
}

/**
 * Update the preview panel
 */
function updatePreview() {
    if (!visualBuilderPreview) return;

    if (currentTemplate.elements.length === 0) {
        // Show empty state
        const emptyState = document.getElementById('preview-empty-state');
        if (emptyState) {
            emptyState.style.display = 'block';
        }
        return;
    }

    // Hide empty state
    const emptyState = document.getElementById('preview-empty-state');
    if (emptyState) {
        emptyState.style.display = 'none';
    }

    // Generate HTML
    let html = '';
    currentTemplate.elements.forEach(element => {
        html += renderElementContent(element) + '\n';
    });

    visualBuilderPreview.innerHTML = html;
}

/**
 * Reset template to empty state
 */
function resetTemplate() {
    currentTemplate = { elements: [], metadata: {} };
    elementCounter = 0;

    // Show empty state
    const emptyState = document.getElementById('canvas-empty-state');
    if (emptyState) {
        emptyState.style.display = 'block';
    }
}

/**
 * Save the visual template
 */
function saveVisualTemplate() {
    if (currentTemplate.elements.length === 0) {
        showNotification('Please add some elements to your template first', 'warning');
        return;
    }

    const templateName = document.getElementById('template-name-input').value.trim();
    if (!templateName) {
        showNotification('Please enter a template name', 'warning');
        return;
    }

    const category = document.getElementById('template-category-select').value;

    // Generate HTML from template
    let html = '';
    currentTemplate.elements.forEach(element => {
        html += renderElementContent(element) + '\n';
    });

    // Save using template manager
    if (typeof templateManager !== 'undefined') {
        const metadata = {
            category: category,
            description: `Visual template with ${currentTemplate.elements.length} components`,
            createdWith: 'visual-builder',
            elementCount: currentTemplate.elements.length
        };

        const success = templateManager.saveTemplate(templateName, html, metadata);

        if (success) {
            showNotification(`Template "${templateName}" saved successfully!`, 'success');

            // Trigger template list refresh if template editor is open
            if (typeof refreshTemplateList === 'function') {
                refreshTemplateList();
            }

            // Refresh the integrated template manager list
            if (typeof loadTemplatesInBuilder === 'function') {
                setTimeout(() => {
                    loadTemplatesInBuilder();
                }, 100);
            }

            // Reset form
            document.getElementById('template-name-input').value = '';
            document.getElementById('template-category-select').value = 'custom';
        } else {
            showNotification('Failed to save template', 'error');
        }
    } else {
        showNotification('Template manager not available', 'error');
    }
}

/**
 * Preview the template with smart processing
 */
async function previewTemplate() {
    if (currentTemplate.elements.length === 0) {
        showNotification('Please add some elements to your template first', 'warning');
        return;
    }

    // Generate HTML from template
    let html = '';
    currentTemplate.elements.forEach(element => {
        html += renderElementContent(element) + '\n';
    });

    try {
        // Process with smart template engine if available
        let processedHtml = html;
        if (typeof processSmartTemplate === 'function') {
            processedHtml = await processSmartTemplate(html);
        }

        // Create preview modal
        const previewModal = document.createElement('div');
        previewModal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2147483651;
            display: flex;
            justify-content: center;
            align-items: center;
        `;

        const previewContent = document.createElement('div');
        previewContent.style.cssText = `
            background: white;
            border-radius: 8px;
            width: 80%;
            max-width: 800px;
            height: 80%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        `;

        previewContent.innerHTML = `
            <div style="
                padding: 16px 20px;
                background: #f8f9fa;
                border-bottom: 1px solid #e0e0e0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-weight: bold;
            ">
                <span>📋 Template Preview</span>
                <button id="close-preview" style="
                    background: #dc3545;
                    color: white;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                    cursor: pointer;
                ">Close</button>
            </div>
            <div style="
                flex: 1;
                padding: 20px;
                overflow-y: auto;
                background: white;
            ">${processedHtml}</div>
        `;

        previewModal.appendChild(previewContent);
        document.body.appendChild(previewModal);

        // Close button
        document.getElementById('close-preview').addEventListener('click', () => {
            document.body.removeChild(previewModal);
        });

        // Close on backdrop click
        previewModal.addEventListener('click', (e) => {
            if (e.target === previewModal) {
                document.body.removeChild(previewModal);
            }
        });

    } catch (error) {
        console.error('Preview error:', error);
        showNotification('Error generating preview', 'error');
    }
}

/**
 * Show notification message
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 16px;
        border-radius: 6px;
        color: white;
        font-weight: bold;
        z-index: 2147483652;
        max-width: 300px;
        animation: slideIn 0.3s ease;
    `;

    // Set background color based on type
    const colors = {
        success: '#28a745',
        error: '#dc3545',
        warning: '#ffc107',
        info: '#17a2b8'
    };

    notification.style.backgroundColor = colors[type] || colors.info;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 3000);
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    .component-tab {
        background: #f8f9fa;
        border: 1px solid #ddd;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;
    }

    .component-tab.active {
        background: #4285f4;
        color: white;
        border-color: #4285f4;
    }

    .component-tab:hover:not(.active) {
        background: #e9ecef;
    }
`;
document.head.appendChild(style);

/**
 * Setup keyboard shortcuts
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Only handle shortcuts when visual builder is open
        if (!visualBuilderModal || visualBuilderModal.style.display === 'none') return;

        // Ctrl+Z - Undo
        if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
            e.preventDefault();
            undoAction();
        }

        // Ctrl+Shift+Z or Ctrl+Y - Redo
        if ((e.ctrlKey && e.shiftKey && e.key === 'Z') || (e.ctrlKey && e.key === 'y')) {
            e.preventDefault();
            redoAction();
        }

        // Ctrl+S - Save template
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            saveVisualTemplate();
        }

        // Delete key - Delete selected element (if any)
        if (e.key === 'Delete') {
            const selectedElement = document.querySelector('.canvas-element:hover');
            if (selectedElement) {
                const elementId = selectedElement.dataset.elementId;
                deleteElement(elementId);
            }
        }

        // Escape - Close modals
        if (e.key === 'Escape') {
            const editModal = document.querySelector('[id*="edit-modal"]');
            if (editModal) {
                document.body.removeChild(editModal);
            } else {
                closeVisualTemplateBuilder();
            }
        }
    });
}

/**
 * Save current state to history for undo/redo
 */
function saveToHistory() {
    // Remove any future history if we're not at the end
    if (visualBuilderHistoryIndex < visualBuilderTemplateHistory.length - 1) {
        visualBuilderTemplateHistory = visualBuilderTemplateHistory.slice(0, visualBuilderHistoryIndex + 1);
    }

    // Add current state
    visualBuilderTemplateHistory.push(JSON.parse(JSON.stringify(currentTemplate)));
    visualBuilderHistoryIndex++;

    // Limit history size
    if (visualBuilderTemplateHistory.length > 50) {
        visualBuilderTemplateHistory.shift();
        visualBuilderHistoryIndex--;
    }
}

/**
 * Undo last action
 */
function undoAction() {
    if (visualBuilderHistoryIndex > 0) {
        visualBuilderHistoryIndex--;
        currentTemplate = JSON.parse(JSON.stringify(visualBuilderTemplateHistory[visualBuilderHistoryIndex]));
        renderCanvas();
        updatePreview();
        showNotification('Action undone', 'info');
    }
}

/**
 * Redo last undone action
 */
function redoAction() {
    if (visualBuilderHistoryIndex < visualBuilderTemplateHistory.length - 1) {
        visualBuilderHistoryIndex++;
        currentTemplate = JSON.parse(JSON.stringify(visualBuilderTemplateHistory[visualBuilderHistoryIndex]));
        renderCanvas();
        updatePreview();
        showNotification('Action redone', 'info');
    }
}

/**
 * Start auto-save functionality
 */
function startAutoSave() {
    // Auto-save every 30 seconds
    autoSaveTimer = setInterval(() => {
        if (currentTemplate.elements.length > 0) {
            autoSaveTemplate();
        }
    }, 30000);
}

/**
 * Auto-save template to localStorage
 */
function autoSaveTemplate() {
    try {
        const autoSaveData = {
            template: currentTemplate,
            timestamp: Date.now(),
            elementCounter: elementCounter
        };

        localStorage.setItem('Stashy_visual_builder_autosave', JSON.stringify(autoSaveData));
    } catch (error) {
        console.error('Auto-save failed:', error);
    }
}

/**
 * Show help dialog for the visual template builder
 */
function showVisualBuilderHelp() {
    // Check if help dialog already exists
    const existingDialog = document.querySelector('.visual-builder-help-modal');
    if (existingDialog) {
        existingDialog.focus();
        return;
    }

    const helpModal = document.createElement('div');
    helpModal.className = 'visual-builder-help-modal';
    helpModal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 2147483651;
        display: flex;
        justify-content: center;
        align-items: center;
    `;

    const helpContent = document.createElement('div');
    helpContent.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 30px;
        max-width: 700px;
        max-height: 80vh;
        overflow-y: auto;
        position: relative;
    `;

    helpContent.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h2 style="margin: 0; color: #333; font-size: 24px;">🎨 Template Builder Help</h2>
            <button id="close-help" style="
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #666;
                padding: 0;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            ">×</button>
        </div>

        <div style="color: #555; line-height: 1.6;">
            <h3 style="color: #667eea; margin-top: 0;">Getting Started</h3>
            <p>The Visual Template Builder lets you create custom note templates without any HTML knowledge. Simply drag and drop components to build your perfect template.</p>

            <h3 style="color: #667eea;">How to Use</h3>
            <ul>
                <li><strong>Components Tab:</strong> Drag elements from the sidebar to the canvas</li>
                <li><strong>Sections Tab:</strong> Click pre-designed template sections to add them instantly</li>
                <li><strong>Manage Tab:</strong> View, edit, rename, and backup your saved templates</li>
            </ul>

            <h3 style="color: #667eea;">Available Components</h3>
            <ul>
                <li><strong>Text Elements:</strong> Headers, paragraphs, quotes, and dividers</li>
                <li><strong>Lists:</strong> Bullet lists, numbered lists, and checkboxes</li>
                <li><strong>Interactive:</strong> Buttons, input fields, and user prompts</li>
                <li><strong>Data:</strong> Tables, date fields, and progress bars</li>
            </ul>

            <h3 style="color: #667eea;">Tips & Tricks</h3>
            <ul>
                <li>Double-click any element on the canvas to edit its content</li>
                <li>Use Ctrl+Z to undo and Ctrl+Y to redo actions</li>
                <li>Save your templates with descriptive names for easy finding</li>
                <li>Preview your template before saving to see how it will look</li>
                <li>Use sections for quick template creation with common layouts</li>
            </ul>

            <h3 style="color: #667eea;">Keyboard Shortcuts</h3>
            <ul>
                <li><strong>Ctrl+S:</strong> Save template</li>
                <li><strong>Ctrl+Z:</strong> Undo last action</li>
                <li><strong>Ctrl+Y:</strong> Redo last action</li>
                <li><strong>Escape:</strong> Close dialogs</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button id="got-it-help" style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
                font-weight: 500;
            ">Got it!</button>
        </div>
    `;

    helpModal.appendChild(helpContent);
    document.body.appendChild(helpModal);

    // Event listeners
    document.getElementById('close-help').addEventListener('click', () => {
        document.body.removeChild(helpModal);
    });

    document.getElementById('got-it-help').addEventListener('click', () => {
        document.body.removeChild(helpModal);
    });

    // Close on backdrop click
    helpModal.addEventListener('click', (e) => {
        if (e.target === helpModal) {
            document.body.removeChild(helpModal);
        }
    });

    // Close on Escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            document.body.removeChild(helpModal);
            document.removeEventListener('keydown', handleEscape);
        }
    };
    document.addEventListener('keydown', handleEscape);
}

/**
 * Show onboarding tour for first-time users
 */
function showOnboardingTour() {
    // Check if user has seen the tour before
    const hasSeenTour = localStorage.getItem('Stashy_visual_builder_tour_seen');
    if (hasSeenTour) {
        isFirstTimeUser = false;
        return;
    }

    const tourModal = document.createElement('div');
    tourModal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        z-index: 2147483651;
        display: flex;
        justify-content: center;
        align-items: center;
    `;

    const tourContent = document.createElement('div');
    tourContent.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 30px;
        max-width: 600px;
        text-align: center;
    `;

    tourContent.innerHTML = `
        <h2 style="color: #667eea; margin: 0 0 20px 0;">🎨 Welcome to Visual Template Builder!</h2>
        <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Create professional templates without any HTML knowledge!
        </p>
        <div style="text-align: left; margin: 20px 0;">
            <h3>Quick Start Guide:</h3>
            <ul style="line-height: 1.8;">
                <li><strong>Drag & Drop:</strong> Drag components from the left panel to the canvas</li>
                <li><strong>Pre-designed Sections:</strong> Click "Sections" tab for ready-made templates</li>
                <li><strong>Edit Components:</strong> Click "Edit" on any component to customize it</li>
                <li><strong>Live Preview:</strong> See your template update in real-time on the right</li>
                <li><strong>Keyboard Shortcuts:</strong> Ctrl+Z (undo), Ctrl+S (save), Delete (remove)</li>
            </ul>
        </div>
        <div style="margin-top: 30px;">
            <button id="start-tour" style="
                background: #667eea;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
                margin-right: 10px;
            ">Start Building!</button>
            <button id="skip-tour" style="
                background: #6c757d;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
            ">Skip Tour</button>
        </div>
    `;

    tourModal.appendChild(tourContent);
    document.body.appendChild(tourModal);

    // Event listeners
    document.getElementById('start-tour').addEventListener('click', () => {
        localStorage.setItem('Stashy_visual_builder_tour_seen', 'true');
        document.body.removeChild(tourModal);
        isFirstTimeUser = false;

        // Add a sample component to get started
        addTemplateSection('dailyJournal');
    });

    document.getElementById('skip-tour').addEventListener('click', () => {
        localStorage.setItem('Stashy_visual_builder_tour_seen', 'true');
        document.body.removeChild(tourModal);
        isFirstTimeUser = false;
    });
}

// Dynamic Table Functions
window.addTableRow = function(button) {
    const table = button.parentElement.nextElementSibling;
    const tbody = table.querySelector('tbody');
    const headerCount = table.querySelectorAll('thead th').length;

    const newRow = document.createElement('tr');
    for (let i = 0; i < headerCount; i++) {
        const cell = document.createElement('td');
        cell.contentEditable = true;
        cell.style.cssText = 'border: 1px solid #ddd; padding: 8px;';
        cell.textContent = `New Cell ${tbody.children.length + 1}-${i + 1}`;
        cell.onblur = function() { checkAutoExpand(this); };
        newRow.appendChild(cell);
    }
    tbody.appendChild(newRow);

    // Focus on first cell of new row
    newRow.firstElementChild.focus();
    showTableNotification('Row added successfully!', 'success');
};

window.addTableColumn = function(button) {
    const table = button.parentElement.nextElementSibling;
    const headerRow = table.querySelector('thead tr');
    const rows = table.querySelectorAll('tbody tr');

    // Add header
    const newHeader = document.createElement('th');
    newHeader.contentEditable = true;
    newHeader.style.cssText = 'border: 1px solid #ddd; padding: 8px; min-width: 100px;';
    newHeader.textContent = `Header ${headerRow.children.length + 1}`;
    if (table.classList.contains('smart-table')) {
        newHeader.onclick = function() { sortTable(this, headerRow.children.length); };
        newHeader.style.cursor = 'pointer';
        newHeader.textContent += ' ↕️';
    }
    headerRow.appendChild(newHeader);

    // Add cells to each row
    rows.forEach((row, index) => {
        const newCell = document.createElement('td');
        newCell.contentEditable = true;
        newCell.style.cssText = 'border: 1px solid #ddd; padding: 8px;';
        newCell.textContent = `Cell ${index + 1}-${row.children.length + 1}`;
        newCell.onblur = function() { checkAutoExpand(this); };
        row.appendChild(newCell);
    });

    // Focus on new header
    newHeader.focus();
    showTableNotification('Column added successfully!', 'success');
};

window.removeTableRow = function(button) {
    const table = button.parentElement.nextElementSibling;
    const tbody = table.querySelector('tbody');

    if (tbody.children.length > 1) {
        tbody.removeChild(tbody.lastElementChild);
        showTableNotification('Row removed successfully!', 'info');
    } else {
        showTableNotification('Cannot remove the last row. Table must have at least one row.', 'error');
    }
};

window.removeTableColumn = function(button) {
    const table = button.parentElement.nextElementSibling;
    const headerRow = table.querySelector('thead tr');
    const rows = table.querySelectorAll('tbody tr');

    if (headerRow.children.length > 1) {
        // Remove header
        headerRow.removeChild(headerRow.lastElementChild);

        // Remove last cell from each row
        rows.forEach(row => {
            row.removeChild(row.lastElementChild);
        });
        showTableNotification('Column removed successfully!', 'info');
    } else {
        showTableNotification('Cannot remove the last column. Table must have at least one column.', 'error');
    }
};

// Smart Table Functions
window.checkAutoExpand = function(cell) {
    const table = cell.closest('table');
    if (!table || !table.dataset.smart) return;

    const row = cell.parentElement;
    const tbody = table.querySelector('tbody');
    const isLastRow = row === tbody.lastElementChild;
    const isLastCell = cell === row.lastElementChild;

    // Auto-expand if user typed in the last cell of the last row
    if (isLastRow && isLastCell && cell.textContent.trim() !== '') {
        const headerCount = table.querySelectorAll('thead th').length;
        const newRow = document.createElement('tr');

        for (let i = 0; i < headerCount; i++) {
            const newCell = document.createElement('td');
            newCell.contentEditable = true;
            newCell.style.cssText = 'border: 1px solid #ddd; padding: 8px;';
            newCell.textContent = '';
            newCell.onblur = function() { checkAutoExpand(this); };
            newRow.appendChild(newCell);
        }

        tbody.appendChild(newRow);

        // Show notification
        showTableNotification('New row added automatically!', 'success');
    }
};

window.sortTable = function(header, columnIndex) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    // Determine sort direction
    const isAscending = !header.dataset.sortDirection || header.dataset.sortDirection === 'desc';
    header.dataset.sortDirection = isAscending ? 'asc' : 'desc';

    // Update header text
    const headerText = header.textContent.replace(/[↑↓↕️]/g, '').trim();
    header.textContent = headerText + (isAscending ? ' ↑' : ' ↓');

    // Sort rows
    rows.sort((a, b) => {
        const aText = a.children[columnIndex].textContent.trim();
        const bText = b.children[columnIndex].textContent.trim();

        // Try to parse as numbers
        const aNum = parseFloat(aText);
        const bNum = parseFloat(bText);

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        } else {
            return isAscending ? aText.localeCompare(bText) : bText.localeCompare(aText);
        }
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));

    showTableNotification(`Sorted by ${headerText} ${isAscending ? 'ascending' : 'descending'}`, 'info');
};

function showTableNotification(message, type) {
    const notification = document.createElement('div');
    const colors = {
        success: '#28a745',
        info: '#17a2b8',
        error: '#dc3545',
        warning: '#ffc107'
    };

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type] || colors.info};
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        z-index: 10000;
        font-size: 14px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 3000);
}

// Universal Table Enhancement System for Note Text Area
function enhanceAllTablesInNote() {
    // Find all tables in the note text area
    const noteTextArea = document.querySelector('#Stashy-note-text');
    if (!noteTextArea) return;

    const tables = noteTextArea.querySelectorAll('table');
    tables.forEach(table => {
        enhanceTableWithDynamicFeatures(table);
    });
}

function enhanceTableWithDynamicFeatures(table) {
    // Skip if already enhanced
    if (table.dataset.enhanced === 'true') return;

    // Mark as enhanced
    table.dataset.enhanced = 'true';

    // Add dynamic table class
    table.classList.add('enhanced-table');

    // Create control buttons container
    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'table-controls-container';
    controlsContainer.style.cssText = `
        margin-bottom: 8px;
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        background: #f8f9fa;
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    `;

    // Create control buttons
    const addRowBtn = createTableControlButton('+ Row', '#28a745', () => addTableRowInNote(table));
    const addColBtn = createTableControlButton('+ Column', '#007bff', () => addTableColumnInNote(table));
    const removeRowBtn = createTableControlButton('- Row', '#dc3545', () => removeTableRowInNote(table));
    const removeColBtn = createTableControlButton('- Column', '#fd7e14', () => removeTableColumnInNote(table));

    controlsContainer.appendChild(addRowBtn);
    controlsContainer.appendChild(addColBtn);
    controlsContainer.appendChild(removeRowBtn);
    controlsContainer.appendChild(removeColBtn);

    // Insert controls before the table
    table.parentNode.insertBefore(controlsContainer, table);

    // Make all cells editable and add auto-expand functionality
    const cells = table.querySelectorAll('td, th');
    cells.forEach(cell => {
        if (!cell.hasAttribute('contenteditable')) {
            cell.contentEditable = true;
        }

        // Add auto-expand functionality
        cell.addEventListener('blur', function() {
            checkAutoExpandInNote(this);
        });

        // Add enhanced styling
        cell.style.transition = 'background-color 0.2s ease';
    });

    // Add sorting functionality to headers (selective based on table type)
    const tableType = determineTableType(table);

    if (tableType === 'template-builder' || tableType === 'smart-table') {
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            if (!header.onclick) {
                header.style.cursor = 'pointer';
                header.style.userSelect = 'none';
                header.title = 'Click to sort';
                header.addEventListener('click', () => sortTableInNote(header, index));

                // Add sort indicator if not present
                if (!header.textContent.includes('↕️') && !header.textContent.includes('↑') && !header.textContent.includes('↓')) {
                    header.textContent += ' ↕️';
                }
            }
        });
    }

    // Mark table type for future reference
    table.dataset.tableType = tableType;
}

function createTableControlButton(text, color, onClick) {
    const button = document.createElement('button');
    button.textContent = text;
    button.style.cssText = `
        background: ${color};
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        font-weight: 500;
        transition: all 0.2s ease;
    `;

    button.addEventListener('mouseover', () => {
        button.style.transform = 'translateY(-1px)';
        button.style.boxShadow = '0 2px 4px rgba(0,0,0,0.2)';
    });

    button.addEventListener('mouseout', () => {
        button.style.transform = 'translateY(0)';
        button.style.boxShadow = 'none';
    });

    button.addEventListener('click', onClick);
    return button;
}

// Table manipulation functions for note text area
function addTableRowInNote(table) {
    const tbody = table.querySelector('tbody') || table;
    const headerCount = table.querySelectorAll('tr')[0]?.children.length || 3;

    const newRow = document.createElement('tr');

    // Get styling from existing cells to maintain consistency
    const existingCell = table.querySelector('td');
    const cellStyle = existingCell ? getComputedCellStyle(existingCell) : getDefaultCellStyle();

    for (let i = 0; i < headerCount; i++) {
        const cell = document.createElement('td');
        cell.contentEditable = true;

        // Apply inherited styling from existing cells
        applyCellStyle(cell, cellStyle);

        cell.textContent = '';
        cell.addEventListener('blur', function() {
            checkAutoExpandInNote(this);
        });
        newRow.appendChild(cell);
    }

    if (tbody.tagName === 'TBODY') {
        tbody.appendChild(newRow);
    } else {
        tbody.appendChild(newRow);
    }

    // Focus on first cell of new row
    newRow.firstElementChild.focus();
    showTableNotificationInNote('Row added successfully!', 'success');
}

function addTableColumnInNote(table) {
    const rows = table.querySelectorAll('tr');

    // Determine table type for sorting behavior
    const tableType = table.dataset.tableType || determineTableType(table);
    const shouldHaveSorting = (tableType === 'template-builder' || tableType === 'smart-table');

    rows.forEach((row, rowIndex) => {
        const isHeaderRow = rowIndex === 0 && table.querySelector('thead');
        const cell = document.createElement(isHeaderRow ? 'th' : 'td');
        cell.contentEditable = true;

        // Get styling from existing cells to maintain consistency
        const existingCell = row.querySelector(isHeaderRow ? 'th' : 'td');
        const cellStyle = existingCell ? getComputedCellStyle(existingCell) : getDefaultCellStyle();

        // Apply inherited styling from existing cells
        applyCellStyle(cell, cellStyle);

        cell.textContent = isHeaderRow ? `Header ${row.children.length + 1}` : '';

        if (cell.tagName === 'TH' && shouldHaveSorting) {
            // Only add sorting to Template Builder and Smart tables
            cell.style.cursor = 'pointer';
            cell.style.userSelect = 'none';
            cell.title = 'Click to sort';
            cell.addEventListener('click', () => sortTableInNote(cell, row.children.length));
            cell.textContent += ' ↕️';
        } else if (cell.tagName === 'TD') {
            cell.addEventListener('blur', function() {
                checkAutoExpandInNote(this);
            });
        }

        row.appendChild(cell);
    });

    showTableNotificationInNote('Column added successfully!', 'success');
}

function removeTableRowInNote(table) {
    const tbody = table.querySelector('tbody');
    const rows = tbody ? tbody.querySelectorAll('tr') : table.querySelectorAll('tr');

    if (rows.length > 1) {
        const lastRow = rows[rows.length - 1];
        lastRow.parentNode.removeChild(lastRow);
        showTableNotificationInNote('Row removed successfully!', 'info');
    } else {
        showTableNotificationInNote('Cannot remove the last row. Table must have at least one row.', 'error');
    }
}

function removeTableColumnInNote(table) {
    const rows = table.querySelectorAll('tr');

    if (rows[0]?.children.length > 1) {
        rows.forEach(row => {
            if (row.children.length > 0) {
                row.removeChild(row.lastElementChild);
            }
        });
        showTableNotificationInNote('Column removed successfully!', 'info');
    } else {
        showTableNotificationInNote('Cannot remove the last column. Table must have at least one column.', 'error');
    }
}

function checkAutoExpandInNote(cell) {
    const table = cell.closest('table');
    if (!table || !table.dataset.enhanced) return;

    const row = cell.parentElement;
    const tbody = table.querySelector('tbody') || table;
    const allRows = tbody.querySelectorAll('tr');
    const isLastRow = row === allRows[allRows.length - 1];
    const isLastCell = cell === row.lastElementChild;

    // Auto-expand if user typed in the last cell of the last row
    if (isLastRow && isLastCell && cell.textContent.trim() !== '') {
        // Use the enhanced addTableRowInNote function that preserves styling
        addTableRowInNote(table);
        showTableNotificationInNote('New row added automatically!', 'success');
    }
}

function sortTableInNote(header, columnIndex) {
    const table = header.closest('table');
    const tbody = table.querySelector('tbody') || table;
    const rows = Array.from(tbody.querySelectorAll('tr'));

    // Skip header row if it exists
    const dataRows = table.querySelector('thead') ? rows : rows.slice(1);

    // Determine sort direction
    const isAscending = !header.dataset.sortDirection || header.dataset.sortDirection === 'desc';
    header.dataset.sortDirection = isAscending ? 'asc' : 'desc';

    // Update header text
    const headerText = header.textContent.replace(/[↑↓↕️]/g, '').trim();
    header.textContent = headerText + (isAscending ? ' ↑' : ' ↓');

    // Sort rows
    dataRows.sort((a, b) => {
        const aText = a.children[columnIndex]?.textContent.trim() || '';
        const bText = b.children[columnIndex]?.textContent.trim() || '';

        // Try to parse as numbers
        const aNum = parseFloat(aText);
        const bNum = parseFloat(bText);

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        } else {
            return isAscending ? aText.localeCompare(bText) : bText.localeCompare(aText);
        }
    });

    // Re-append sorted rows
    dataRows.forEach(row => tbody.appendChild(row));

    showTableNotificationInNote(`Sorted by ${headerText} ${isAscending ? 'ascending' : 'descending'}`, 'info');
}

function showTableNotificationInNote(message, type) {
    const notification = document.createElement('div');
    const colors = {
        success: '#28a745',
        info: '#17a2b8',
        error: '#dc3545',
        warning: '#ffc107'
    };

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type] || colors.info};
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        z-index: 10000;
        font-size: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        animation: slideIn 0.3s ease;
        max-width: 300px;
    `;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 2500);
}

// Auto-enhance tables when content changes
function setupTableAutoEnhancement() {
    const noteTextArea = document.querySelector('#Stashy-note-text');
    if (!noteTextArea) return;

    // Create observer to watch for new tables
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // Check if the added node is a table or contains tables
                    if (node.tagName === 'TABLE') {
                        enhanceTableWithDynamicFeatures(node);
                    } else if (node.querySelectorAll) {
                        const tables = node.querySelectorAll('table');
                        tables.forEach(table => enhanceTableWithDynamicFeatures(table));
                    }
                }
            });
        });
    });

    observer.observe(noteTextArea, {
        childList: true,
        subtree: true
    });

    // Also enhance existing tables
    enhanceAllTablesInNote();
}

// Initialize table enhancement when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupTableAutoEnhancement);
} else {
    setupTableAutoEnhancement();
}

// Also run enhancement when note UI is opened
document.addEventListener('click', function(e) {
    if (e.target.closest('#Stashy-note-text') || e.target.id === 'Stashy-note-text') {
        setTimeout(enhanceAllTablesInNote, 100);
    }
});

/**
 * Determine the type of table for appropriate feature application
 * @param {HTMLElement} table - The table element to analyze
 * @returns {string} - Table type: 'template-builder', 'smart-table', 'pre-built-template', or 'unknown'
 */
function determineTableType(table) {
    // Check if table is from Template Builder (has specific classes or attributes)
    if (table.classList.contains('Stashy-table') ||
        table.classList.contains('Stashy-smart-table') ||
        table.classList.contains('smart-table') ||
        table.classList.contains('dynamic-table')) {
        return 'template-builder';
    }

    // Check if table has smart table syntax indicators
    if (table.classList.contains('Stashy-smart-table') ||
        table.querySelector('td[data-smart-syntax]') ||
        table.querySelector('th[data-smart-syntax]')) {
        return 'smart-table';
    }

    // Check if table is from pre-built templates (inside template content wrapper)
    if (table.closest('.Stashy-template-content')) {
        return 'pre-built-template';
    }

    // Check if table was created in Visual Template Builder context
    if (table.closest('#Stashy-visual-template-builder') ||
        table.dataset.createdBy === 'visual-builder') {
        return 'template-builder';
    }

    // Default to unknown type
    return 'unknown';
}

/**
 * Get computed style from an existing cell to maintain consistency
 * @param {HTMLElement} cell - The existing cell to copy style from
 * @returns {Object} - Style object with relevant properties
 */
function getComputedCellStyle(cell) {
    const computedStyle = window.getComputedStyle(cell);
    return {
        border: computedStyle.border || computedStyle.borderWidth + ' ' + computedStyle.borderStyle + ' ' + computedStyle.borderColor,
        borderTop: computedStyle.borderTop,
        borderRight: computedStyle.borderRight,
        borderBottom: computedStyle.borderBottom,
        borderLeft: computedStyle.borderLeft,
        padding: computedStyle.padding,
        backgroundColor: computedStyle.backgroundColor,
        fontSize: computedStyle.fontSize,
        fontFamily: computedStyle.fontFamily,
        fontWeight: computedStyle.fontWeight,
        textAlign: computedStyle.textAlign,
        verticalAlign: computedStyle.verticalAlign,
        lineHeight: computedStyle.lineHeight,
        color: computedStyle.color
    };
}

/**
 * Get default cell style for fallback
 * @returns {Object} - Default style object
 */
function getDefaultCellStyle() {
    return {
        border: '1px solid #ddd',
        padding: '8px',
        backgroundColor: 'transparent',
        fontSize: 'inherit',
        fontFamily: 'inherit',
        fontWeight: 'normal',
        textAlign: 'left',
        verticalAlign: 'top',
        lineHeight: 'normal',
        color: 'inherit'
    };
}

/**
 * Apply style to a cell element
 * @param {HTMLElement} cell - The cell to style
 * @param {Object} style - The style object to apply
 */
function applyCellStyle(cell, style) {
    // Apply border styles
    if (style.border && style.border !== 'none') {
        cell.style.border = style.border;
    } else {
        // Apply individual border properties if border shorthand is not available
        if (style.borderTop) cell.style.borderTop = style.borderTop;
        if (style.borderRight) cell.style.borderRight = style.borderRight;
        if (style.borderBottom) cell.style.borderBottom = style.borderBottom;
        if (style.borderLeft) cell.style.borderLeft = style.borderLeft;
    }

    // Apply other styles
    if (style.padding) cell.style.padding = style.padding;
    if (style.backgroundColor && style.backgroundColor !== 'rgba(0, 0, 0, 0)') {
        cell.style.backgroundColor = style.backgroundColor;
    }
    if (style.fontSize) cell.style.fontSize = style.fontSize;
    if (style.fontFamily) cell.style.fontFamily = style.fontFamily;
    if (style.fontWeight) cell.style.fontWeight = style.fontWeight;
    if (style.textAlign) cell.style.textAlign = style.textAlign;
    if (style.verticalAlign) cell.style.verticalAlign = style.verticalAlign;
    if (style.lineHeight) cell.style.lineHeight = style.lineHeight;
    if (style.color) cell.style.color = style.color;

    // Always add transition for smooth interactions
    cell.style.transition = 'background-color 0.2s ease';
}

// Integrated Template Management Variables (selectedTemplateInBuilder already declared above)

/**
 * Load templates in the builder manage tab
 */
function loadTemplatesInBuilder() {
    const templateList = document.getElementById('template-list-builder');
    if (!templateList) return;

    templateList.innerHTML = '';

    // Get all templates from multiple sources
    const allTemplates = {};

    // Add built-in templates
    if (typeof StashyTemplates === 'object') {
        Object.assign(allTemplates, StashyTemplates);
    }

    // Add custom templates from templateManager (primary source for custom templates)
    if (typeof templateManager !== 'undefined') {
        const managerTemplates = templateManager.getAllTemplates();
        Object.keys(managerTemplates).forEach(name => {
            if (managerTemplates[name].isCustom) {
                allTemplates[name] = managerTemplates[name].content;
            }
        });
    }

    // Add custom templates from legacy storage (fallback)
    const legacyCustomTemplates = getCustomTemplatesForBuilder();
    Object.keys(legacyCustomTemplates).forEach(name => {
        if (!allTemplates[name]) { // Don't override templateManager templates
            allTemplates[name] = legacyCustomTemplates[name];
        }
    });

    // Create template items
    Object.keys(allTemplates).forEach(templateName => {
        const templateItem = createTemplateItemForBuilder(templateName, allTemplates[templateName]);
        templateList.appendChild(templateItem);
    });

    // Setup search and filter
    setupTemplateSearchInBuilder();
}

/**
 * Create a template item for the builder
 */
function createTemplateItemForBuilder(name, content) {
    const item = document.createElement('div');
    item.className = 'template-item-builder';
    item.dataset.templateName = name;
    item.style.cssText = `
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 4px;
        padding: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 12px;
    `;

    // Determine if it's a custom template (check multiple sources)
    let isCustom = false;

    // Check if it's NOT in built-in templates
    if (!StashyTemplates.hasOwnProperty(name)) {
        isCustom = true;
    }

    // Check templateManager for custom status
    if (typeof templateManager !== 'undefined') {
        const template = templateManager.getTemplate(name);
        if (template) {
            isCustom = template.isCustom;
        }
    }

    const category = getTemplateCategoryForBuilder(name);

    item.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="flex: 1; min-width: 0;">
                <div style="font-weight: 500; color: #333; margin-bottom: 2px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                    ${escapeHtmlForBuilder(name)}
                </div>
                <div style="font-size: 10px; color: #666;">
                    ${isCustom ? '👤 Custom' : '🏭 Built-in'} • ${category}
                </div>
            </div>
            <div style="font-size: 10px; color: #999; margin-left: 8px;">
                ${content.includes('<table') ? '📋' : '📄'}
            </div>
        </div>
    `;

    // Add hover effects
    item.addEventListener('mouseenter', () => {
        item.style.background = '#f8f9fa';
        item.style.borderColor = '#667eea';
    });

    item.addEventListener('mouseleave', () => {
        if (selectedTemplateInBuilder !== name) {
            item.style.background = 'white';
            item.style.borderColor = '#e0e0e0';
        }
    });

    // Click to select
    item.addEventListener('click', () => {
        selectTemplateInBuilder(name, content, item);
    });

    return item;
}

/**
 * Select a template in the builder
 */
function selectTemplateInBuilder(name, content, itemElement) {
    // Update UI to show selected state
    document.querySelectorAll('.template-item-builder').forEach(item => {
        item.style.background = 'white';
        item.style.borderColor = '#e0e0e0';
    });

    itemElement.style.background = '#e3f2fd';
    itemElement.style.borderColor = '#2196f3';

    // Set current selected template
    selectedTemplateInBuilder = name;

    // Enable action buttons
    document.getElementById('edit-template-btn-builder').disabled = false;
    document.getElementById('rename-template-btn-builder').disabled = false;
    document.getElementById('backup-template-btn-builder').disabled = false;
    document.getElementById('delete-template-btn-builder').disabled = false;
}

/**
 * Setup template search and filter in builder
 */
function setupTemplateSearchInBuilder() {
    const searchInput = document.getElementById('template-search-builder');
    const categoryFilter = document.getElementById('template-category-filter-builder');

    if (searchInput) {
        searchInput.addEventListener('input', filterTemplatesInBuilder);
    }

    if (categoryFilter) {
        categoryFilter.addEventListener('change', filterTemplatesInBuilder);
    }
}

/**
 * Filter templates in builder
 */
function filterTemplatesInBuilder() {
    const searchTerm = document.getElementById('template-search-builder')?.value.toLowerCase() || '';
    const categoryFilter = document.getElementById('template-category-filter-builder')?.value || '';

    const templateItems = document.querySelectorAll('.template-item-builder');

    templateItems.forEach(item => {
        const templateName = item.dataset.templateName.toLowerCase();
        const templateCategory = item.querySelector('div div:last-child').textContent.split('•')[1]?.trim() || '';

        const matchesSearch = templateName.includes(searchTerm);
        const matchesCategory = !categoryFilter || templateCategory === categoryFilter;

        item.style.display = (matchesSearch && matchesCategory) ? 'block' : 'none';
    });
}

/**
 * Get template category for builder
 */
function getTemplateCategoryForBuilder(name) {
    const lowerName = name.toLowerCase();

    if (lowerName.includes('meeting') || lowerName.includes('project') ||
        lowerName.includes('bug') || lowerName.includes('office') ||
        lowerName.includes('rubric') || lowerName.includes('publication')) return 'work';

    if (lowerName.includes('lecture') || lowerName.includes('study') ||
        lowerName.includes('assignment') || lowerName.includes('reading') ||
        lowerName.includes('grade') || lowerName.includes('schedule')) return 'study';

    if (lowerName.includes('journal') || lowerName.includes('daily') ||
        lowerName.includes('shopping') || lowerName.includes('event') ||
        lowerName.includes('goal') || lowerName.includes('recipe')) return 'personal';

    if (lowerName.includes('experiment') || lowerName.includes('literature') ||
        lowerName.includes('data') || lowerName.includes('lab') ||
        lowerName.includes('sample') || lowerName.includes('protocol')) return 'project';

    return 'general';
}

/**
 * Get custom templates for builder
 */
function getCustomTemplatesForBuilder() {
    try {
        return JSON.parse(localStorage.getItem('Stashy_CustomTemplates') || '{}');
    } catch (e) {
        console.error('Error loading custom templates:', e);
        return {};
    }
}

/**
 * Escape HTML for builder
 */
function escapeHtmlForBuilder(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Setup template management event listeners
 */
function setupTemplateManagementEvents() {
    // Use event delegation for better reliability
    document.addEventListener('click', (e) => {
        // Edit template button
        if (e.target.id === 'edit-template-btn-builder') {
            e.preventDefault();
            e.stopPropagation();
            editTemplateInBuilder();
        }

        // Rename template button
        else if (e.target.id === 'rename-template-btn-builder') {
            e.preventDefault();
            e.stopPropagation();
            renameTemplateInBuilder();
        }

        // Backup template button
        else if (e.target.id === 'backup-template-btn-builder') {
            e.preventDefault();
            e.stopPropagation();
            backupTemplateInBuilder();
        }

        // Delete template button
        else if (e.target.id === 'delete-template-btn-builder') {
            e.preventDefault();
            e.stopPropagation();
            deleteTemplateInBuilder();
        }
    });

    // Also try direct attachment with retry mechanism
    const attachDirectListeners = () => {
        // Edit template button
        const editBtn = document.getElementById('edit-template-btn-builder');
        if (editBtn && !editBtn.hasAttribute('data-listener-attached')) {
            editBtn.addEventListener('click', editTemplateInBuilder);
            editBtn.setAttribute('data-listener-attached', 'true');
        }

        // Rename template button
        const renameBtn = document.getElementById('rename-template-btn-builder');
        if (renameBtn && !renameBtn.hasAttribute('data-listener-attached')) {
            renameBtn.addEventListener('click', renameTemplateInBuilder);
            renameBtn.setAttribute('data-listener-attached', 'true');
        }

        // Backup template button
        const backupBtn = document.getElementById('backup-template-btn-builder');
        if (backupBtn && !backupBtn.hasAttribute('data-listener-attached')) {
            backupBtn.addEventListener('click', backupTemplateInBuilder);
            backupBtn.setAttribute('data-listener-attached', 'true');
        }

        // Delete template button
        const deleteBtn = document.getElementById('delete-template-btn-builder');
        if (deleteBtn && !deleteBtn.hasAttribute('data-listener-attached')) {
            deleteBtn.addEventListener('click', deleteTemplateInBuilder);
            deleteBtn.setAttribute('data-listener-attached', 'true');
        }
    };

    // Try immediate attachment
    attachDirectListeners();

    // Retry after DOM updates
    setTimeout(attachDirectListeners, 100);
    setTimeout(attachDirectListeners, 500);
}

/**
 * Edit template in builder
 */
function editTemplateInBuilder() {
    if (!selectedTemplateInBuilder) return;

    // Get template content from all sources
    let content = null;

    // Try template manager first (for custom templates)
    if (typeof templateManager !== 'undefined') {
        const template = templateManager.getTemplate(selectedTemplateInBuilder);
        if (template) {
            content = template.content;
        }
    }

    // Fallback to built-in templates
    if (!content && StashyTemplates[selectedTemplateInBuilder]) {
        content = StashyTemplates[selectedTemplateInBuilder];
    }

    // Fallback to custom templates storage
    if (!content) {
        const customTemplates = getCustomTemplatesForBuilder();
        content = customTemplates[selectedTemplateInBuilder];
    }

    if (!content) {
        showBuilderNotification('Template content not found', 'error');
        return;
    }

    // Clear current template
    currentTemplate = { elements: [] };
    elementCounter = 0;

    // Load template content into the canvas for editing
    const dropZone = document.getElementById('canvas-drop-zone');
    const emptyState = document.getElementById('canvas-empty-state');

    if (dropZone) {
        // Clear current canvas
        dropZone.innerHTML = '';

        // Hide empty state
        if (emptyState) {
            emptyState.style.display = 'none';
        }

        // Parse template content and convert to editable elements
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = content;

        // Convert HTML elements to visual builder elements
        const htmlElements = tempDiv.children;
        for (let i = 0; i < htmlElements.length; i++) {
            const htmlElement = htmlElements[i];
            const builderElement = convertHtmlToBuilderElement(htmlElement);
            if (builderElement) {
                currentTemplate.elements.push(builderElement);
                elementCounter++;
            }
        }

        // Render the template in the canvas
        renderCanvas();

        // Switch to components tab for editing
        switchTab('components');

        // Show notification
        showBuilderNotification(`Template "${selectedTemplateInBuilder}" loaded for editing`, 'success');

        // Update template name in save section
        const templateNameInput = document.getElementById('template-name-input');
        if (templateNameInput) {
            templateNameInput.value = selectedTemplateInBuilder;
        }

        // Update preview
        updatePreview();

        // Save to history
        saveToHistory();
    }
}

/**
 * Convert HTML element to visual builder element
 */
function convertHtmlToBuilderElement(htmlElement) {
    const tagName = htmlElement.tagName.toLowerCase();
    const textContent = htmlElement.textContent.trim();
    const innerHTML = htmlElement.innerHTML;

    let componentType = 'paragraph'; // Default component type
    let elementData = { text: textContent };

    // Determine component type and data based on HTML tag
    switch (tagName) {
        case 'h1':
            componentType = 'header1';
            elementData = { text: textContent };
            break;
        case 'h2':
            componentType = 'header2';
            elementData = { text: textContent };
            break;
        case 'h3':
            componentType = 'header3';
            elementData = { text: textContent };
            break;
        case 'h4':
        case 'h5':
        case 'h6':
            componentType = 'header3'; // Use header3 for h4-h6
            elementData = { text: textContent };
            break;
        case 'p':
            componentType = 'paragraph';
            elementData = { text: innerHTML };
            break;
        case 'ul':
            componentType = 'bulletList';
            const listItems = Array.from(htmlElement.querySelectorAll('li')).map(li => li.textContent.trim());
            elementData = {
                item1: listItems[0] || '',
                item2: listItems[1] || '',
                item3: listItems[2] || '',
                item4: listItems[3] || '',
                item5: listItems[4] || ''
            };
            break;
        case 'ol':
            componentType = 'numberedList';
            const numberedItems = Array.from(htmlElement.querySelectorAll('li')).map(li => li.textContent.trim());
            elementData = {
                item1: numberedItems[0] || '',
                item2: numberedItems[1] || '',
                item3: numberedItems[2] || '',
                item4: numberedItems[3] || '',
                item5: numberedItems[4] || ''
            };
            break;
        case 'table':
            componentType = 'dataTable';
            elementData = { text: 'Imported Table' };
            break;
        case 'img':
            componentType = 'paragraph'; // Treat images as paragraphs with HTML content
            elementData = { text: htmlElement.outerHTML };
            break;
        case 'div':
            // Check if it's a special component
            if (htmlElement.classList.contains('Stashy-table')) {
                componentType = 'dataTable';
                elementData = { text: 'Stashy Table' };
            } else {
                componentType = 'paragraph';
                elementData = { text: innerHTML || textContent };
            }
            break;
        default:
            componentType = 'paragraph';
            elementData = { text: htmlElement.outerHTML };
    }

    // Get the component definition
    const component = TEMPLATE_COMPONENTS[componentType];
    if (!component) {
        // Fallback to paragraph if component not found
        componentType = 'paragraph';
        component = TEMPLATE_COMPONENTS[componentType];
        elementData = { text: htmlElement.outerHTML };
    }

    // Create the element in the expected format
    const element = {
        id: `element-${elementCounter}`,
        type: componentType,
        component: component,
        data: elementData
    };

    return element;
}

/**
 * Rename template in builder
 */
function renameTemplateInBuilder() {
    if (!selectedTemplateInBuilder) return;

    const newName = prompt('Enter new template name:', selectedTemplateInBuilder);
    if (!newName || newName.trim() === '') return;

    const trimmedName = newName.trim();
    if (trimmedName === selectedTemplateInBuilder) return;

    // Check if name already exists
    const allTemplates = { ...StashyTemplates, ...getCustomTemplatesForBuilder() };
    if (allTemplates.hasOwnProperty(trimmedName)) {
        alert('A template with this name already exists!');
        return;
    }

    // Get current content
    const content = allTemplates[selectedTemplateInBuilder];

    // Delete old and create new
    deleteTemplateByNameInBuilder(selectedTemplateInBuilder, false);
    saveTemplateByNameInBuilder(trimmedName, content);

    // Update selection
    selectedTemplateInBuilder = trimmedName;

    // Refresh list
    loadTemplatesInBuilder();
    showBuilderNotification('Template renamed successfully!', 'success');
}

/**
 * Backup template in builder
 */
function backupTemplateInBuilder() {
    if (!selectedTemplateInBuilder) return;

    const allTemplates = { ...StashyTemplates, ...getCustomTemplatesForBuilder() };
    const content = allTemplates[selectedTemplateInBuilder];

    const backupData = {
        name: selectedTemplateInBuilder,
        content: content,
        category: getTemplateCategoryForBuilder(selectedTemplateInBuilder),
        timestamp: new Date().toISOString(),
        version: '1.0'
    };

    // Create download
    const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${selectedTemplateInBuilder.replace(/[^a-z0-9]/gi, '_')}_backup.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showBuilderNotification('Template backup downloaded!', 'success');
}

/**
 * Delete template in builder
 */
function deleteTemplateInBuilder() {
    if (!selectedTemplateInBuilder) return;

    const isBuiltIn = StashyTemplates.hasOwnProperty(selectedTemplateInBuilder);
    const confirmMessage = isBuiltIn
        ? `Are you sure you want to delete the built-in template "${selectedTemplateInBuilder}"? This action cannot be undone.`
        : `Are you sure you want to delete the template "${selectedTemplateInBuilder}"?`;

    if (!confirm(confirmMessage)) return;

    deleteTemplateByNameInBuilder(selectedTemplateInBuilder, true);

    // Clear selection
    selectedTemplateInBuilder = null;

    // Disable buttons
    document.getElementById('edit-template-btn-builder').disabled = true;
    document.getElementById('rename-template-btn-builder').disabled = true;
    document.getElementById('backup-template-btn-builder').disabled = true;
    document.getElementById('delete-template-btn-builder').disabled = true;

    // Refresh list
    loadTemplatesInBuilder();
    showBuilderNotification('Template deleted successfully!', 'success');
}

/**
 * Save template by name in builder
 */
function saveTemplateByNameInBuilder(name, content) {
    // Try to save using templateManager first (preferred method)
    if (typeof templateManager !== 'undefined') {
        try {
            const metadata = {
                category: getTemplateCategoryForBuilder(name),
                description: `Template managed through Visual Template Builder`,
                createdWith: 'visual-builder-manager'
            };

            templateManager.saveTemplate(name, content, metadata);

            // Refresh the template list
            setTimeout(() => {
                loadTemplatesInBuilder();
            }, 100);

            return;
        } catch (e) {
            console.error('Error saving template with templateManager:', e);
        }
    }

    // Fallback to legacy storage
    const customTemplates = getCustomTemplatesForBuilder();
    customTemplates[name] = content;

    try {
        localStorage.setItem('Stashy_CustomTemplates', JSON.stringify(customTemplates));

        // Refresh the template list
        setTimeout(() => {
            loadTemplatesInBuilder();
        }, 100);
    } catch (e) {
        console.error('Error saving template to legacy storage:', e);
    }
}

/**
 * Delete template by name in builder
 */
function deleteTemplateByNameInBuilder(name, showConfirm = true) {
    // Try to delete using templateManager first
    if (typeof templateManager !== 'undefined') {
        try {
            const template = templateManager.getTemplate(name);
            if (template && template.isCustom) {
                templateManager.deleteTemplate(name);

                // Refresh the template list
                setTimeout(() => {
                    loadTemplatesInBuilder();
                }, 100);

                return;
            }
        } catch (e) {
            console.error('Error deleting template with templateManager:', e);
        }
    }

    // Fallback to legacy storage
    const customTemplates = getCustomTemplatesForBuilder();

    if (customTemplates.hasOwnProperty(name)) {
        // Delete from custom templates
        delete customTemplates[name];
        localStorage.setItem('Stashy_CustomTemplates', JSON.stringify(customTemplates));
    } else if (StashyTemplates.hasOwnProperty(name)) {
        // Mark built-in template as deleted
        customTemplates[`__deleted_${name}`] = true;
        localStorage.setItem('Stashy_CustomTemplates', JSON.stringify(customTemplates));
    }

    // Refresh the template list
    setTimeout(() => {
        loadTemplatesInBuilder();
    }, 100);
}

/**
 * Show notification in builder
 */
function showBuilderNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        z-index: 2147483650;
        animation: slideIn 0.3s ease;
    `;

    switch (type) {
        case 'success':
            notification.style.background = '#28a745';
            break;
        case 'error':
            notification.style.background = '#dc3545';
            break;
        default:
            notification.style.background = '#007bff';
    }

    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

console.log("Stashy: Enhanced Template Builder with Universal Dynamic Tables and Style Consistency Loaded");

// Template Session Management Functions

/**
 * Create a new template session
 */
function createTemplateSession(templateName = null, templateContent = null) {
    sessionCounter++;
    const sessionId = `session-${sessionCounter}`;

    const session = {
        id: sessionId,
        templateName: templateName || `New Template ${sessionCounter}`,
        originalTemplateName: templateName,
        template: templateContent ? parseTemplateContent(templateContent) : { elements: [], metadata: {} },
        history: [],
        historyIndex: -1,
        lastModified: Date.now(),
        isDirty: false
    };

    templateSessions.set(sessionId, session);
    return sessionId;
}

/**
 * Switch to a different template session
 */
function switchTemplateSession(sessionId) {
    if (!templateSessions.has(sessionId)) {
        console.error('Template session not found:', sessionId);
        return false;
    }

    // Save current session state if exists
    if (currentSessionId && templateSessions.has(currentSessionId)) {
        saveCurrentSessionState();
    }

    // Load new session
    currentSessionId = sessionId;
    const session = templateSessions.get(sessionId);

    // Update current template
    currentTemplate = { ...session.template };
    visualBuilderTemplateHistory = [...session.history];
    visualBuilderHistoryIndex = session.historyIndex;

    // Update UI
    renderCanvas();
    updatePreview();
    updateSessionIndicator();

    // Update template name input
    const templateNameInput = document.getElementById('template-name-input');
    if (templateNameInput) {
        templateNameInput.value = session.templateName;
    }

    return true;
}

/**
 * Save current session state
 */
function saveCurrentSessionState() {
    if (!currentSessionId || !templateSessions.has(currentSessionId)) return;

    const session = templateSessions.get(currentSessionId);
    session.template = { ...currentTemplate };
    session.history = [...visualBuilderTemplateHistory];
    session.historyIndex = visualBuilderHistoryIndex;
    session.lastModified = Date.now();
    session.isDirty = true;

    templateSessions.set(currentSessionId, session);
}

/**
 * Parse template content into builder format
 */
function parseTemplateContent(content) {
    if (typeof content === 'string') {
        // Convert HTML content to builder elements
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = content;

        const elements = [];
        let counter = 0;

        for (let i = 0; i < tempDiv.children.length; i++) {
            const htmlElement = tempDiv.children[i];
            const builderElement = convertHtmlToBuilderElement(htmlElement);
            if (builderElement) {
                builderElement.id = `element-${counter++}`;
                elements.push(builderElement);
            }
        }

        return { elements, metadata: {} };
    }

    return content || { elements: [], metadata: {} };
}

/**
 * Update session indicator in UI
 */
function updateSessionIndicator() {
    const indicator = document.getElementById('session-indicator');
    if (!indicator) return;

    if (currentSessionId && templateSessions.has(currentSessionId)) {
        const session = templateSessions.get(currentSessionId);
        const dirtyIndicator = session.isDirty ? ' •' : '';
        indicator.textContent = `Editing: ${session.templateName}${dirtyIndicator}`;
        indicator.style.display = 'block';
    } else {
        indicator.style.display = 'none';
    }
}

/**
 * Open template switcher modal
 */
function openTemplateSwitcher() {
    const modal = document.createElement('div');
    modal.id = 'template-switcher-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 2147483647;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 24px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    `;

    content.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h3 style="margin: 0; color: #333;">🔄 Switch Template</h3>
            <button id="close-switcher" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">×</button>
        </div>

        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #333;">Current Sessions</h4>
            <div id="current-sessions" style="max-height: 200px; overflow-y: auto;"></div>
        </div>

        <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 10px 0; color: #333;">Load Different Template</h4>
            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                <input type="text" id="template-search-switcher" placeholder="Search templates..." style="flex: 1; padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                <button id="new-template-btn" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer;">+ New</button>
            </div>
            <div id="available-templates" style="max-height: 250px; overflow-y: auto; border: 1px solid #e0e0e0; border-radius: 6px;"></div>
        </div>

        <div style="display: flex; justify-content: flex-end; gap: 10px;">
            <button id="cancel-switcher" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer;">Cancel</button>
        </div>
    `;

    modal.appendChild(content);
    document.body.appendChild(modal);

    // Populate current sessions
    populateCurrentSessions();

    // Populate available templates
    populateAvailableTemplates();

    // Setup event listeners
    setupSwitcherEventListeners(modal);
}

/**
 * Populate current sessions list
 */
function populateCurrentSessions() {
    const container = document.getElementById('current-sessions');
    if (!container) return;

    container.innerHTML = '';

    if (templateSessions.size === 0) {
        container.innerHTML = '<p style="color: #666; font-style: italic; margin: 0;">No active sessions</p>';
        return;
    }

    templateSessions.forEach((session, sessionId) => {
        const sessionItem = document.createElement('div');
        sessionItem.style.cssText = `
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            ${sessionId === currentSessionId ? 'background: #e3f2fd; border-color: #2196f3;' : 'background: #f8f9fa;'}
        `;

        const dirtyIndicator = session.isDirty ? ' <span style="color: #ff9800;">•</span>' : '';
        const currentIndicator = sessionId === currentSessionId ? ' <span style="color: #2196f3;">(Current)</span>' : '';

        sessionItem.innerHTML = `
            <div style="font-weight: 500; color: #333;">${session.templateName}${dirtyIndicator}${currentIndicator}</div>
            <div style="font-size: 12px; color: #666; margin-top: 4px;">
                ${session.template.elements.length} elements • Modified ${new Date(session.lastModified).toLocaleTimeString()}
            </div>
        `;

        sessionItem.addEventListener('click', () => {
            if (sessionId !== currentSessionId) {
                switchTemplateSession(sessionId);
                closeTemplateSwitcher();
            }
        });

        sessionItem.addEventListener('mouseenter', () => {
            if (sessionId !== currentSessionId) {
                sessionItem.style.background = '#f0f0f0';
            }
        });

        sessionItem.addEventListener('mouseleave', () => {
            if (sessionId !== currentSessionId) {
                sessionItem.style.background = '#f8f9fa';
            }
        });

        container.appendChild(sessionItem);
    });
}

/**
 * Populate available templates list
 */
function populateAvailableTemplates() {
    const container = document.getElementById('available-templates');
    if (!container) return;

    container.innerHTML = '';

    // Get all templates
    const allTemplates = {};

    // Add built-in templates
    if (typeof StashyTemplates === 'object') {
        Object.assign(allTemplates, StashyTemplates);
    }

    // Add custom templates from templateManager
    if (typeof templateManager !== 'undefined') {
        const managerTemplates = templateManager.getAllTemplates();
        Object.keys(managerTemplates).forEach(name => {
            if (managerTemplates[name].isCustom) {
                allTemplates[name] = managerTemplates[name].content;
            }
        });
    }

    // Add custom templates from legacy storage
    const legacyCustomTemplates = getCustomTemplatesForBuilder();
    Object.keys(legacyCustomTemplates).forEach(name => {
        if (!allTemplates[name]) {
            allTemplates[name] = legacyCustomTemplates[name];
        }
    });

    Object.keys(allTemplates).forEach(templateName => {
        const templateItem = document.createElement('div');
        templateItem.style.cssText = `
            padding: 10px;
            border-bottom: 1px solid #e0e0e0;
            cursor: pointer;
            transition: background 0.2s ease;
        `;

        const isCustom = !StashyTemplates.hasOwnProperty(templateName);
        const typeIndicator = isCustom ? '👤 Custom' : '🏭 Built-in';

        templateItem.innerHTML = `
            <div style="font-weight: 500; color: #333;">${templateName}</div>
            <div style="font-size: 12px; color: #666; margin-top: 2px;">${typeIndicator}</div>
        `;

        templateItem.addEventListener('click', () => {
            loadTemplateInNewSession(templateName, allTemplates[templateName]);
            closeTemplateSwitcher();
        });

        templateItem.addEventListener('mouseenter', () => {
            templateItem.style.background = '#f0f0f0';
        });

        templateItem.addEventListener('mouseleave', () => {
            templateItem.style.background = 'transparent';
        });

        container.appendChild(templateItem);
    });
}

/**
 * Load template in new session
 */
function loadTemplateInNewSession(templateName, templateContent) {
    const sessionId = createTemplateSession(templateName, templateContent);
    switchTemplateSession(sessionId);
    showBuilderNotification(`Loaded template: ${templateName}`, 'success');
}

/**
 * Setup switcher event listeners
 */
function setupSwitcherEventListeners(modal) {
    // Close buttons
    document.getElementById('close-switcher').addEventListener('click', closeTemplateSwitcher);
    document.getElementById('cancel-switcher').addEventListener('click', closeTemplateSwitcher);

    // New template button
    document.getElementById('new-template-btn').addEventListener('click', () => {
        const sessionId = createTemplateSession();
        switchTemplateSession(sessionId);
        closeTemplateSwitcher();
        showBuilderNotification('Created new template session', 'success');
    });

    // Search functionality
    const searchInput = document.getElementById('template-search-switcher');
    searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const templateItems = document.querySelectorAll('#available-templates > div');

        templateItems.forEach(item => {
            const templateName = item.querySelector('div').textContent.toLowerCase();
            item.style.display = templateName.includes(searchTerm) ? 'block' : 'none';
        });
    });

    // Close on backdrop click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeTemplateSwitcher();
        }
    });
}

/**
 * Close template switcher
 */
function closeTemplateSwitcher() {
    const modal = document.getElementById('template-switcher-modal');
    if (modal) {
        modal.remove();
    }
}

// Legacy Template Management System (now integrated into Visual Template Builder)
// These functions are kept for backward compatibility but redirect to the integrated interface

/**
 * Open the template management interface (redirects to Visual Template Builder)
 */
function openTemplateManager() {
    // Open Visual Template Builder and switch to manage tab
    openVisualTemplateBuilder();
    setTimeout(() => {
        switchBuilderTab('manage');
    }, 100);
}

/**
 * Close the template management interface (legacy compatibility)
 */
function closeTemplateManager() {
    // Close Visual Template Builder
    closeVisualTemplateBuilder();
}

// Make functions available globally
window.openVisualTemplateBuilder = openVisualTemplateBuilder;
window.closeVisualTemplateBuilder = closeVisualTemplateBuilder;
window.openTemplateManager = openTemplateManager;
window.closeTemplateManager = closeTemplateManager;
window.enhanceAllTablesInNote = enhanceAllTablesInNote;
window.getComputedCellStyle = getComputedCellStyle;
window.applyCellStyle = applyCellStyle;

// --- END OF FILE content-visual-template-builder.js ---
