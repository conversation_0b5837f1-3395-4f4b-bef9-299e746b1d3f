/**
 * Stashy Virtual Data Loader
 * Provides progressive loading for large datasets with virtual scrolling support
 */

// Create a namespace to avoid global pollution
window.StashyVirtualDataLoader = (function() {
    // Loading strategies
    const LOADING_STRATEGIES = {
        PAGINATION: 'pagination',       // Load data in pages
        INFINITE_SCROLL: 'infinite',    // Load more data as user scrolls
        VIRTUAL_SCROLL: 'virtual',      // Virtual scrolling with fixed height items
        WINDOWED: 'windowed'            // Windowed scrolling with variable height items
    };

    // Data sources
    const DATA_SOURCES = {
        HIGHLIGHTS: 'highlights',
        NOTES: 'notes',
        METADATA: 'metadata',
        CACHE: 'cache',
        CUSTOM: 'custom'
    };

    // Configuration
    const config = {
        defaultPageSize: 50,                // Default number of items per page
        preloadPages: 1,                    // Number of pages to preload
        loadThreshold: 0.8,                 // Load more when scrolled to 80% of visible area
        defaultStrategy: LOADING_STRATEGIES.VIRTUAL_SCROLL,
        cacheResults: true,                 // Cache loaded results
        cacheTTL: 5 * 60 * 1000,            // Cache TTL in milliseconds (5 minutes)
        useWorker: true,                    // Use web worker for data processing
        debug: false                        // Debug mode
    };

    // Private variables
    const dataLoaders = {};
    const dataCache = {};
    let workerInstance = null;

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StashyVirtualDataLoader]', ...args);
        }
    }

    /**
     * Creates a virtual data loader for a specific element
     * @param {string} elementId - The ID of the container element
     * @param {Object} options - Loader options
     * @returns {Object} The data loader instance
     */
    function createLoader(elementId, options = {}) {
        // Check if element exists
        const element = document.getElementById(elementId);
        if (!element) {
            throw new Error(`Element with ID "${elementId}" not found`);
        }

        // Merge options with defaults
        const loaderOptions = {
            strategy: config.defaultStrategy,
            pageSize: config.defaultPageSize,
            source: DATA_SOURCES.HIGHLIGHTS,
            sourceOptions: {},
            itemHeight: 50,                 // Default item height in pixels
            totalItems: 0,                  // Total number of items (if known)
            renderItem: null,               // Function to render an item
            loadData: null,                 // Function to load data
            ...options
        };

        // Create loader instance
        const loader = {
            id: elementId,
            element,
            options: loaderOptions,
            state: {
                loading: false,
                initialized: false,
                currentPage: 0,
                loadedPages: [],
                visibleItems: [],
                allItems: [],
                error: null
            }
        };

        // Initialize the loader
        initializeLoader(loader);

        // Store the loader
        dataLoaders[elementId] = loader;

        return loader;
    }

    /**
     * Initializes a data loader
     * @param {Object} loader - The loader to initialize
     */
    function initializeLoader(loader) {
        try {
            const { element, options } = loader;

            // Set up the container
            element.classList.add('Stashy-virtual-container');
            element.style.position = 'relative';
            element.style.overflow = 'auto';
            element.innerHTML = '';

            // Create inner container for items
            const innerContainer = document.createElement('div');
            innerContainer.className = 'Stashy-virtual-content';
            innerContainer.style.position = 'relative';
            element.appendChild(innerContainer);

            // Create loading indicator
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'Stashy-virtual-loading';
            loadingIndicator.textContent = 'Loading...';
            loadingIndicator.style.display = 'none';
            element.appendChild(loadingIndicator);

            // Store references
            loader.innerContainer = innerContainer;
            loader.loadingIndicator = loadingIndicator;

            // Set up scroll event listener based on strategy
            switch (options.strategy) {
                case LOADING_STRATEGIES.PAGINATION:
                    setupPaginationControls(loader);
                    break;

                case LOADING_STRATEGIES.INFINITE_SCROLL:
                    setupInfiniteScroll(loader);
                    break;

                case LOADING_STRATEGIES.VIRTUAL_SCROLL:
                    setupVirtualScroll(loader);
                    break;

                case LOADING_STRATEGIES.WINDOWED:
                    setupWindowedScroll(loader);
                    break;

                default:
                    throw new Error(`Unknown loading strategy: ${options.strategy}`);
            }

            // Load initial data
            loadInitialData(loader);

            loader.state.initialized = true;
        } catch (error) {
            console.error('Stashy: Error initializing virtual data loader:', error);
            loader.state.error = error.message;
        }
    }

    /**
     * Sets up pagination controls
     * @param {Object} loader - The loader to set up
     */
    function setupPaginationControls(loader) {
        const { element, options } = loader;

        // Create pagination controls
        const paginationControls = document.createElement('div');
        paginationControls.className = 'Stashy-pagination-controls';
        paginationControls.style.display = 'flex';
        paginationControls.style.justifyContent = 'center';
        paginationControls.style.padding = '10px';
        paginationControls.style.gap = '5px';

        // Previous button
        const prevButton = document.createElement('button');
        prevButton.textContent = '← Previous';
        prevButton.disabled = true;
        prevButton.addEventListener('click', () => {
            if (loader.state.currentPage > 0) {
                loadPage(loader, loader.state.currentPage - 1);
            }
        });

        // Next button
        const nextButton = document.createElement('button');
        nextButton.textContent = 'Next →';
        nextButton.addEventListener('click', () => {
            loadPage(loader, loader.state.currentPage + 1);
        });

        // Page indicator
        const pageIndicator = document.createElement('span');
        pageIndicator.className = 'Stashy-page-indicator';
        pageIndicator.style.margin = '0 10px';
        pageIndicator.textContent = 'Page 1';

        // Add controls to container
        paginationControls.appendChild(prevButton);
        paginationControls.appendChild(pageIndicator);
        paginationControls.appendChild(nextButton);

        // Add controls to element
        element.appendChild(paginationControls);

        // Store references
        loader.paginationControls = paginationControls;
        loader.prevButton = prevButton;
        loader.nextButton = nextButton;
        loader.pageIndicator = pageIndicator;
    }

    /**
     * Sets up infinite scroll
     * @param {Object} loader - The loader to set up
     */
    function setupInfiniteScroll(loader) {
        const { element } = loader;

        // Add scroll event listener
        element.addEventListener('scroll', () => {
            if (loader.state.loading) return;

            const { scrollTop, scrollHeight, clientHeight } = element;
            const scrolledPercentage = (scrollTop + clientHeight) / scrollHeight;

            if (scrolledPercentage >= config.loadThreshold) {
                loadNextPage(loader);
            }
        });
    }

    /**
     * Sets up virtual scrolling
     * @param {Object} loader - The loader to set up
     */
    function setupVirtualScroll(loader) {
        const { element, options, innerContainer } = loader;

        // Set up scroll event listener
        element.addEventListener('scroll', () => {
            updateVisibleItems(loader);
        });

        // Set up resize observer
        const resizeObserver = new ResizeObserver(() => {
            updateVisibleItems(loader);
        });
        resizeObserver.observe(element);

        // Store reference
        loader.resizeObserver = resizeObserver;
    }

    /**
     * Sets up windowed scrolling
     * @param {Object} loader - The loader to set up
     */
    function setupWindowedScroll(loader) {
        // Similar to virtual scroll but with variable height items
        setupVirtualScroll(loader);

        // Add additional state for variable heights
        loader.state.itemHeights = [];
    }

    /**
     * Loads initial data
     * @param {Object} loader - The loader to load data for
     */
    async function loadInitialData(loader) {
        try {
            // Show loading indicator
            showLoading(loader, true);

            // Load first page
            await loadPage(loader, 0);

            // Update visible items for virtual/windowed scroll
            if (loader.options.strategy === LOADING_STRATEGIES.VIRTUAL_SCROLL ||
                loader.options.strategy === LOADING_STRATEGIES.WINDOWED) {
                updateVisibleItems(loader);
            }
        } catch (error) {
            console.error('Stashy: Error loading initial data:', error);
            loader.state.error = error.message;
        } finally {
            // Hide loading indicator
            showLoading(loader, false);
        }
    }

    /**
     * Loads a specific page of data
     * @param {Object} loader - The loader to load data for
     * @param {number} page - The page number to load
     * @returns {Promise<Array>} A promise that resolves with the loaded items
     */
    async function loadPage(loader, page) {
        try {
            // Check if already loading
            if (loader.state.loading) {
                return [];
            }

            // Update state
            loader.state.loading = true;
            loader.state.currentPage = page;

            // Show loading indicator
            showLoading(loader, true);

            // Check cache first
            const cacheKey = getCacheKey(loader, page);
            if (config.cacheResults && dataCache[cacheKey]) {
                const cachedData = dataCache[cacheKey];
                if (Date.now() < cachedData.expiry) {
                    debugLog(`Using cached data for ${cacheKey}`);
                    processLoadedData(loader, cachedData.data, page);
                    return cachedData.data;
                }
            }

            // Load data
            let items = [];
            if (loader.options.loadData) {
                // Use custom load function
                items = await loader.options.loadData(page, loader.options.pageSize, loader.options.sourceOptions);
            } else {
                // Use built-in loaders
                items = await loadDataFromSource(loader.options.source, page, loader.options.pageSize, loader.options.sourceOptions);
            }

            // Process loaded data
            processLoadedData(loader, items, page);

            // Cache results if enabled
            if (config.cacheResults) {
                dataCache[cacheKey] = {
                    data: items,
                    expiry: Date.now() + config.cacheTTL
                };
            }

            return items;
        } catch (error) {
            console.error(`Stashy: Error loading page ${page}:`, error);
            loader.state.error = error.message;
            return [];
        } finally {
            // Update state
            loader.state.loading = false;

            // Hide loading indicator
            showLoading(loader, false);

            // Update pagination controls if using pagination
            if (loader.options.strategy === LOADING_STRATEGIES.PAGINATION) {
                updatePaginationControls(loader);
            }
        }
    }

    /**
     * Processes loaded data
     * @param {Object} loader - The loader to process data for
     * @param {Array} items - The loaded items
     * @param {number} page - The page number
     */
    function processLoadedData(loader, items, page) {
        const { options, state, innerContainer } = loader;

        // Update state
        if (!state.loadedPages.includes(page)) {
            state.loadedPages.push(page);
        }

        // Handle different strategies
        switch (options.strategy) {
            case LOADING_STRATEGIES.PAGINATION:
                // Replace all items
                state.allItems = items;
                renderItems(loader, items);
                break;

            case LOADING_STRATEGIES.INFINITE_SCROLL:
                // Append items
                if (page === 0) {
                    state.allItems = items;
                } else {
                    state.allItems = state.allItems.concat(items);
                }
                renderItems(loader, state.allItems);
                break;

            case LOADING_STRATEGIES.VIRTUAL_SCROLL:
            case LOADING_STRATEGIES.WINDOWED:
                // Update all items
                if (page === 0) {
                    state.allItems = items;
                } else {
                    // Insert items at the correct position
                    const startIndex = page * options.pageSize;
                    state.allItems = [
                        ...state.allItems.slice(0, startIndex),
                        ...items,
                        ...state.allItems.slice(startIndex + items.length)
                    ];
                }

                // Update container height
                updateContainerHeight(loader);

                // Update visible items
                updateVisibleItems(loader);
                break;
        }

        // Update total items count if not set
        if (!options.totalItems && items.length > 0) {
            // If we got fewer items than page size, we can estimate the total
            if (items.length < options.pageSize) {
                options.totalItems = page * options.pageSize + items.length;
            }
        }
    }

    /**
     * Renders items in the container
     * @param {Object} loader - The loader to render items for
     * @param {Array} items - The items to render
     */
    function renderItems(loader, items) {
        const { options, innerContainer } = loader;

        // Clear container
        innerContainer.innerHTML = '';

        // Render each item
        items.forEach((item, index) => {
            const itemElement = document.createElement('div');
            itemElement.className = 'Stashy-virtual-item';
            itemElement.dataset.index = index;

            // Use custom render function if provided
            if (options.renderItem) {
                const renderedContent = options.renderItem(item, index);
                if (typeof renderedContent === 'string') {
                    itemElement.innerHTML = renderedContent;
                } else if (renderedContent instanceof Node) {
                    itemElement.appendChild(renderedContent);
                }
            } else {
                // Default rendering
                itemElement.textContent = JSON.stringify(item);
            }

            innerContainer.appendChild(itemElement);
        });
    }

    /**
     * Updates the visible items for virtual scrolling
     * @param {Object} loader - The loader to update
     */
    function updateVisibleItems(loader) {
        const { element, options, state, innerContainer } = loader;

        // Skip if not using virtual or windowed scroll
        if (options.strategy !== LOADING_STRATEGIES.VIRTUAL_SCROLL &&
            options.strategy !== LOADING_STRATEGIES.WINDOWED) {
            return;
        }

        // Get scroll position
        const scrollTop = element.scrollTop;
        const viewportHeight = element.clientHeight;

        // Calculate visible range
        const startIndex = Math.floor(scrollTop / options.itemHeight);
        const endIndex = Math.ceil((scrollTop + viewportHeight) / options.itemHeight);

        // Add buffer for smoother scrolling
        const bufferSize = options.pageSize / 2;
        const visibleStartIndex = Math.max(0, startIndex - bufferSize);
        const visibleEndIndex = Math.min(state.allItems.length - 1, endIndex + bufferSize);

        // Get visible items
        const visibleItems = state.allItems.slice(visibleStartIndex, visibleEndIndex + 1);
        state.visibleItems = visibleItems;

        // Clear container
        innerContainer.innerHTML = '';

        // Render visible items
        visibleItems.forEach((item, index) => {
            const actualIndex = visibleStartIndex + index;
            const itemElement = document.createElement('div');
            itemElement.className = 'Stashy-virtual-item';
            itemElement.dataset.index = actualIndex;

            // Position the item
            itemElement.style.position = 'absolute';
            itemElement.style.top = `${actualIndex * options.itemHeight}px`;
            itemElement.style.width = '100%';

            if (options.strategy === LOADING_STRATEGIES.VIRTUAL_SCROLL) {
                itemElement.style.height = `${options.itemHeight}px`;
            }

            // Use custom render function if provided
            if (options.renderItem) {
                const renderedContent = options.renderItem(item, actualIndex);
                if (typeof renderedContent === 'string') {
                    itemElement.innerHTML = renderedContent;
                } else if (renderedContent instanceof Node) {
                    itemElement.appendChild(renderedContent);
                }
            } else {
                // Default rendering
                itemElement.textContent = JSON.stringify(item);
            }

            innerContainer.appendChild(itemElement);

            // For windowed scroll, update item heights
            if (options.strategy === LOADING_STRATEGIES.WINDOWED) {
                // Use ResizeObserver to track actual height
                const resizeObserver = new ResizeObserver(entries => {
                    for (const entry of entries) {
                        const height = entry.contentRect.height;
                        if (height > 0 && state.itemHeights[actualIndex] !== height) {
                            state.itemHeights[actualIndex] = height;
                            updateWindowedLayout(loader);
                        }
                    }
                });
                resizeObserver.observe(itemElement);
            }
        });

        // Check if we need to load more data
        const lastVisibleIndex = visibleEndIndex;
        const totalLoaded = state.allItems.length;
        const totalItems = options.totalItems || Infinity;

        if (lastVisibleIndex >= totalLoaded - bufferSize && totalLoaded < totalItems) {
            // Calculate next page to load
            const nextPage = Math.floor(totalLoaded / options.pageSize);
            if (!state.loadedPages.includes(nextPage) && !state.loading) {
                loadPage(loader, nextPage);
            }
        }
    }

    /**
     * Updates the layout for windowed scrolling
     * @param {Object} loader - The loader to update
     */
    function updateWindowedLayout(loader) {
        const { state, innerContainer } = loader;

        // Skip if not using windowed scroll
        if (loader.options.strategy !== LOADING_STRATEGIES.WINDOWED) {
            return;
        }

        // Update positions of all visible items
        const items = innerContainer.querySelectorAll('.Stashy-virtual-item');
        let currentTop = 0;

        items.forEach(item => {
            const index = parseInt(item.dataset.index, 10);

            // Position the item
            item.style.top = `${currentTop}px`;

            // Update current top position
            const height = state.itemHeights[index] || loader.options.itemHeight;
            currentTop += height;
        });

        // Update container height
        updateContainerHeight(loader);
    }

    /**
     * Updates the container height for virtual scrolling
     * @param {Object} loader - The loader to update
     */
    function updateContainerHeight(loader) {
        const { options, state, innerContainer } = loader;

        // Calculate total height
        let totalHeight = 0;

        if (options.strategy === LOADING_STRATEGIES.VIRTUAL_SCROLL) {
            // Fixed height items
            totalHeight = state.allItems.length * options.itemHeight;
        } else if (options.strategy === LOADING_STRATEGIES.WINDOWED) {
            // Variable height items
            if (state.itemHeights.length > 0) {
                // Use known heights
                totalHeight = state.itemHeights.reduce((sum, height) => sum + (height || options.itemHeight), 0);
            } else {
                // Estimate based on default height
                totalHeight = state.allItems.length * options.itemHeight;
            }
        }

        // Set container height
        innerContainer.style.height = `${totalHeight}px`;
    }

    /**
     * Updates pagination controls
     * @param {Object} loader - The loader to update
     */
    function updatePaginationControls(loader) {
        const { state, prevButton, nextButton, pageIndicator, options } = loader;

        if (!prevButton || !nextButton || !pageIndicator) {
            return;
        }

        // Update previous button
        prevButton.disabled = state.currentPage === 0;

        // Update next button
        const hasMorePages = options.totalItems
            ? (state.currentPage + 1) * options.pageSize < options.totalItems
            : state.allItems.length === options.pageSize;
        nextButton.disabled = !hasMorePages;

        // Update page indicator
        const totalPages = options.totalItems
            ? Math.ceil(options.totalItems / options.pageSize)
            : state.currentPage + (hasMorePages ? 2 : 1);
        pageIndicator.textContent = `Page ${state.currentPage + 1} of ${totalPages}`;
    }

    /**
     * Loads the next page of data
     * @param {Object} loader - The loader to load data for
     * @returns {Promise<Array>} A promise that resolves with the loaded items
     */
    function loadNextPage(loader) {
        return loadPage(loader, loader.state.currentPage + 1);
    }

    /**
     * Shows or hides the loading indicator
     * @param {Object} loader - The loader to update
     * @param {boolean} show - Whether to show the loading indicator
     */
    function showLoading(loader, show) {
        if (loader.loadingIndicator) {
            loader.loadingIndicator.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * Gets a cache key for the loader and page
     * @param {Object} loader - The loader
     * @param {number} page - The page number
     * @returns {string} The cache key
     */
    function getCacheKey(loader, page) {
        return `${loader.id}_${loader.options.source}_${page}_${loader.options.pageSize}`;
    }

    /**
     * Loads data from a source
     * @param {string} source - The data source
     * @param {number} page - The page number
     * @param {number} pageSize - The page size
     * @param {Object} options - Source-specific options
     * @returns {Promise<Array>} A promise that resolves with the loaded items
     */
    async function loadDataFromSource(source, page, pageSize, options = {}) {
        try {
            // Calculate offset
            const offset = page * pageSize;

            // Load data based on source
            switch (source) {
                case DATA_SOURCES.HIGHLIGHTS:
                    if (window.StashyIndexedDB) {
                        return await window.StashyIndexedDB.getHighlights({
                            offset,
                            limit: pageSize,
                            ...options
                        });
                    }
                    break;

                case DATA_SOURCES.NOTES:
                    if (window.StashyIndexedDB) {
                        return await window.StashyIndexedDB.getNotes({
                            offset,
                            limit: pageSize,
                            ...options
                        });
                    }
                    break;

                case DATA_SOURCES.METADATA:
                    // Metadata doesn't support pagination directly
                    if (window.StashyIndexedDB) {
                        const key = options.key;
                        if (key) {
                            const metadata = await window.StashyIndexedDB.getMetadata(key);
                            return metadata ? [metadata] : [];
                        }
                    }
                    break;

                case DATA_SOURCES.CACHE:
                    // Cache doesn't support pagination directly
                    // This is a placeholder for future implementation
                    return [];

                case DATA_SOURCES.CUSTOM:
                    // For custom data sources, the loadData function should be provided
                    return [];

                default:
                    throw new Error(`Unknown data source: ${source}`);
            }

            return [];
        } catch (error) {
            console.error(`Stashy: Error loading data from source ${source}:`, error);
            throw error;
        }
    }

    // Return the public API
    return {
        // Core operations
        createLoader,
        getLoader: (elementId) => dataLoaders[elementId] || null,
        refreshLoader: (elementId) => {
            const loader = dataLoaders[elementId];
            if (loader) {
                loadInitialData(loader);
            }
        },
        destroyLoader: (elementId) => {
            const loader = dataLoaders[elementId];
            if (loader) {
                // Clean up event listeners
                if (loader.resizeObserver) {
                    loader.resizeObserver.disconnect();
                }

                // Remove from registry
                delete dataLoaders[elementId];

                return true;
            }
            return false;
        },

        // Data operations
        loadPage: (elementId, page) => {
            const loader = dataLoaders[elementId];
            if (loader) {
                return loadPage(loader, page);
            }
            return Promise.reject(new Error(`Loader with ID "${elementId}" not found`));
        },
        loadNextPage: (elementId) => {
            const loader = dataLoaders[elementId];
            if (loader) {
                return loadNextPage(loader);
            }
            return Promise.reject(new Error(`Loader with ID "${elementId}" not found`));
        },

        // Constants
        LOADING_STRATEGIES,
        DATA_SOURCES,

        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stashy: Virtual Data Loader Loaded");
