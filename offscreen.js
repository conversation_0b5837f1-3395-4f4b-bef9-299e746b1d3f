// --- START OF FILE offscreen.js ---
console.log("Offscreen document loaded.");

// --- Replicated Canvas Drawing Helpers (from background.js) ---





// --- Message Listener ---
chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
    console.log("Offscreen received message:", message.action, message);

    // Validate the sender is from our extension
    if (sender.id !== chrome.runtime.id) {
        console.warn("Offscreen: Rejected message from unauthorized sender:", sender.id);
        sendResponse({ success: false, error: "Unauthorized sender" });
        return false;
    }

    // Check if message is targeted for offscreen document
    if (message.target === 'offscreen' || !message.target) {
        // No overlay processing functionality
    }

    // Handle other potential actions if needed
    return false; // Indicate synchronous response if action not handled
});

// --- END OF FILE offscreen.js ---