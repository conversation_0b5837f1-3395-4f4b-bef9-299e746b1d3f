<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Stashy Screenshot Settings</title>
    <link rel="stylesheet" href="screenshot-settings.css">
</head>
<body>
    <div class="container">
        <h1>Screenshot Settings</h1>

        <div class="form-group">
            <label for="storage-location">Storage Location</label>
            <select id="storage-location">
                <option value="local">Local Only</option>
                <option value="drive">Google Drive Only</option>
                <option value="both">Both Local and Google Drive</option>
            </select>
            <p class="info-text">Choose where your screenshots will be saved.</p>
        </div>

        <div id="drive-settings" class="hidden">
            <div class="form-group">
                <label for="drive-folder-name">Google Drive Folder Name</label>
                <input type="text" id="drive-folder-name" value="Stashy Screenshots">
                <p class="info-text">Name of the folder where screenshots will be saved in Google Drive.</p>
            </div>

            <div class="form-group checkbox-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="use-app-data-folder" checked>
                    Use private folder (not visible in Drive UI)
                </label>
                <p class="info-text">When enabled, screenshots are saved to a private application folder that isn't visible in the Google Drive interface.</p>
            </div>

            <div class="form-group">
                <label for="auto-delete-after">Auto-Delete After (Days)</label>
                <input type="number" id="auto-delete-after" min="0" value="0">
                <p class="info-text">Number of days after which screenshots will be automatically deleted from Google Drive. Use 0 for never.</p>
            </div>

            <div class="form-group checkbox-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="ask-before-upload" checked>
                    Ask before uploading to Google Drive
                </label>
                <p class="info-text">When enabled, you'll be asked for confirmation before each screenshot is uploaded to Google Drive.</p>
            </div>
        </div>

        <div class="buttons">
            <button id="cancel-btn" class="secondary">Cancel</button>
            <button id="save-btn" class="primary">Save Settings</button>
        </div>
    </div>

    <script src="screenshot-settings.js"></script>
</body>
</html>
