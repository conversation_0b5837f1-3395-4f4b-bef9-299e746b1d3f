# Stashy Payment Integration Guide

This guide explains how to test and use the payment integration system between your Stashy Chrome Extension and stashyapp.com website.

## 🚀 Quick Start

### 1. Load the Extension
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked" and select your Stashy extension folder
4. The extension should now be loaded and active

### 2. Test the Integration
1. Open the `test-payment-integration.html` file in Chrome
2. Follow the step-by-step tests on the page
3. Check the debug log for detailed information

## 🔧 How It Works

### Architecture Overview
```
Website (stashyapp.com) ←→ content-for-website.js ←→ background.js ←→ Your Backend API
```

### Key Components

#### 1. **content-for-website.js**
- Secure bridge between your website and extension
- Listens for custom events from your website
- Forwards requests to background script
- Sends responses back to website

#### 2. **background.js Premium Functions**
- `getUserInfo()` - Gets user's Google ID and email
- `checkPremiumStatus()` - Checks premium status from your backend
- `refreshPremiumStatus()` - Forces cache refresh

#### 3. **lib/premium-manager.js**
- Manages feature locking/unlocking
- Handles usage counters for free tier
- Provides UI styling functions

## 🌐 Website Integration

### Required Events Your Website Should Dispatch

#### Check Extension Installation
```javascript
document.dispatchEvent(new CustomEvent('stashyCheckExtension'));
```

#### Get User Authentication Info
```javascript
document.dispatchEvent(new CustomEvent('stashyGetUserInfo'));
```

#### Check Premium Status
```javascript
document.dispatchEvent(new CustomEvent('stashyGetPremiumStatus'));
```

### Listen for Responses

#### Extension Detection Response
```javascript
document.addEventListener('stashyExtensionResponse', (event) => {
    const { installed, version, name } = event.detail;
    // Handle response
});
```

#### User Info Response
```javascript
document.addEventListener('stashyUserInfoResponse', (event) => {
    const { success, userId, email, error } = event.detail;
    if (success) {
        // Use userId for payment processing
    }
});
```

#### Premium Status Response
```javascript
document.addEventListener('stashyPremiumStatusResponse', (event) => {
    const { success, isPremium, expiryDate, error } = event.detail;
    // Handle premium status
});
```

## 💳 Payment Flow Integration

### Step 1: Get User ID
```javascript
// Your website's pricing.html page
document.dispatchEvent(new CustomEvent('stashyGetUserInfo'));

document.addEventListener('stashyUserInfoResponse', (event) => {
    if (event.detail.success) {
        const userId = event.detail.userId;
        // Proceed with payment using this userId
        initiatePayment(userId);
    }
});
```

### Step 2: Process Payment
Use the `userId` with your existing Razorpay integration to process the payment.

### Step 3: Update Backend
After successful payment, update the user's premium status in your Firestore database.

### Step 4: Refresh Extension Status
The extension will automatically refresh its premium status cache every 5 minutes, or you can trigger an immediate refresh.

## 🔒 Premium Features

### Features That Require Premium
- All AI features (analysis, summarization, etc.)
- Export functions (PDF, Word, HTML)
- Google Drive sync and backup
- Advanced templates and template builder
- Advanced highlighting styles
- Unlimited notes and notebooks

### Free Tier Limitations
- Maximum 10 notes
- Maximum 2 notebooks
- Maximum 5 highlights per page
- No AI features
- No exports
- No Google Drive sync

## 🎨 UI Integration

### Premium Status Indicators
The extension automatically adds premium status indicators to:
- Popup interface
- Content script toolbars
- AI feature buttons
- Export buttons

### CSS Classes Applied
- `stashy-premium-user` - Applied to body when user is premium
- `stashy-free-user` - Applied to body when user is free
- `premium-locked` - Applied to locked features
- `premium-available` - Applied to available features

## 🧪 Testing Checklist

### Basic Integration
- [ ] Extension loads without errors
- [ ] content-for-website.js is injected on your website
- [ ] Extension responds to `stashyCheckExtension` event
- [ ] User authentication works (`stashyGetUserInfo`)
- [ ] Premium status check works (`stashyGetPremiumStatus`)

### Premium Features
- [ ] AI features show premium indicators
- [ ] Free users see upgrade prompts
- [ ] Premium users can access all features
- [ ] Usage counters work for free tier
- [ ] Premium status updates after payment

### UI/UX
- [ ] Premium status shows in popup
- [ ] Locked features have visual indicators
- [ ] Upgrade buttons link to correct URLs
- [ ] Premium badges display correctly

## 🐛 Troubleshooting

### Common Issues

#### Extension Not Detected
- Check if extension is loaded in `chrome://extensions/`
- Verify content script is injected on your website
- Check browser console for errors

#### User Authentication Fails
- Ensure user is signed into Google in their browser
- Check if identity permission is granted
- Verify OAuth2 configuration in manifest.json

#### Premium Status Not Updating
- Check network requests to your backend API
- Verify CORS settings on your backend
- Check if API endpoint is accessible

#### Features Not Locking/Unlocking
- Verify premium-manager.js is loaded
- Check if premium status is being applied to UI
- Ensure CSS classes are being added correctly

## 📝 Backend API Requirements

Your backend should provide:

### Endpoint: `/checkStatus/{userId}`
```json
{
  "premium": boolean,
  "expiryDate": "2024-12-31T23:59:59Z" // Optional
}
```

### CORS Headers Required
```
Access-Control-Allow-Origin: chrome-extension://your-extension-id
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

## 🔄 Next Steps

1. Test the integration using the provided test file
2. Update your website's pricing page to use the extension bridge
3. Test the complete payment flow
4. Deploy and monitor the integration

## 📞 Support

If you encounter issues:
1. Check the browser console for errors
2. Review the debug log in the test page
3. Verify all files are properly loaded
4. Test with a fresh browser profile

The integration is designed to be secure, efficient, and user-friendly. The extension handles all the complex authentication and premium status management, while your website just needs to dispatch simple events and listen for responses.
