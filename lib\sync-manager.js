/**
 * Stashy Sync Manager
 * Provides background synchronization to ensure data consistency
 */

// Create a namespace to avoid global pollution
window.StashySyncManager = (function() {
    // Sync status constants
    const SYNC_STATUS = {
        IDLE: 'idle',
        SYNCING: 'syncing',
        COMPLETED: 'completed',
        FAILED: 'failed',
        CONFLICT: 'conflict'
    };

    // Sync operation types
    const SYNC_OPERATIONS = {
        READ: 'read',
        WRITE: 'write',
        DELETE: 'delete'
    };

    // Configuration
    const config = {
        enabled: true,                  // Enable synchronization
        syncInterval: 5 * 60 * 1000,    // Sync every 5 minutes
        maxRetries: 3,                  // Maximum number of retry attempts
        retryDelay: 5000,               // Delay between retries (ms)
        batchSize: 50,                  // Number of items to sync in a batch
        prioritizeRecent: true,         // Prioritize recently modified items
        conflictResolution: 'newest',   // How to resolve conflicts: 'newest', 'local', 'remote'
        debug: false                    // Debug mode
    };

    // Private variables
    let syncTimer = null;
    let syncQueue = [];
    let syncInProgress = false;
    let lastSyncTime = 0;
    let syncStats = {
        lastSync: null,
        totalSyncs: 0,
        successfulSyncs: 0,
        failedSyncs: 0,
        itemsSynced: 0,
        conflicts: 0
    };

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StashySyncManager]', ...args);
        }
    }

    /**
     * Initializes the sync manager
     * @returns {Promise<boolean>} A promise that resolves with true if initialization was successful
     */
    async function init() {
        try {
            // Load sync stats from storage
            const stats = await loadSyncStats();
            if (stats) {
                syncStats = { ...syncStats, ...stats };
                lastSyncTime = syncStats.lastSync ? syncStats.lastSync.timestamp : 0;
                debugLog('Loaded sync stats:', syncStats);
            }

            // Start sync timer if enabled
            if (config.enabled) {
                startSyncTimer();
            }

            return true;
        } catch (error) {
            console.error('Stashy: Error initializing sync manager:', error);
            return false;
        }
    }

    /**
     * Loads sync stats from storage
     * @returns {Promise<Object|null>} A promise that resolves with the sync stats or null
     */
    async function loadSyncStats() {
        try {
            if (window.StashyIndexedDB) {
                const stats = await window.StashyIndexedDB.getMetadata('sync_stats');
                return stats || null;
            }
            return null;
        } catch (error) {
            console.error('Stashy: Error loading sync stats:', error);
            return null;
        }
    }

    /**
     * Saves sync stats to storage
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function saveSyncStats() {
        try {
            if (window.StashyIndexedDB) {
                await window.StashyIndexedDB.setMetadata({
                    key: 'sync_stats',
                    data: syncStats,
                    lastModified: Date.now()
                });
                return true;
            }
            return false;
        } catch (error) {
            console.error('Stashy: Error saving sync stats:', error);
            return false;
        }
    }

    /**
     * Starts the sync timer
     */
    function startSyncTimer() {
        if (syncTimer) {
            clearInterval(syncTimer);
        }

        syncTimer = setInterval(() => {
            if (!syncInProgress) {
                syncAll().catch(error => {
                    console.error('Stashy: Error during scheduled sync:', error);
                });
            }
        }, config.syncInterval);

        debugLog(`Started sync timer (interval: ${config.syncInterval}ms)`);
    }

    /**
     * Stops the sync timer
     */
    function stopSyncTimer() {
        if (syncTimer) {
            clearInterval(syncTimer);
            syncTimer = null;
            debugLog('Stopped sync timer');
        }
    }

    /**
     * Adds an item to the sync queue
     * @param {Object} item - The item to sync
     * @returns {string} The sync item ID
     */
    function queueForSync(item) {
        if (!config.enabled) return null;

        const syncItem = {
            id: `sync-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
            item: item,
            operation: item.operation || SYNC_OPERATIONS.WRITE,
            timestamp: Date.now(),
            attempts: 0,
            status: SYNC_STATUS.IDLE
        };

        syncQueue.push(syncItem);
        debugLog(`Added item to sync queue: ${syncItem.id}`);

        // If we're not currently syncing, schedule a sync
        if (!syncInProgress && syncQueue.length === 1) {
            setTimeout(() => {
                syncAll().catch(error => {
                    console.error('Stashy: Error during queued sync:', error);
                });
            }, 1000);
        }

        return syncItem.id;
    }

    /**
     * Removes an item from the sync queue
     * @param {string} id - The sync item ID
     * @returns {boolean} True if the item was removed
     */
    function removeFromSyncQueue(id) {
        const initialLength = syncQueue.length;
        syncQueue = syncQueue.filter(item => item.id !== id);
        const removed = syncQueue.length < initialLength;
        
        if (removed) {
            debugLog(`Removed item from sync queue: ${id}`);
        }
        
        return removed;
    }

    /**
     * Synchronizes all items in the queue
     * @returns {Promise<Object>} A promise that resolves with the sync results
     */
    async function syncAll() {
        if (syncInProgress) {
            debugLog('Sync already in progress, skipping');
            return { success: false, error: 'Sync already in progress' };
        }

        syncInProgress = true;
        debugLog('Starting sync');

        const results = {
            success: true,
            startTime: Date.now(),
            endTime: null,
            itemsProcessed: 0,
            itemsSucceeded: 0,
            itemsFailed: 0,
            conflicts: 0,
            errors: []
        };

        try {
            // Sort queue by priority if enabled
            if (config.prioritizeRecent) {
                syncQueue.sort((a, b) => b.timestamp - a.timestamp);
            }

            // Process queue in batches
            for (let i = 0; i < syncQueue.length; i += config.batchSize) {
                const batch = syncQueue.slice(i, i + config.batchSize);
                
                // Process each item in the batch
                for (const syncItem of batch) {
                    try {
                        syncItem.status = SYNC_STATUS.SYNCING;
                        syncItem.attempts++;
                        
                        // Process based on operation type
                        let itemResult;
                        switch (syncItem.operation) {
                            case SYNC_OPERATIONS.READ:
                                itemResult = await syncReadOperation(syncItem);
                                break;
                                
                            case SYNC_OPERATIONS.WRITE:
                                itemResult = await syncWriteOperation(syncItem);
                                break;
                                
                            case SYNC_OPERATIONS.DELETE:
                                itemResult = await syncDeleteOperation(syncItem);
                                break;
                                
                            default:
                                throw new Error(`Unknown sync operation: ${syncItem.operation}`);
                        }
                        
                        // Handle result
                        results.itemsProcessed++;
                        
                        if (itemResult.success) {
                            syncItem.status = SYNC_STATUS.COMPLETED;
                            results.itemsSucceeded++;
                            removeFromSyncQueue(syncItem.id);
                        } else if (itemResult.conflict) {
                            syncItem.status = SYNC_STATUS.CONFLICT;
                            results.conflicts++;
                            
                            // Handle conflict based on configuration
                            const resolved = await resolveConflict(syncItem, itemResult);
                            if (resolved.success) {
                                removeFromSyncQueue(syncItem.id);
                            }
                        } else {
                            syncItem.status = SYNC_STATUS.FAILED;
                            syncItem.error = itemResult.error;
                            results.itemsFailed++;
                            
                            // Retry if we haven't exceeded max attempts
                            if (syncItem.attempts >= config.maxRetries) {
                                removeFromSyncQueue(syncItem.id);
                                results.errors.push({
                                    id: syncItem.id,
                                    error: itemResult.error,
                                    item: syncItem.item
                                });
                            }
                        }
                    } catch (error) {
                        console.error(`Stashy: Error syncing item ${syncItem.id}:`, error);
                        syncItem.status = SYNC_STATUS.FAILED;
                        syncItem.error = error.message;
                        results.itemsFailed++;
                        
                        // Retry if we haven't exceeded max attempts
                        if (syncItem.attempts >= config.maxRetries) {
                            removeFromSyncQueue(syncItem.id);
                            results.errors.push({
                                id: syncItem.id,
                                error: error.message,
                                item: syncItem.item
                            });
                        }
                    }
                }
            }

            // Update sync stats
            results.endTime = Date.now();
            syncStats.lastSync = {
                timestamp: results.endTime,
                duration: results.endTime - results.startTime,
                itemsProcessed: results.itemsProcessed,
                itemsSucceeded: results.itemsSucceeded,
                itemsFailed: results.itemsFailed,
                conflicts: results.conflicts
            };
            syncStats.totalSyncs++;
            syncStats.successfulSyncs += results.success ? 1 : 0;
            syncStats.failedSyncs += results.success ? 0 : 1;
            syncStats.itemsSynced += results.itemsSucceeded;
            syncStats.conflicts += results.conflicts;
            
            // Save sync stats
            await saveSyncStats();
            
            debugLog('Sync completed', results);
            return results;
        } catch (error) {
            console.error('Stashy: Error during sync:', error);
            
            results.success = false;
            results.endTime = Date.now();
            results.error = error.message;
            
            // Update sync stats
            syncStats.totalSyncs++;
            syncStats.failedSyncs++;
            syncStats.lastSync = {
                timestamp: results.endTime,
                duration: results.endTime - results.startTime,
                error: error.message
            };
            
            // Save sync stats
            await saveSyncStats();
            
            return results;
        } finally {
            syncInProgress = false;
            lastSyncTime = Date.now();
        }
    }

    /**
     * Synchronizes a read operation
     * @param {Object} syncItem - The sync item
     * @returns {Promise<Object>} A promise that resolves with the operation result
     */
    async function syncReadOperation(syncItem) {
        // This is a placeholder for actual read sync logic
        // In a real implementation, you would:
        // 1. Read data from the remote source
        // 2. Compare with local data
        // 3. Update local data if needed
        
        return {
            success: true,
            operation: SYNC_OPERATIONS.READ,
            item: syncItem.item
        };
    }

    /**
     * Synchronizes a write operation
     * @param {Object} syncItem - The sync item
     * @returns {Promise<Object>} A promise that resolves with the operation result
     */
    async function syncWriteOperation(syncItem) {
        // This is a placeholder for actual write sync logic
        // In a real implementation, you would:
        // 1. Check if the item exists remotely
        // 2. Compare timestamps to detect conflicts
        // 3. Write the item to the remote source
        
        return {
            success: true,
            operation: SYNC_OPERATIONS.WRITE,
            item: syncItem.item
        };
    }

    /**
     * Synchronizes a delete operation
     * @param {Object} syncItem - The sync item
     * @returns {Promise<Object>} A promise that resolves with the operation result
     */
    async function syncDeleteOperation(syncItem) {
        // This is a placeholder for actual delete sync logic
        // In a real implementation, you would:
        // 1. Check if the item exists remotely
        // 2. Delete the item from the remote source
        
        return {
            success: true,
            operation: SYNC_OPERATIONS.DELETE,
            item: syncItem.item
        };
    }

    /**
     * Resolves a sync conflict
     * @param {Object} syncItem - The sync item
     * @param {Object} conflictInfo - Information about the conflict
     * @returns {Promise<Object>} A promise that resolves with the resolution result
     */
    async function resolveConflict(syncItem, conflictInfo) {
        // This is a placeholder for actual conflict resolution logic
        // In a real implementation, you would:
        // 1. Compare local and remote versions
        // 2. Apply the resolution strategy
        // 3. Update the appropriate version
        
        let resolution;
        switch (config.conflictResolution) {
            case 'newest':
                // Use the newest version based on timestamp
                resolution = conflictInfo.localTimestamp > conflictInfo.remoteTimestamp ? 'local' : 'remote';
                break;
                
            case 'local':
                // Always use local version
                resolution = 'local';
                break;
                
            case 'remote':
                // Always use remote version
                resolution = 'remote';
                break;
                
            default:
                resolution = 'newest';
        }
        
        debugLog(`Resolved conflict for ${syncItem.id} using strategy: ${resolution}`);
        
        return {
            success: true,
            resolution,
            item: syncItem.item
        };
    }

    /**
     * Gets the current sync status
     * @returns {Object} The current sync status
     */
    function getSyncStatus() {
        return {
            enabled: config.enabled,
            inProgress: syncInProgress,
            queueLength: syncQueue.length,
            lastSyncTime,
            stats: { ...syncStats }
        };
    }

    // Initialize the sync manager when loaded
    init().catch(error => {
        console.error('Stashy: Failed to initialize sync manager:', error);
    });

    // Return the public API
    return {
        // Core sync operations
        syncAll,
        queueForSync,
        removeFromSyncQueue,
        getSyncStatus,

        // Constants
        SYNC_STATUS,
        SYNC_OPERATIONS,

        // Configuration
        updateConfig: (newConfig) => {
            const oldEnabled = config.enabled;
            Object.assign(config, newConfig);
            
            // Handle timer state changes
            if (!oldEnabled && config.enabled) {
                startSyncTimer();
            } else if (oldEnabled && !config.enabled) {
                stopSyncTimer();
            }
            
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stashy: Sync Manager Loaded");
