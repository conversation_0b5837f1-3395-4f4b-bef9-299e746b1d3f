/**
 * Stashy TTL Manager
 * Provides advanced TTL (Time-To-Live) management for cached data
 */

// Create a namespace to avoid global pollution
window.StashyTTLManager = (function() {
    // Constants for TTL categories
    const TTL_CATEGORIES = {
        VOLATILE: 'volatile',       // Very short-lived data (e.g., UI state)
        TEMPORARY: 'temporary',     // Short-lived data (e.g., search results)
        STANDARD: 'standard',       // Normal cache items
        PERSISTENT: 'persistent',   // Long-lived data (e.g., user preferences)
        PERMANENT: 'permanent'      // Data that doesn't expire automatically
    };

    // Default TTL values (in milliseconds)
    const DEFAULT_TTL = {
        [TTL_CATEGORIES.VOLATILE]: 60 * 1000,             // 1 minute
        [TTL_CATEGORIES.TEMPORARY]: 5 * 60 * 1000,        // 5 minutes
        [TTL_CATEGORIES.STANDARD]: 30 * 60 * 1000,        // 30 minutes
        [TTL_CATEGORIES.PERSISTENT]: 24 * 60 * 60 * 1000, // 24 hours
        [TTL_CATEGORIES.PERMANENT]: 30 * 24 * 60 * 60 * 1000 // 30 days (effectively permanent)
    };

    // TTL adjustment factors based on usage patterns
    const USAGE_FACTORS = {
        FREQUENT: 1.5,    // Extend TTL for frequently accessed items
        NORMAL: 1.0,      // No adjustment
        RARE: 0.8         // Reduce TTL for rarely accessed items
    };

    // Configuration
    const config = {
        adaptiveTTL: true,         // Adjust TTL based on usage patterns
        usageThresholds: {
            frequent: 5,            // Access count to be considered frequent
            rare: 1                 // Access count to be considered rare
        },
        maxExtensionFactor: 3,      // Maximum TTL extension factor
        debug: false                // Debug mode
    };

    // Private variables
    const usageStats = {};          // Track item usage: { key: { accessCount, lastAccessed } }

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StashyTTLManager]', ...args);
        }
    }

    /**
     * Calculates the TTL for an item based on its category and usage
     * @param {string} category - The TTL category
     * @param {string} key - The cache key
     * @returns {number} The calculated TTL in milliseconds
     */
    function calculateTTL(category, key) {
        // Get the base TTL for the category
        const baseTTL = DEFAULT_TTL[category] || DEFAULT_TTL[TTL_CATEGORIES.STANDARD];

        // If adaptive TTL is disabled, return the base TTL
        if (!config.adaptiveTTL) {
            return baseTTL;
        }

        // Get usage statistics for the key
        const usage = usageStats[key] || { accessCount: 0, lastAccessed: 0 };

        // Determine the usage factor
        let usageFactor = USAGE_FACTORS.NORMAL;
        if (usage.accessCount >= config.usageThresholds.frequent) {
            usageFactor = USAGE_FACTORS.FREQUENT;
        } else if (usage.accessCount <= config.usageThresholds.rare) {
            usageFactor = USAGE_FACTORS.RARE;
        }

        // Calculate the adjusted TTL
        const adjustedTTL = baseTTL * usageFactor;

        // Ensure we don't exceed the maximum extension
        const maxTTL = baseTTL * config.maxExtensionFactor;
        const finalTTL = Math.min(adjustedTTL, maxTTL);

        debugLog(`Calculated TTL for ${key} (${category}): ${finalTTL}ms (base: ${baseTTL}ms, factor: ${usageFactor})`);

        return finalTTL;
    }

    /**
     * Records an access to a cached item
     * @param {string} key - The cache key
     */
    function recordAccess(key) {
        if (!config.adaptiveTTL) return;

        // Initialize or update usage stats for the key
        if (!usageStats[key]) {
            usageStats[key] = {
                accessCount: 0,
                lastAccessed: 0
            };
        }

        // Update access count and timestamp
        usageStats[key].accessCount++;
        usageStats[key].lastAccessed = Date.now();

        debugLog(`Recorded access for ${key}, count: ${usageStats[key].accessCount}`);
    }

    /**
     * Clears usage statistics for a key or all keys
     * @param {string} [key] - Optional key to clear stats for
     */
    function clearUsageStats(key) {
        if (key) {
            delete usageStats[key];
            debugLog(`Cleared usage stats for ${key}`);
        } else {
            Object.keys(usageStats).forEach(k => delete usageStats[k]);
            debugLog('Cleared all usage stats');
        }
    }

    // Return the public API
    return {
        // Core TTL operations
        calculateTTL,
        recordAccess,
        clearUsageStats,

        // Constants
        TTL_CATEGORIES,
        DEFAULT_TTL,
        USAGE_FACTORS,

        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stashy: TTL Manager Loaded");
