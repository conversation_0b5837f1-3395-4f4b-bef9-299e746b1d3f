/**
 * Stashy Conflict Resolver
 * Provides sophisticated conflict resolution for data synchronization
 */

// Create a namespace to avoid global pollution
window.StashyConflictResolver = (function() {
    // Conflict resolution strategies
    const STRATEGIES = {
        NEWEST_WINS: 'newest_wins',         // Use the most recently modified version
        LOCAL_WINS: 'local_wins',           // Always use the local version
        REMOTE_WINS: 'remote_wins',         // Always use the remote version
        MERGE: 'merge',                     // Merge the changes when possible
        MANUAL: 'manual',                   // Let the user decide
        KEEP_BOTH: 'keep_both'              // Keep both versions
    };

    // Conflict types
    const CONFLICT_TYPES = {
        TIMESTAMP: 'timestamp',             // Conflicting timestamps
        CONTENT: 'content',                 // Conflicting content
        DELETION: 'deletion',               // One version is deleted
        SCHEMA: 'schema',                   // Schema differences
        UNKNOWN: 'unknown'                  // Unknown conflict type
    };

    // Configuration
    const config = {
        defaultStrategy: STRATEGIES.MERGE,  // Default resolution strategy
        fallbackStrategy: STRATEGIES.NEWEST_WINS, // Fallback if merge fails
        autoResolveThreshold: 5 * 60 * 1000, // Auto-resolve if time difference < 5 min
        mergeFieldStrategy: {               // How to merge specific fields
            text: 'concatenate',            // Concatenate text fields
            tags: 'union',                  // Union of tags
            metadata: 'newest',             // Use newest metadata
            position: 'newest'              // Use newest position
        },
        preserveHistory: true,              // Keep history of conflicts
        debug: false                        // Debug mode (disabled for production)
    };

    // Private variables
    let conflictHistory = [];
    const MAX_HISTORY = 100;

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StashyConflictResolver]', ...args);
        }
    }

    /**
     * Detects conflicts between two versions of the same item
     * @param {Object} localVersion - The local version
     * @param {Object} remoteVersion - The remote version
     * @returns {Object} The conflict information
     */
    function detectConflict(localVersion, remoteVersion) {
        // Check if both versions exist
        if (!localVersion || !remoteVersion) {
            return {
                hasConflict: true,
                type: CONFLICT_TYPES.DELETION,
                description: !localVersion ? 'Local version deleted' : 'Remote version deleted'
            };
        }

        // Check timestamps
        const localTime = localVersion.lastModified || localVersion.timestamp || 0;
        const remoteTime = remoteVersion.lastModified || remoteVersion.timestamp || 0;
        const timeDiff = Math.abs(localTime - remoteTime);

        // Check content differences
        const contentDifferences = findContentDifferences(localVersion, remoteVersion);
        const hasContentConflict = contentDifferences.length > 0;

        // Determine conflict type
        let conflictType = CONFLICT_TYPES.UNKNOWN;
        if (timeDiff > 0) {
            conflictType = CONFLICT_TYPES.TIMESTAMP;
        }
        if (hasContentConflict) {
            conflictType = CONFLICT_TYPES.CONTENT;
        }

        // Check if there's actually a conflict
        const hasConflict = hasContentConflict || timeDiff > config.autoResolveThreshold;

        return {
            hasConflict,
            type: conflictType,
            timeDiff,
            contentDifferences,
            localTime,
            remoteTime,
            newerVersion: localTime > remoteTime ? 'local' : 'remote',
            description: generateConflictDescription(conflictType, contentDifferences, localTime, remoteTime)
        };
    }

    /**
     * Finds content differences between two versions
     * @param {Object} localVersion - The local version
     * @param {Object} remoteVersion - The remote version
     * @returns {Array} Array of differences
     */
    function findContentDifferences(localVersion, remoteVersion) {
        const differences = [];

        // Compare important fields
        const fieldsToCompare = [
            'text', 'title', 'tags', 'color', 'position', 'selection',
            'notebookId', 'isArchived', 'isPinned'
        ];

        for (const field of fieldsToCompare) {
            if (localVersion[field] !== undefined || remoteVersion[field] !== undefined) {
                // Handle array fields
                if (Array.isArray(localVersion[field]) || Array.isArray(remoteVersion[field])) {
                    const localArray = Array.isArray(localVersion[field]) ? localVersion[field] : [];
                    const remoteArray = Array.isArray(remoteVersion[field]) ? remoteVersion[field] : [];

                    // Compare arrays
                    if (JSON.stringify(localArray) !== JSON.stringify(remoteArray)) {
                        differences.push({
                            field,
                            local: localArray,
                            remote: remoteArray,
                            type: 'array'
                        });
                    }
                }
                // Handle object fields
                else if (typeof localVersion[field] === 'object' || typeof remoteVersion[field] === 'object') {
                    const localObj = typeof localVersion[field] === 'object' ? localVersion[field] : {};
                    const remoteObj = typeof remoteVersion[field] === 'object' ? remoteVersion[field] : {};

                    // Compare objects
                    if (JSON.stringify(localObj) !== JSON.stringify(remoteObj)) {
                        differences.push({
                            field,
                            local: localObj,
                            remote: remoteObj,
                            type: 'object'
                        });
                    }
                }
                // Handle primitive fields
                else if (localVersion[field] !== remoteVersion[field]) {
                    differences.push({
                        field,
                        local: localVersion[field],
                        remote: remoteVersion[field],
                        type: typeof localVersion[field]
                    });
                }
            }
        }

        return differences;
    }

    /**
     * Generates a human-readable description of the conflict
     * @param {string} conflictType - The type of conflict
     * @param {Array} contentDifferences - The content differences
     * @param {number} localTime - The local timestamp
     * @param {number} remoteTime - The remote timestamp
     * @returns {string} The conflict description
     */
    function generateConflictDescription(conflictType, contentDifferences, localTime, remoteTime) {
        let description = '';

        switch (conflictType) {
            case CONFLICT_TYPES.TIMESTAMP:
                const timeDiff = Math.abs(localTime - remoteTime);
                const newerVersion = localTime > remoteTime ? 'local' : 'remote';
                description = `The ${newerVersion} version is ${Math.round(timeDiff / 1000)} seconds newer`;
                break;

            case CONFLICT_TYPES.CONTENT:
                const fieldNames = contentDifferences.map(diff => diff.field).join(', ');
                description = `Content differences in fields: ${fieldNames}`;
                break;

            case CONFLICT_TYPES.DELETION:
                description = 'One version has been deleted';
                break;

            case CONFLICT_TYPES.SCHEMA:
                description = 'The versions have different schemas';
                break;

            default:
                description = 'Unknown conflict type';
        }

        return description;
    }

    /**
     * Resolves a conflict using the specified strategy
     * @param {Object} localVersion - The local version
     * @param {Object} remoteVersion - The remote version
     * @param {Object} conflictInfo - Information about the conflict
     * @param {string} [strategy] - The resolution strategy to use
     * @returns {Object} The resolved item and resolution info
     */
    function resolveConflict(localVersion, remoteVersion, conflictInfo, strategy = config.defaultStrategy) {
        // If no conflict, return the local version
        if (!conflictInfo.hasConflict) {
            return {
                resolved: true,
                result: localVersion,
                strategy: 'no_conflict',
                changes: []
            };
        }

        let resolvedItem;
        let resolutionInfo = {
            resolved: true,
            strategy,
            changes: []
        };

        // Apply the resolution strategy
        switch (strategy) {
            case STRATEGIES.NEWEST_WINS:
                resolvedItem = conflictInfo.newerVersion === 'local' ? localVersion : remoteVersion;
                resolutionInfo.winningVersion = conflictInfo.newerVersion;
                break;

            case STRATEGIES.LOCAL_WINS:
                resolvedItem = localVersion;
                resolutionInfo.winningVersion = 'local';
                break;

            case STRATEGIES.REMOTE_WINS:
                resolvedItem = remoteVersion;
                resolutionInfo.winningVersion = 'remote';
                break;

            case STRATEGIES.MERGE:
                try {
                    const mergeResult = mergeVersions(localVersion, remoteVersion, conflictInfo);
                    resolvedItem = mergeResult.merged;
                    resolutionInfo.changes = mergeResult.changes;
                    resolutionInfo.mergeSuccessful = true;
                } catch (error) {
                    // Fallback to default strategy if merge fails
                    debugLog('Merge failed, falling back to', config.fallbackStrategy, error);
                    return resolveConflict(localVersion, remoteVersion, conflictInfo, config.fallbackStrategy);
                }
                break;

            case STRATEGIES.KEEP_BOTH:
                // Create a copy of the remote version with a new key
                const remoteCopy = { ...remoteVersion };
                const originalKey = remoteCopy.key;
                remoteCopy.key = `${originalKey}_remote_${Date.now()}`;
                remoteCopy.isConflictCopy = true;
                remoteCopy.originalKey = originalKey;

                // Return both versions
                resolvedItem = localVersion;
                resolutionInfo.secondaryItem = remoteCopy;
                resolutionInfo.keepBoth = true;
                break;

            case STRATEGIES.MANUAL:
                // Cannot resolve automatically
                return {
                    resolved: false,
                    strategy: STRATEGIES.MANUAL,
                    localVersion,
                    remoteVersion,
                    conflictInfo
                };

            default:
                // Unknown strategy, fall back to newest wins
                return resolveConflict(localVersion, remoteVersion, conflictInfo, STRATEGIES.NEWEST_WINS);
        }

        // Add conflict history if enabled
        if (config.preserveHistory) {
            addToConflictHistory({
                timestamp: Date.now(),
                strategy,
                conflictType: conflictInfo.type,
                contentDifferences: conflictInfo.contentDifferences,
                resolved: resolutionInfo.resolved
            });
        }

        // Add resolution metadata to the resolved item
        resolvedItem.conflictResolution = {
            timestamp: Date.now(),
            strategy,
            conflictType: conflictInfo.type
        };

        // Update the lastModified timestamp
        resolvedItem.lastModified = Date.now();

        resolutionInfo.result = resolvedItem;
        return resolutionInfo;
    }

    /**
     * Merges two versions of an item
     * @param {Object} localVersion - The local version
     * @param {Object} remoteVersion - The remote version
     * @param {Object} conflictInfo - Information about the conflict
     * @returns {Object} The merged item and changes made
     */
    function mergeVersions(localVersion, remoteVersion, conflictInfo) {
        // Start with the newer version as the base
        const baseVersion = conflictInfo.newerVersion === 'local' ? localVersion : remoteVersion;
        const otherVersion = conflictInfo.newerVersion === 'local' ? remoteVersion : localVersion;

        // Create a new object for the merged result
        const merged = { ...baseVersion };
        const changes = [];

        // Process each difference
        for (const diff of conflictInfo.contentDifferences) {
            const { field, type } = diff;
            const localValue = localVersion[field];
            const remoteValue = remoteVersion[field];

            // Skip if the field doesn't exist in one version
            if (localValue === undefined || remoteValue === undefined) {
                merged[field] = localValue !== undefined ? localValue : remoteValue;
                changes.push({
                    field,
                    action: 'use_existing',
                    value: merged[field]
                });
                continue;
            }

            // Apply field-specific merge strategy
            const fieldStrategy = config.mergeFieldStrategy[field] || 'newest';

            switch (fieldStrategy) {
                case 'concatenate':
                    // For text fields, concatenate with a separator
                    if (typeof localValue === 'string' && typeof remoteValue === 'string') {
                        merged[field] = `${localValue}\n\n---\n\n${remoteValue}`;
                        changes.push({
                            field,
                            action: 'concatenate',
                            local: localValue,
                            remote: remoteValue
                        });
                    } else {
                        // Fall back to newest for non-string fields
                        merged[field] = conflictInfo.newerVersion === 'local' ? localValue : remoteValue;
                        changes.push({
                            field,
                            action: 'use_newest',
                            value: merged[field]
                        });
                    }
                    break;

                case 'union':
                    // For arrays, take the union
                    if (Array.isArray(localValue) && Array.isArray(remoteValue)) {
                        // Use Set to get unique values
                        const unionSet = new Set([...localValue, ...remoteValue]);
                        merged[field] = Array.from(unionSet);
                        changes.push({
                            field,
                            action: 'union',
                            local: localValue,
                            remote: remoteValue,
                            result: merged[field]
                        });
                    } else {
                        // Fall back to newest for non-array fields
                        merged[field] = conflictInfo.newerVersion === 'local' ? localValue : remoteValue;
                        changes.push({
                            field,
                            action: 'use_newest',
                            value: merged[field]
                        });
                    }
                    break;

                case 'newest':
                default:
                    // Use the value from the newer version
                    merged[field] = conflictInfo.newerVersion === 'local' ? localValue : remoteValue;
                    changes.push({
                        field,
                        action: 'use_newest',
                        value: merged[field]
                    });
                    break;
            }
        }

        return { merged, changes };
    }

    /**
     * Adds an entry to the conflict history
     * @param {Object} entry - The history entry to add
     */
    function addToConflictHistory(entry) {
        // Add to history
        conflictHistory.unshift(entry);

        // Limit history size
        if (conflictHistory.length > MAX_HISTORY) {
            conflictHistory = conflictHistory.slice(0, MAX_HISTORY);
        }
    }

    // Return the public API
    return {
        // Core conflict resolution
        detectConflict,
        resolveConflict,
        mergeVersions,

        // Constants
        STRATEGIES,
        CONFLICT_TYPES,

        // History management
        getConflictHistory: () => [...conflictHistory],
        clearConflictHistory: () => { conflictHistory = []; },

        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stashy: Conflict Resolver Loaded");
