/**
 * Stashy Dashboard Performance Optimizer
 * Provides advanced performance optimizations for dashboard note loading
 * Maintains high-quality images while optimizing loading and rendering
 */

window.StashyrDashboardOptimizer = (function() {
    'use strict';

    // Configuration
    const config = {
        thumbnailMaxWidth: 300,
        thumbnailMaxHeight: 200,
        thumbnailQuality: 0.8,
        lazyLoadThreshold: '100px',
        memoryThreshold: 70, // Percentage
        batchSize: 20,
        maxConcurrentLoads: 3,
        cacheSize: 100,
        enableVirtualScrolling: true,
        enableLazyLoading: true,
        enableMemoryManagement: true
    };

    // State management
    let imageCache = new Map();
    let loadingQueue = [];
    let currentLoads = 0;
    let intersectionObserver = null;
    let memoryMonitor = null;

    /**
     * Initializes the dashboard performance optimizer
     */
    function initialize() {
        console.log('Dashboard Optimizer: Initializing performance optimizations...');
        
        setupIntersectionObserver();
        setupMemoryMonitoring();
        setupImageCache();
        
        console.log('Dashboard Optimizer: Performance optimizations initialized');
    }

    /**
     * Sets up intersection observer for lazy loading
     */
    function setupIntersectionObserver() {
        if (!config.enableLazyLoading) return;

        intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    
                    if (element.classList.contains('lazy-load-image')) {
                        loadHighQualityImage(element);
                    } else if (element.classList.contains('note-item')) {
                        optimizeNoteItem(element);
                    }
                }
            });
        }, {
            rootMargin: config.lazyLoadThreshold,
            threshold: 0.1
        });
    }

    /**
     * Sets up memory monitoring
     */
    function setupMemoryMonitoring() {
        if (!config.enableMemoryManagement || !('memory' in performance)) return;

        memoryMonitor = setInterval(() => {
            const memoryInfo = performance.memory;
            const usagePercent = (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100;
            
            if (usagePercent > config.memoryThreshold) {
                console.log(`Dashboard Optimizer: High memory usage (${usagePercent.toFixed(1)}%), optimizing...`);
                optimizeMemoryUsage();
            }
        }, 30000); // Check every 30 seconds
    }

    /**
     * Sets up image cache with LRU eviction
     */
    function setupImageCache() {
        imageCache = new Map();
    }

    /**
     * Creates optimized thumbnail for dashboard display
     * @param {string} originalImageData - Original high-quality image data
     * @returns {Promise<string>} - Thumbnail data URL
     */
    async function createOptimizedThumbnail(originalImageData) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            
            img.onload = () => {
                try {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d', { 
                        willReadFrequently: true,
                        alpha: false 
                    });

                    // Calculate optimal thumbnail dimensions
                    const { width: thumbWidth, height: thumbHeight } = calculateThumbnailDimensions(
                        img.width, 
                        img.height, 
                        config.thumbnailMaxWidth, 
                        config.thumbnailMaxHeight
                    );

                    canvas.width = thumbWidth;
                    canvas.height = thumbHeight;

                    // Enable high-quality scaling
                    ctx.imageSmoothingEnabled = true;
                    ctx.imageSmoothingQuality = 'high';

                    // Draw resized image
                    ctx.drawImage(img, 0, 0, thumbWidth, thumbHeight);

                    // Convert to optimized format
                    const thumbnailData = canvas.toDataURL('image/jpeg', config.thumbnailQuality);
                    
                    resolve(thumbnailData);
                } catch (error) {
                    console.error('Dashboard Optimizer: Error creating thumbnail:', error);
                    resolve(originalImageData); // Fallback to original
                }
            };

            img.onerror = () => {
                console.error('Dashboard Optimizer: Error loading image for thumbnail');
                resolve(originalImageData); // Fallback to original
            };

            img.src = originalImageData;
        });
    }

    /**
     * Calculates optimal thumbnail dimensions maintaining aspect ratio
     */
    function calculateThumbnailDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
        const aspectRatio = originalWidth / originalHeight;
        
        let width = maxWidth;
        let height = maxWidth / aspectRatio;
        
        if (height > maxHeight) {
            height = maxHeight;
            width = maxHeight * aspectRatio;
        }
        
        return { width: Math.round(width), height: Math.round(height) };
    }

    /**
     * Optimizes content for dashboard display
     * @param {string} content - HTML content
     * @returns {Promise<string>} - Optimized content
     */
    async function optimizeContentForDashboard(content) {
        if (!content || typeof content !== 'string') {
            return content;
        }

        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = content;

        const images = tempDiv.querySelectorAll('img');
        const imagePromises = [];

        images.forEach((img) => {
            const src = img.src || img.getAttribute('data-image-data');
            if (src && src.startsWith('data:')) {
                const promise = createOptimizedThumbnail(src).then(thumbnailData => {
                    // Store original for later high-quality loading
                    img.setAttribute('data-original-src', src);
                    img.setAttribute('data-lazy-load', 'true');
                    img.classList.add('lazy-load-image');
                    
                    // Set thumbnail as initial source
                    img.src = thumbnailData;
                    img.style.transition = 'filter 0.3s ease';
                    
                    // Cache the thumbnail
                    cacheImage(src, thumbnailData, 'thumbnail');
                });
                imagePromises.push(promise);
            }
        });

        await Promise.all(imagePromises);
        return tempDiv.innerHTML;
    }

    /**
     * Loads high-quality image when needed
     * @param {HTMLImageElement} imgElement - Image element to upgrade
     */
    function loadHighQualityImage(imgElement) {
        const originalSrc = imgElement.getAttribute('data-original-src');
        if (!originalSrc || imgElement.getAttribute('data-loading') === 'true') {
            return;
        }

        imgElement.setAttribute('data-loading', 'true');

        // Check cache first
        const cached = getCachedImage(originalSrc, 'original');
        if (cached) {
            imgElement.src = cached;
            imgElement.style.filter = 'none';
            imgElement.removeAttribute('data-lazy-load');
            imgElement.removeAttribute('data-loading');
            intersectionObserver?.unobserve(imgElement);
            return;
        }

        // Add to loading queue
        addToLoadingQueue({
            element: imgElement,
            src: originalSrc,
            type: 'original'
        });
    }

    /**
     * Adds image to loading queue with concurrency control
     */
    function addToLoadingQueue(loadTask) {
        loadingQueue.push(loadTask);
        processLoadingQueue();
    }

    /**
     * Processes the loading queue with concurrency limits
     */
    function processLoadingQueue() {
        if (currentLoads >= config.maxConcurrentLoads || loadingQueue.length === 0) {
            return;
        }

        const task = loadingQueue.shift();
        currentLoads++;

        const img = new Image();
        
        img.onload = () => {
            task.element.src = task.src;
            task.element.style.filter = 'none';
            task.element.removeAttribute('data-lazy-load');
            task.element.removeAttribute('data-loading');
            
            // Cache the loaded image
            cacheImage(task.src, task.src, task.type);
            
            // Unobserve the element
            intersectionObserver?.unobserve(task.element);
            
            currentLoads--;
            processLoadingQueue(); // Process next in queue
        };

        img.onerror = () => {
            console.warn('Dashboard Optimizer: Failed to load high-quality image');
            task.element.removeAttribute('data-loading');
            currentLoads--;
            processLoadingQueue(); // Process next in queue
        };

        img.src = task.src;
    }

    /**
     * Caches image data with LRU eviction
     */
    function cacheImage(key, data, type) {
        const cacheKey = `${key}_${type}`;
        
        // Remove if already exists (for LRU)
        if (imageCache.has(cacheKey)) {
            imageCache.delete(cacheKey);
        }
        
        // Add to end (most recently used)
        imageCache.set(cacheKey, {
            data: data,
            timestamp: Date.now(),
            type: type
        });
        
        // Evict oldest if cache is full
        if (imageCache.size > config.cacheSize) {
            const firstKey = imageCache.keys().next().value;
            imageCache.delete(firstKey);
        }
    }

    /**
     * Gets cached image data
     */
    function getCachedImage(key, type) {
        const cacheKey = `${key}_${type}`;
        const cached = imageCache.get(cacheKey);
        
        if (cached) {
            // Move to end (mark as recently used)
            imageCache.delete(cacheKey);
            imageCache.set(cacheKey, cached);
            return cached.data;
        }
        
        return null;
    }

    /**
     * Optimizes memory usage
     */
    function optimizeMemoryUsage() {
        // Clear old cache entries
        const now = Date.now();
        const maxAge = 5 * 60 * 1000; // 5 minutes
        
        for (const [key, value] of imageCache.entries()) {
            if (now - value.timestamp > maxAge) {
                imageCache.delete(key);
            }
        }
        
        // Force garbage collection if available
        if (window.gc) {
            setTimeout(() => window.gc(), 100);
        }
    }

    /**
     * Optimizes a note item for performance
     */
    function optimizeNoteItem(noteItem) {
        // Add performance optimizations for visible note items
        noteItem.classList.add('optimized');
        
        // Observe images within the note item
        const images = noteItem.querySelectorAll('.lazy-load-image');
        images.forEach(img => {
            if (intersectionObserver) {
                intersectionObserver.observe(img);
            }
        });
    }

    /**
     * Cleans up resources
     */
    function cleanup() {
        if (intersectionObserver) {
            intersectionObserver.disconnect();
            intersectionObserver = null;
        }
        
        if (memoryMonitor) {
            clearInterval(memoryMonitor);
            memoryMonitor = null;
        }
        
        imageCache.clear();
        loadingQueue = [];
        currentLoads = 0;
    }

    // Public API
    return {
        initialize,
        optimizeContentForDashboard,
        loadHighQualityImage,
        createOptimizedThumbnail,
        optimizeNoteItem,
        cleanup,
        
        // Configuration
        setConfig: (newConfig) => Object.assign(config, newConfig),
        getConfig: () => ({ ...config }),
        
        // Cache management
        clearCache: () => imageCache.clear(),
        getCacheSize: () => imageCache.size,
        
        // Statistics
        getStats: () => ({
            cacheSize: imageCache.size,
            queueLength: loadingQueue.length,
            currentLoads: currentLoads
        })
    };
})();
