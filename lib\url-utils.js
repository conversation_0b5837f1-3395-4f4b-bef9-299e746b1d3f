/**
 * Stashy URL Utilities
 * Secure URL handling and validation utilities
 * Replaces external dependencies for security compliance
 */

(function() {
    'use strict';

    const StashyURLUtils = {
        /**
         * Validates if a URL is safe and well-formed
         * @param {string} url - The URL to validate
         * @returns {boolean} True if the URL is safe
         */
        isValidURL: function(url) {
            if (!url || typeof url !== 'string') {
                return false;
            }

            try {
                const parsedURL = new URL(url);
                
                // Only allow safe protocols
                const allowedProtocols = ['http:', 'https:', 'data:', 'file:'];
                if (!allowedProtocols.includes(parsedURL.protocol)) {
                    return false;
                }

                // Block dangerous URLs
                const dangerousPatterns = [
                    /javascript:/i,
                    /vbscript:/i,
                    /data:text\/html/i,
                    /data:application\/javascript/i
                ];

                for (const pattern of dangerousPatterns) {
                    if (pattern.test(url)) {
                        return false;
                    }
                }

                return true;
            } catch (e) {
                return false;
            }
        },

        /**
         * Sanitizes a URL for safe use
         * @param {string} url - The URL to sanitize
         * @returns {string|null} The sanitized URL or null if unsafe
         */
        sanitizeURL: function(url) {
            if (!this.isValidURL(url)) {
                return null;
            }

            try {
                const parsedURL = new URL(url);
                return parsedURL.href;
            } catch (e) {
                return null;
            }
        },

        /**
         * Extracts the domain from a URL
         * @param {string} url - The URL to extract domain from
         * @returns {string|null} The domain or null if invalid
         */
        extractDomain: function(url) {
            if (!this.isValidURL(url)) {
                return null;
            }

            try {
                const parsedURL = new URL(url);
                return parsedURL.hostname;
            } catch (e) {
                return null;
            }
        },

        /**
         * Creates a safe filename from a URL
         * @param {string} url - The URL to create filename from
         * @returns {string} A safe filename
         */
        createSafeFilename: function(url) {
            if (!url || typeof url !== 'string') {
                return 'Stashy_export';
            }

            try {
                const parsedURL = new URL(url);
                let hostname = parsedURL.hostname.replace(/^www\./, '');
                
                // Remove unsafe characters
                hostname = hostname.replace(/[^a-z0-9\.\-]/gi, '_');
                
                // Limit length
                hostname = hostname.substring(0, 50);
                
                return hostname || 'Stashy_export';
            } catch (e) {
                // Fallback for invalid URLs
                const safeName = url.replace(/[^a-z0-9\.\-]/gi, '_').substring(0, 50);
                return safeName || 'Stashy_export';
            }
        },

        /**
         * Checks if a URL is from a trusted domain
         * @param {string} url - The URL to check
         * @returns {boolean} True if from a trusted domain
         */
        isTrustedDomain: function(url) {
            const trustedDomains = [
                'googleapis.com',
                'google.com',
                'gstatic.com'
            ];

            const domain = this.extractDomain(url);
            if (!domain) {
                return false;
            }

            return trustedDomains.some(trusted => 
                domain === trusted || domain.endsWith('.' + trusted)
            );
        },

        /**
         * Converts a relative URL to absolute
         * @param {string} relativeUrl - The relative URL
         * @param {string} baseUrl - The base URL
         * @returns {string|null} The absolute URL or null if invalid
         */
        makeAbsolute: function(relativeUrl, baseUrl) {
            if (!relativeUrl || !baseUrl) {
                return null;
            }

            try {
                const absoluteURL = new URL(relativeUrl, baseUrl);
                return this.sanitizeURL(absoluteURL.href);
            } catch (e) {
                return null;
            }
        },

        /**
         * Encodes a string for safe use in URLs
         * @param {string} str - The string to encode
         * @returns {string} The encoded string
         */
        safeEncode: function(str) {
            if (!str || typeof str !== 'string') {
                return '';
            }

            try {
                return encodeURIComponent(str);
            } catch (e) {
                // Fallback encoding
                return str.replace(/[^a-zA-Z0-9\-_.~]/g, function(match) {
                    return '%' + match.charCodeAt(0).toString(16).toUpperCase();
                });
            }
        },

        /**
         * Decodes a URL-encoded string safely
         * @param {string} str - The string to decode
         * @returns {string} The decoded string
         */
        safeDecode: function(str) {
            if (!str || typeof str !== 'string') {
                return '';
            }

            try {
                return decodeURIComponent(str);
            } catch (e) {
                // Return original string if decoding fails
                return str;
            }
        }
    };

    // Make it available globally
    window.StashyURLUtils = StashyURLUtils;
    
    console.log('Stashy: URL utilities loaded securely');
})();
