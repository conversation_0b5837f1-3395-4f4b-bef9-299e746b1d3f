/**
 * <PERSON><PERSON>y Voice Settings Enhanced Styles
 * External CSS file for voice-settings-enhanced.html
 * Extracted from inline styles for better code organization and CSP compliance
 */

/* CSS Variables */
:root {
    --primary-color: #34D399;
    --primary-hover: #10B981;
    --primary-light: #D1FAE5;
    --secondary-color: #6B7280;
    --secondary-light: #F3F4F6;
    --secondary-lighter: #F9FAFB;
    --border-color: #E5E7EB;
    --text-primary: #1F2937;
    --text-secondary: #4B5563;
    --text-light: #9CA3AF;
    --danger-color: #EF4444;
    --warning-color: #F59E0B;
    --info-color: #3B82F6;
    --success-color: #10B981;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --transition: all 0.2s ease;
}

/* Reset and Base Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    color: var(--text-primary);
    background-color: #ffffff;
    line-height: 1.5;
    padding: 0;
    margin: 0;
    min-height: 100vh;
}

/* Container */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0;
}

/* Header Styles */
header {
    background-color: var(--primary-color);
    color: white;
    padding: 20px;
    box-shadow: var(--shadow-md);
    position: relative;
}

header h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin-top: 4px;
    font-weight: 400;
}

.back-button {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: var(--transition);
    text-decoration: none;
}

.back-button:hover {
    background-color: rgba(255, 255, 255, 0.3);
}

/* Main Content */
main {
    padding: 20px;
}

.settings-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

@media (min-width: 768px) {
    .settings-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Setting Cards */
.setting-card {
    background-color: white;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: var(--transition);
}

.setting-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--secondary-lighter);
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header h2 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: var(--text-primary);
}

.card-header .icon {
    color: var(--primary-color);
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-body {
    padding: 16px;
}

/* Form Elements */
.form-group {
    margin-bottom: 16px;
}

.form-group:last-child {
    margin-bottom: 0;
}

label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    font-size: 14px;
    color: var(--text-secondary);
}

select,
input[type="text"],
input[type="password"],
input[type="number"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: 14px;
    color: var(--text-primary);
    background-color: white;
    transition: var(--transition);
}

select:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="number"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
}

/* Checkbox Styles */
.checkbox-group {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
}

.checkbox-group:last-child {
    margin-bottom: 0;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    cursor: pointer;
    user-select: none;
}

.checkbox-container {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-right: 10px;
}

.checkbox-container input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    width: 18px;
    height: 18px;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    transition: var(--transition);
}

.checkbox-container input:checked ~ .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 6px;
    top: 2px;
    width: 4px;
    height: 9px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container input:focus ~ .checkmark {
    box-shadow: 0 0 0 3px var(--primary-light);
}

/* API Key Container */
.api-key-container {
    background-color: var(--secondary-lighter);
    border-radius: var(--radius-sm);
    padding: 12px;
    margin-top: 12px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.api-key-container.visible {
    display: block;
}

.api-key-container.hidden {
    display: none;
}

.api-key-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.api-key-header label {
    margin-bottom: 0;
}

.toggle-password {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
}

.toggle-password:hover {
    color: var(--text-primary);
}

/* Help Text and Info */
.help-text {
    font-size: 12px;
    color: var(--text-secondary);
    margin-top: 4px;
}

.security-notice {
    margin: 10px 0;
    padding: 0;
}

.security-warning,
.key-scope-info {
    padding: 8px 12px;
    margin: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    line-height: 1.4;
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.security-warning {
    background-color: #e8f5e8;
    border: 1px solid #4caf50;
    color: #2e7d32;
}

.key-scope-info {
    background-color: #e3f2fd;
    border: 1px solid #2196f3;
    color: #1565c0;
}

.security-warning i,
.key-scope-info i {
    margin-top: 2px;
    flex-shrink: 0;
}

.provider-info {
    margin-top: 12px;
    font-size: 13px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 6px;
}

.provider-info a {
    color: var(--info-color);
    text-decoration: none;
}

.provider-info a:hover {
    text-decoration: underline;
}

/* Slider Styles */
.slider-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.slider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.slider-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--primary-color);
    background-color: var(--primary-light);
    padding: 2px 8px;
    border-radius: 12px;
}

.slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--secondary-light);
    outline: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
}

.slider::-webkit-slider-thumb:hover {
    background: var(--primary-hover);
    transform: scale(1.1);
}

.slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
    border: none;
}

.slider::-moz-range-thumb:hover {
    background: var(--primary-hover);
    transform: scale(1.1);
}

/* Action Bar */
.action-bar {
    background-color: var(--secondary-lighter);
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid var(--border-color);
    margin-top: 20px;
}

/* Button Styles */
.primary-button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.primary-button:hover {
    background-color: var(--primary-hover);
}

.secondary-button {
    background-color: white;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.secondary-button:hover {
    background-color: var(--secondary-lighter);
    color: var(--text-primary);
}

/* Status Message */
.status-message {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: var(--radius-sm);
    font-size: 14px;
    font-weight: 500;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: 8px;
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    z-index: 1000;
}

.status-message.success {
    background-color: var(--success-color);
    color: white;
}

.status-message.error {
    background-color: var(--danger-color);
    color: white;
}

.status-message.info {
    background-color: var(--info-color);
    color: white;
}

.status-message.visible {
    transform: translateY(0);
    opacity: 1;
}

/* Provider and Feature Badges */
.provider-logo {
    width: 24px;
    height: 24px;
    object-fit: contain;
    margin-right: 8px;
}

.provider-option {
    display: flex;
    align-items: center;
}

.feature-badge {
    display: inline-block;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 6px;
    text-transform: uppercase;
}

.feature-badge.free {
    background-color: var(--primary-light);
    color: var(--primary-hover);
}

.feature-badge.premium {
    background-color: #FEF3C7;
    color: #D97706;
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    display: inline-block;
    margin-left: 4px;
    color: var(--text-light);
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: var(--text-primary);
    color: white;
    text-align: center;
    border-radius: var(--radius-sm);
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
    font-weight: normal;
    box-shadow: var(--shadow-md);
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--text-primary) transparent transparent transparent;
}

/* Responsive Design */
@media (max-width: 767px) {
    .settings-container {
        grid-template-columns: 1fr;
    }

    header h1 {
        font-size: 20px;
    }

    .back-button {
        padding: 6px 10px;
        font-size: 12px;
    }
}
