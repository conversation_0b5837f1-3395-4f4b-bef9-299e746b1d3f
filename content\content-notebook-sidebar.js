/**
 * Stashy Notebook Sidebar
 * Provides notebook selection functionality within the note UI
 */

// Global variables for notebook selector
let notebookSelectorData = {
    notebooks: [],
    currentNotebookId: null,
    isLoaded: false,
    isDropdownOpen: false
};

/**
 * Initializes the notebook selector when the note UI is created
 */
function initializeNotebookSidebar() {
    // Load notebooks from storage
    loadNotebooksForSelector();

    // Set up event listeners
    setupNotebookSelectorEvents();
}

/**
 * Toggles the notebook dropdown visibility
 */
function toggleNotebookDropdown() {
    // Ensure dropdown is outside note container
    const dropdown = createDropdownOutsideContainer();
    const toggle = document.getElementById('Stashy-notebook-toggle');

    if (!dropdown || !toggle) {
        console.warn("Stashy: Notebook dropdown elements not found");
        return;
    }

    notebookSelectorData.isDropdownOpen = !notebookSelectorData.isDropdownOpen;

    if (notebookSelectorData.isDropdownOpen) {
        // Show dropdown temporarily to measure its height
        dropdown.style.display = 'block';
        dropdown.style.visibility = 'hidden';

        // Smart positioning to prevent cutoff
        positionDropdownSafely(dropdown, toggle);

        // Make dropdown visible and ensure highest z-index
        dropdown.style.visibility = 'visible';
        dropdown.style.zIndex = '2147483647'; // Highest possible z-index - above snippets
        toggle.classList.add('active');

        // Close dropdown when clicking outside
        setTimeout(() => {
            document.addEventListener('click', closeDropdownOnOutsideClick, true);
        }, 0);
    } else {
        dropdown.style.display = 'none';
        toggle.classList.remove('active');
        document.removeEventListener('click', closeDropdownOnOutsideClick, true);

        // Reset positioning styles and clean up classes
        resetDropdownPosition(dropdown);
        dropdown.classList.remove('Stashy-notebook-dropdown-force-top');
    }
}

/**
 * Positions the dropdown safely to prevent viewport cutoff using fixed positioning
 */
function positionDropdownSafely(dropdown, toggle) {
    try {
        // Get viewport and element dimensions
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        const toggleRect = toggle.getBoundingClientRect();
        const dropdownHeight = dropdown.offsetHeight;
        const dropdownWidth = dropdown.offsetWidth;

        // Calculate space above and below the toggle button
        const spaceAbove = toggleRect.top;
        const spaceBelow = viewportHeight - toggleRect.bottom;

        // Determine optimal vertical position
        const preferAbove = spaceAbove >= dropdownHeight + 10; // 10px buffer
        const canFitBelow = spaceBelow >= dropdownHeight + 10;

        let topPosition, leftPosition;

        // Set shadow color
        const shadowColor = 'rgba(0, 0, 0, 0.15)';

        if (preferAbove) {
            // Position above the toggle button
            topPosition = toggleRect.top - dropdownHeight - 2; // 2px gap
            dropdown.style.boxShadow = `0 -4px 12px ${shadowColor}`; // Upward shadow
        } else if (canFitBelow) {
            // Position below the toggle button
            topPosition = toggleRect.bottom + 2; // 2px gap
            dropdown.style.boxShadow = `0 4px 12px ${shadowColor}`; // Downward shadow
        } else {
            // If neither position fits perfectly, choose the one with more space
            if (spaceAbove > spaceBelow) {
                // Position above but limit height
                const maxHeight = Math.max(spaceAbove - 20, 100);
                topPosition = 10; // 10px from top of viewport
                dropdown.style.maxHeight = maxHeight + 'px';
                dropdown.style.boxShadow = `0 -4px 12px ${shadowColor}`;
            } else {
                // Position below but limit height
                const maxHeight = Math.max(spaceBelow - 20, 100);
                topPosition = toggleRect.bottom + 2;
                dropdown.style.maxHeight = maxHeight + 'px';
                dropdown.style.boxShadow = `0 4px 12px ${shadowColor}`;
            }
        }

        // Calculate horizontal position (align to right edge of toggle button)
        leftPosition = toggleRect.right - dropdownWidth;

        // Ensure dropdown doesn't go off the left edge of the viewport
        if (leftPosition < 10) {
            leftPosition = 10; // 10px from left edge
        }

        // Ensure dropdown doesn't go off the right edge of the viewport
        if (leftPosition + dropdownWidth > viewportWidth - 10) {
            leftPosition = viewportWidth - dropdownWidth - 10; // 10px from right edge
        }

        // Apply fixed positioning
        dropdown.style.position = 'fixed';
        dropdown.style.top = topPosition + 'px';
        dropdown.style.left = leftPosition + 'px';
        dropdown.style.bottom = 'auto';
        dropdown.style.right = 'auto';
        dropdown.style.marginBottom = '0';
        dropdown.style.marginTop = '0';

        // Ensure z-index is always set correctly regardless of position
        enforceDropdownZIndex(dropdown);

    } catch (error) {
        // Fallback to simple fixed positioning
        const toggleRect = toggle.getBoundingClientRect();
        dropdown.style.position = 'fixed';
        dropdown.style.top = (toggleRect.top - 150) + 'px'; // Simple above positioning
        dropdown.style.left = (toggleRect.right - 150) + 'px'; // Simple right alignment
        dropdown.style.bottom = 'auto';
        dropdown.style.right = 'auto';
        dropdown.style.maxHeight = '200px';

        // Still ensure z-index is correct
        enforceDropdownZIndex(dropdown);
    }
}

/**
 * Ensures the dropdown has the correct z-index and escapes stacking contexts
 */
function enforceDropdownZIndex(dropdown) {
    // Move dropdown to document body to escape note container's stacking context
    if (dropdown.parentElement && dropdown.parentElement.id !== 'body') {
        document.body.appendChild(dropdown);
    }

    dropdown.style.zIndex = '2147483647'; // Highest possible z-index
    dropdown.style.isolation = 'isolate'; // Create new stacking context
    dropdown.style.contain = 'none'; // Prevent containment interference

    // Double-check that the z-index was applied
    const computedZIndex = window.getComputedStyle(dropdown).zIndex;

    // If for some reason the z-index didn't apply, try with !important via CSS class
    if (computedZIndex !== '2147483647') {
        dropdown.classList.add('Stashy-notebook-dropdown-force-top');
    }
}

/**
 * Resets dropdown position to default CSS values
 */
function resetDropdownPosition(dropdown) {
    dropdown.style.position = '';
    dropdown.style.bottom = '';
    dropdown.style.top = '';
    dropdown.style.left = '';
    dropdown.style.right = '';
    dropdown.style.marginBottom = '';
    dropdown.style.marginTop = '';
    dropdown.style.maxHeight = '';
    dropdown.style.boxShadow = '';
    dropdown.style.zIndex = ''; // Reset z-index to CSS default
}

/**
 * Closes dropdown when clicking outside
 */
function closeDropdownOnOutsideClick(event) {
    const dropdown = document.getElementById('Stashy-notebook-dropdown');
    const toggle = document.getElementById('Stashy-notebook-toggle');

    if (!dropdown || !toggle) return;

    // Check if click is outside the dropdown and toggle
    if (!dropdown.contains(event.target) && !toggle.contains(event.target)) {
        notebookSelectorData.isDropdownOpen = false;
        dropdown.style.display = 'none';
        toggle.classList.remove('active');
        document.removeEventListener('click', closeDropdownOnOutsideClick, true);

        // Clean up z-index and positioning
        resetDropdownPosition(dropdown);
        dropdown.classList.remove('Stashy-notebook-dropdown-force-top');
    }
}

/**
 * Loads notebooks from Chrome storage and populates the selector
 */
async function loadNotebooksForSelector() {
    try {
        const NOTEBOOKS_STORAGE_KEY = 'Stashy_notebooks';
        const result = await chrome.storage.local.get(NOTEBOOKS_STORAGE_KEY);

        let notebooks = result[NOTEBOOKS_STORAGE_KEY] || [];

        // Ensure notebooks is an array
        if (!Array.isArray(notebooks)) {
            notebooks = [];
        }

        // Sort notebooks alphabetically
        notebooks.sort((a, b) => a.name.localeCompare(b.name));

        notebookSelectorData.notebooks = notebooks;
        notebookSelectorData.isLoaded = true;

        // Render the notebook dropdown
        renderNotebookDropdown();

        // Set current notebook based on note data
        updateCurrentNotebookSelection();

    } catch (error) {
        console.error("Stashy: Error loading notebooks for selector:", error);
        notebookSelectorData.isLoaded = true; // Mark as loaded even on error
        renderNotebookDropdown(); // Render empty list
    }
}

/**
 * Creates dropdown outside note container to escape stacking context
 */
function createDropdownOutsideContainer() {
    let dropdown = document.getElementById('Stashy-notebook-dropdown');

    // If dropdown exists but is inside note container, move it
    if (dropdown && dropdown.closest('#Stashy-container')) {
        document.body.appendChild(dropdown);
    }

    // If dropdown doesn't exist, create it on document body
    if (!dropdown) {
        dropdown = document.createElement('div');
        dropdown.id = 'Stashy-notebook-dropdown';
        dropdown.className = 'Stashy-notebook-dropdown';
        dropdown.style.display = 'none';
        dropdown.style.visibility = 'hidden';
        document.body.appendChild(dropdown);
    }

    return dropdown;
}

/**
 * Renders the notebook dropdown list
 */
function renderNotebookDropdown() {
    // Ensure dropdown is outside note container
    const notebookDropdown = createDropdownOutsideContainer();
    if (!notebookDropdown) {
        // Retry after a delay in case the UI is still being created
        setTimeout(renderNotebookDropdown, 500);
        return;
    }

    // Clear existing content
    notebookDropdown.innerHTML = '';

    // Add default "Ungrouped" option only
    const ungroupedItem = createNotebookItem(null, 'Ungrouped', '📄');
    notebookDropdown.appendChild(ungroupedItem);

    // Add custom notebooks
    notebookSelectorData.notebooks.forEach(notebook => {
        const item = createNotebookItem(notebook.id, notebook.name, '📚');
        notebookDropdown.appendChild(item);
    });
}

/**
 * Creates a notebook item element
 */
function createNotebookItem(notebookId, name, icon) {
    const item = document.createElement('button');
    item.className = 'Stashy-notebook-item';
    item.dataset.notebookId = notebookId || 'UNGROUPED';
    item.title = `Select notebook: ${name}`;

    const iconSpan = document.createElement('span');
    iconSpan.className = 'Stashy-notebook-item-icon';
    iconSpan.textContent = icon;

    const nameSpan = document.createElement('span');
    nameSpan.className = 'Stashy-notebook-item-name';
    nameSpan.textContent = name;

    item.appendChild(iconSpan);
    item.appendChild(nameSpan);

    // Add click event listener
    item.addEventListener('click', (e) => {
        e.stopPropagation();
        handleNotebookSelection(notebookId, name);
    });

    // Prevent drag events from interfering
    item.addEventListener('mousedown', (e) => e.stopPropagation());

    return item;
}

/**
 * Handles notebook selection
 */
async function handleNotebookSelection(notebookId, notebookName) {
    try {
        // Update current selection
        notebookSelectorData.currentNotebookId = notebookId;

        // Update visual selection
        updateNotebookSelectionUI(notebookId);

        // Update the current note's notebook assignment
        await updateCurrentNoteNotebook(notebookId);

        // Update the toggle button display
        updateToggleButtonDisplay(notebookName);

        // Close the dropdown
        toggleNotebookDropdown();

        // Show feedback
        showNotebookSelectionFeedback(notebookName);

    } catch (error) {
        showNotebookSelectionFeedback("Error selecting notebook", true);
    }
}

/**
 * Updates the toggle button to show the currently selected notebook
 */
function updateToggleButtonDisplay(notebookName) {
    const currentSpan = document.querySelector('.Stashy-notebook-current');
    if (currentSpan) {
        currentSpan.textContent = notebookName || 'Ungrouped';
    }
}

/**
 * Updates the visual selection in the notebook sidebar
 */
function updateNotebookSelectionUI(selectedNotebookId) {
    const notebookItems = document.querySelectorAll('.Stashy-notebook-item');

    notebookItems.forEach(item => {
        const itemNotebookId = item.dataset.notebookId;
        const normalizedItemId = itemNotebookId === 'UNGROUPED' ? null : itemNotebookId;
        const normalizedSelectedId = selectedNotebookId === 'UNGROUPED' ? null : selectedNotebookId;

        if (normalizedItemId === normalizedSelectedId) {
            item.classList.add('selected');
        } else {
            item.classList.remove('selected');
        }
    });
}

/**
 * Updates the current note's notebook assignment
 */
async function updateCurrentNoteNotebook(notebookId) {
    try {
        // Get current note data
        if (typeof currentNoteIndex === 'undefined' || typeof notes === 'undefined') {
            return;
        }

        const currentNote = notes[currentNoteIndex];
        if (!currentNote) {
            return;
        }

        // Update notebook assignment
        const normalizedNotebookId = notebookId === 'UNGROUPED' ? null : notebookId;
        currentNote.notebookId = normalizedNotebookId;

        // Save the note
        if (typeof saveCurrentNote === 'function') {
            await saveCurrentNote();
        } else if (typeof scheduleSave === 'function') {
            // Fallback to scheduleSave if saveCurrentNote is not available
            scheduleSave();
        }
    } catch (error) {
        console.error("Stashy: Error updating note notebook:", error);
        throw error;
    }
}

/**
 * Shows feedback when a notebook is selected
 */
function showNotebookSelectionFeedback(notebookName, isError = false) {
    // Use existing status function if available
    if (typeof showStatus === 'function') {
        const message = isError ? notebookName : `Note moved to: ${notebookName}`;
        const type = isError ? 'error' : 'success';
        showStatus(message, type, 2000);
    }
}

/**
 * Updates the current notebook selection based on note data
 */
function updateCurrentNotebookSelection() {
    if (typeof currentNoteIndex === 'undefined' || typeof notes === 'undefined') {
        return;
    }

    const currentNote = notes[currentNoteIndex];
    if (!currentNote) {
        return;
    }

    const notebookId = currentNote.notebookId || null;
    notebookSelectorData.currentNotebookId = notebookId;
    updateNotebookSelectionUI(notebookId);

    // Update toggle button display
    const notebookName = getNotebookNameById(notebookId);
    updateToggleButtonDisplay(notebookName);
}

/**
 * Gets notebook name by ID
 */
function getNotebookNameById(notebookId) {
    if (!notebookId || notebookId === 'UNGROUPED') {
        return 'Ungrouped';
    }

    const notebook = notebookSelectorData.notebooks.find(nb => nb.id === notebookId);
    return notebook ? notebook.name : 'Ungrouped';
}

/**
 * Sets up event listeners for the notebook selector
 */
function setupNotebookSelectorEvents() {
    // Listen for note changes to update selection
    if (typeof window !== 'undefined') {
        // Custom event listener for note changes
        window.addEventListener('StashyNoteChanged', () => {
            updateCurrentNotebookSelection();
        });

        // Listen for notebook updates from dashboard
        window.addEventListener('StashyNotebooksUpdated', () => {
            loadNotebooksForSelector();
        });
    }
}

/**
 * Refreshes the notebook selector (called when notebooks are updated)
 */
function refreshNotebookSidebar() {
    if (notebookSelectorData.isLoaded) {
        loadNotebooksForSelector();
    }
}

// Export functions for global access
if (typeof window !== 'undefined') {
    window.initializeNotebookSidebar = initializeNotebookSidebar;
    window.refreshNotebookSidebar = refreshNotebookSidebar;
    window.updateCurrentNotebookSelection = updateCurrentNotebookSelection;
    window.toggleNotebookDropdown = toggleNotebookDropdown;
}
