/**
 * Parses the content of the noteText element to find Q:/A: formatted flashcards.
 * @returns {Array<object>} An array of flashcard objects { front: string, back: string }.
 */
function parseFlashcardsFromNote() {
    if (!noteText) {
        console.log('Stashy: noteText element not found');
        return [];
    }

    // Get content from the note, handling HTML properly
    let noteContent = '';

    // First try to get HTML content and convert <br> tags to newlines
    if (noteText.innerHTML) {
        noteContent = noteText.innerHTML
            .replace(/<br\s*\/?>/gi, '\n')  // Convert <br> tags to newlines
            .replace(/<div[^>]*>/gi, '\n')   // Convert <div> tags to newlines
            .replace(/<p[^>]*>/gi, '\n')     // Convert <p> tags to newlines
            .replace(/<\/div>/gi, '')        // Remove closing div tags
            .replace(/<\/p>/gi, '')          // Remove closing p tags
            .replace(/<[^>]*>/g, '')         // Remove any remaining HTML tags
            .replace(/&nbsp;/g, ' ')         // Convert &nbsp; to spaces
            .replace(/&amp;/g, '&')          // Convert &amp; to &
            .replace(/&lt;/g, '<')           // Convert &lt; to <
            .replace(/&gt;/g, '>')           // Convert &gt; to >
            .trim();
    }

    // Fallback to plain text if HTML processing didn't work
    if (!noteContent) {
        noteContent = noteText.textContent || noteText.innerText || '';
    }

    // Always try to parse Q&A patterns from the entire note content
    const result = parseFlashcardsFromPlainText(noteContent);
    return result;
}


/**
 * Opens the flashcard study modal. Parses cards from the current note.
 */
function openFlashcardModal() {
    // Ensure the modal UI exists
    if (!flashcardModalOverlay) {
        createFlashcardModal(); // Create if missing (defined in ui.js)
        if (!flashcardModalOverlay) { // Check again after creation attempt
             console.error("Stashy: Failed to create flashcard modal.");
             alert("Error: Could not open flashcard study mode.");
             return;
        }
    }

    // Parse cards from the *current* note text each time study mode is opened
    parsedFlashcards = parseFlashcardsFromNote();

    if (parsedFlashcards.length === 0) {
        alert("No flashcards found in Q:/A: format in this note.\n\nFormat example:\nQ: What is the capital of France?\nA: Paris");
        return;
    }

    // Reset state for new session
    currentFlashcardIndex = 0;
    isFlashcardBackVisible = false;

    // Make the modal overlay visible
    flashcardModalOverlay.style.display = 'flex';
    flashcardModalOverlay.setAttribute('aria-hidden', 'false');

    // Display the first card and set focus
    requestAnimationFrame(() => {
        displayFlashcard(currentFlashcardIndex);
        flashcardContent?.focus(); // Focus the card content area
    });
}

/**
 * Closes the flashcard study modal and resets its state.
 */
function closeFlashcardModal() {
    if (flashcardModalOverlay && flashcardModalOverlay.style.display !== 'none') {
        flashcardModalOverlay.style.display = 'none';
        flashcardModalOverlay.setAttribute('aria-hidden', 'true');
        resetFlashcardState();

        // Return focus appropriately
        if (noteContainer?.classList.contains('visible')) {
            noteText?.focus();
        } else {
            toggleButton?.focus();
        }
        console.log("Stashy: Flashcard modal closed.");
    }
}

/**
 * Resets the flashcard state variables and clears the modal UI.
 */
function resetFlashcardState() {
    parsedFlashcards = [];
    currentFlashcardIndex = 0;
    isFlashcardBackVisible = false;
    if (flashcardContent) flashcardContent.innerHTML = '';
    if (flashcardCounter) flashcardCounter.textContent = 'Card 0 / 0';
    if (flashcardPrevBtn) flashcardPrevBtn.disabled = true;
    if (flashcardNextBtn) flashcardNextBtn.disabled = true;
}

/**
 * Displays the flashcard at the given index in the modal.
 * Shows only the front initially.
 * @param {number} index - The index of the card in the `parsedFlashcards` array.
 */
function displayFlashcard(index) {
    if (!flashcardModalOverlay || flashcardModalOverlay.style.display === 'none' || index < 0 || index >= parsedFlashcards.length) {
        return; // Modal not open or index out of bounds
    }
    if (!flashcardContent || !flashcardCounter || !flashcardPrevBtn || !flashcardNextBtn) {
        console.error("Stashy: Flashcard modal UI elements missing during display.");
        return;
    }

    const card = parsedFlashcards[index];
    flashcardContent.innerHTML = ''; // Clear previous content

    // Create front element (use innerHTML to render potential basic formatting like <br>)
    const frontDiv = document.createElement('div');
    frontDiv.innerHTML = card.front.replace(/\n/g, '<br>'); // Render newlines as breaks
    frontDiv.classList.add('flashcard-front');
    flashcardContent.appendChild(frontDiv);

    // Add instructional hint
    const hintP = document.createElement('p');
    hintP.classList.add('flashcard-reveal-hint');
    hintP.textContent = '(Click card or press Space/Enter to reveal answer)';
    flashcardContent.appendChild(hintP);

    flashcardContent.setAttribute('aria-label', `Front: ${card.front}. Click or press Space/Enter to reveal answer`);
    isFlashcardBackVisible = false;

    // Update counter and button states
    flashcardCounter.textContent = `Card ${index + 1} / ${parsedFlashcards.length}`;
    flashcardPrevBtn.disabled = (index === 0);
    flashcardNextBtn.disabled = (index === parsedFlashcards.length - 1);
}

/**
 * Toggles the visibility of the answer (back) of the current flashcard.
 */
function toggleFlashcardAnswer() {
    if (!flashcardModalOverlay || flashcardModalOverlay.style.display === 'none' || !flashcardContent) return;
    if (currentFlashcardIndex < 0 || currentFlashcardIndex >= parsedFlashcards.length) return;

    const card = parsedFlashcards[currentFlashcardIndex];

    if (isFlashcardBackVisible) {
        // Hide answer: Re-display card showing only front + hint
        displayFlashcard(currentFlashcardIndex);
        // Refocus after redraw
        requestAnimationFrame(() => flashcardContent.focus());
    } else {
        // Show answer: Clear existing content (front + hint)
        flashcardContent.innerHTML = '';

        // Create front element
        const frontDiv = document.createElement('div');
        frontDiv.innerHTML = card.front.replace(/\n/g, '<br>');
        frontDiv.classList.add('flashcard-front');

        // Create separator
        const separator = document.createElement('hr');

        // Create back element
        const backDiv = document.createElement('div');
        backDiv.innerHTML = card.back.replace(/\n/g, '<br>'); // Render newlines
        backDiv.classList.add('flashcard-back');

        // Append all parts
        flashcardContent.appendChild(frontDiv);
        flashcardContent.appendChild(separator);
        flashcardContent.appendChild(backDiv);

        // Update ARIA label for screen readers
        flashcardContent.setAttribute('aria-label', `Front: ${card.front}. Back: ${card.back}. Click or press Space/Enter to hide answer`);
        isFlashcardBackVisible = true;
    }
}

/**
 * Navigates to the next flashcard.
 */
function nextFlashcard() {
    if (currentFlashcardIndex < parsedFlashcards.length - 1) {
        currentFlashcardIndex++;
        displayFlashcard(currentFlashcardIndex);
        flashcardContent?.focus(); // Focus new card
    }
}

/**
 * Navigates to the previous flashcard.
 */
function prevFlashcard() {
    if (currentFlashcardIndex > 0) {
        currentFlashcardIndex--;
        displayFlashcard(currentFlashcardIndex);
        flashcardContent?.focus(); // Focus new card
    }
}

/**
 * Handles key presses specifically when the flashcard modal is open.
 * (Arrow keys, Space, Enter, Escape).
 * @param {KeyboardEvent} event - The keydown event.
 */
function handleFlashcardKeyPress(event) {
    // Ensure modal is open and the event originated within it
    if (!flashcardModalOverlay || flashcardModalOverlay.style.display === 'none' || !flashcardModalOverlay.contains(event.target)) {
        return;
    }

    // Allow default behavior for inputs/textareas if added later
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
    }

    // Prevent default browser actions for keys we handle (e.g., space scrolling)
    if ([' ', 'Enter', 'ArrowRight', 'ArrowLeft', 'Escape'].includes(event.key)) {
         event.preventDefault();
    }

    // Handle key actions
    switch (event.key) {
        case 'ArrowRight':
            nextFlashcard(); // Click the next button logic
            break;
        case 'ArrowLeft':
            prevFlashcard(); // Click the prev button logic
            break;
        case 'Escape':
            closeFlashcardModal();
            break;
        // Space and Enter handled by handleFlashcardContentKeyPress if target is content
    }
}

/**
 * Handles key presses specifically when the flashcard content area has focus.
 * (Space, Enter for revealing answer).
 * @param {KeyboardEvent} event - The keydown event.
 */
function handleFlashcardContentKeyPress(event) {
     if (!flashcardModalOverlay || flashcardModalOverlay.style.display === 'none') {
         return;
     }
    if (event.key === ' ' || event.key === 'Enter') {
        event.preventDefault(); // Prevent default space/enter actions
        toggleFlashcardAnswer();
    }
    // We don't need to handle arrows here, handleFlashcardKeyPress on the overlay does that.
}

/**
 * Parses flashcards from plain text content (new method)
 * @param {string} plainText - Plain text containing Q:/A: pairs
 * @returns {Array} Array of flashcard objects
 */
function parseFlashcardsFromPlainText(plainText) {
    // First, try to split content by Q: patterns to handle cases where everything is on one line
    let processedText = plainText;

    // If we have Q: patterns but few newlines, split on Q: to create proper lines
    const qCount = (plainText.match(/Q:/g) || []).length;
    const lineCount = plainText.split('\n').length;

    if (qCount > 1 && lineCount < qCount) {
        // Split by Q: and add newlines, then rejoin
        processedText = plainText
            .replace(/Q:/g, '\nQ:')  // Add newline before each Q:
            .replace(/A:/g, '\nA:')  // Add newline before each A:
            .replace(/^\n/, '')      // Remove leading newline
            .trim();
    }

    const lines = processedText.split('\n');

    const cards = [];
    let currentFront = null;
    let currentBackLines = [];

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();

        // Skip empty lines
        if (!trimmedLine) continue;

        // Simple Q/A pattern matching - exactly like the original
        const qMatch = trimmedLine.match(/^Q\s*[:\-.]?\s*(.*)/i);
        const aMatch = trimmedLine.match(/^A\s*[:\-.]?\s*(.*)/i);

        if (qMatch) {
            // Found a new Question
            // Save previous card if one was being built
            if (currentFront !== null && currentBackLines.length > 0) {
                const backText = currentBackLines.join(' ').trim();
                if (backText) {
                    cards.push({ front: currentFront, back: backText });
                }
            }

            // Start the new question
            currentFront = qMatch[1].trim();
            currentBackLines = []; // Reset answer accumulator

        } else if (aMatch && currentFront !== null) {
            // Found the start of the Answer for the current question
            const answerText = aMatch[1].trim();
            if (answerText) {
                currentBackLines.push(answerText);
            }

        } else if (currentFront !== null && trimmedLine !== '') {
            // This line is part of the current answer if we have a current question
            // Only include if it's not a new Q/A pattern
            if (!trimmedLine.match(/^Q\s*[:\-.]?\s*/i) &&
                !trimmedLine.match(/^A\s*[:\-.]?\s*/i)) {
                currentBackLines.push(trimmedLine);
            }
        }
    }

    // Save the last card after the loop finishes
    if (currentFront !== null && currentBackLines.length > 0) {
        const backText = currentBackLines.join(' ').trim();
        if (backText) {
            cards.push({ front: currentFront, back: backText });
        }
    }

    // Filter out cards that might be incomplete
    const validCards = cards.filter(card => {
        const hasValidFront = card.front && card.front.trim().length > 0;
        const hasValidBack = card.back && card.back.trim().length > 0;
        return hasValidFront && hasValidBack;
    });

    return validCards;
}

console.log("Stashy: Flashcard Logic Loaded");