/**
 * Google Docs Button Content Script for Stashy
 *
 * This content script injects a button into the dashboard to export notes and highlights to Google Docs.
 * It's designed to work even if the dashboard integration module fails.
 */

(function() {
    // Only run on dashboard pages - check if this looks like a dashboard
    const isDashboardPage = () => {
        // Check URL for dashboard indicators
        const url = window.location.href.toLowerCase();
        if (url.includes('dashboard') || url.includes('Stashy')) {
            return true;
        }

        // Check for dashboard-specific elements
        const dashboardIndicators = [
            '.dashboard-wrapper',
            '.Stashy-dashboard-container',
            '.dashboard-container',
            '#dashboard',
            '[class*="dashboard"]'
        ];

        return dashboardIndicators.some(selector => document.querySelector(selector));
    };

    // Exit early if not on a dashboard page
    if (!isDashboardPage()) {
        // Not on dashboard page, skipping
        return;
    }

    // Google Docs Button Content Script loaded on dashboard page

    // Configuration
    const config = {
        buttonId: 'Stashy-content-gdocs-btn',
        buttonText: 'Export to Google Docs',
        buttonIcon: '📄',
        buttonColor: '#4285F4',
        buttonHoverColor: '#3367D6',
        dashboardSelector: '.dashboard-wrapper, .main-content, .container, .Stashy-dashboard-container, .dashboard-container',
        checkInterval: 500, // Check every 500ms
        maxChecks: 60 // Check for 30 seconds
    };

    // State
    let dashboardObserver = null;
    let checkCount = 0;
    let checkIntervalId = null;
    let button = null;

    /**
     * Main initialization function
     */
    function init() {
        // Start checking for dashboard
        startCheckingForDashboard();

        // Listen for dashboard-related events
        setupEventListeners();
    }

    /**
     * Sets up event listeners
     */
    function setupEventListeners() {
        // Listen for clicks on document
        document.addEventListener('click', function(event) {
            // Check if clicked element might be related to dashboard
            const target = event.target;

            // If target has "dashboard" in its class or id
            if (target.className.includes('dashboard') ||
                (target.id && target.id.includes('dashboard')) ||
                target.closest('[class*="dashboard"]') ||
                target.closest('[id*="dashboard"]')) {

                // Wait a moment then check for dashboard
                setTimeout(() => {
                    checkForDashboard();
                }, 500);
            }

            // Check if the click was on the export button
            if (event.target.id === config.buttonId || event.target.closest('#' + config.buttonId)) {
                handleButtonClick();
            }
        });

        // Listen for changes in the document (for checkboxes)
        document.addEventListener('change', function(event) {
            if (event.target.type === 'checkbox') {
                setTimeout(updateButtonVisibility, 100);
            }
        });
    }

    /**
     * Starts checking for the dashboard
     */
    function startCheckingForDashboard() {
        if (checkIntervalId) {
            clearInterval(checkIntervalId);
        }

        checkCount = 0;

        checkIntervalId = setInterval(() => {
            checkCount++;

            if (checkCount > config.maxChecks) {
                clearInterval(checkIntervalId);
                console.log("Stashy Content GDocs: Stopped checking for dashboard after maximum attempts");
                return;
            }

            checkForDashboard();
        }, config.checkInterval);
    }

    /**
     * Checks if the dashboard is present and injects the button if needed
     */
    function checkForDashboard() {
        const dashboardContainer = document.querySelector(config.dashboardSelector);

        if (dashboardContainer) {
            console.log("Stashy Content GDocs: Dashboard found");

            // Clear the interval if it's still running
            if (checkIntervalId) {
                clearInterval(checkIntervalId);
                checkIntervalId = null;
            }

            // Inject the button
            injectButton();

            // Set up observer to watch for changes in the dashboard
            setupDashboardObserver(dashboardContainer);

            // Check for selected items
            updateButtonVisibility();
        }
    }

    /**
     * Sets up a MutationObserver to watch for changes in the dashboard
     * @param {HTMLElement} dashboardContainer - The dashboard container element
     */
    function setupDashboardObserver(dashboardContainer) {
        // Disconnect any existing observer
        if (dashboardObserver) {
            dashboardObserver.disconnect();
        }

        // Create a new observer
        dashboardObserver = new MutationObserver((mutations) => {
            // Check if any mutations involve checkboxes or selection changes
            const relevantMutation = mutations.some(mutation => {
                // Check added nodes
                if (mutation.addedNodes.length > 0) {
                    for (let node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if this is a checkbox or contains checkboxes
                            if (node.tagName === 'INPUT' && node.type === 'checkbox') {
                                return true;
                            }
                            const checkboxes = node.querySelectorAll('input[type="checkbox"]');
                            if (checkboxes.length > 0) {
                                return true;
                            }
                        }
                    }
                }

                // Check attributes
                if (mutation.type === 'attributes') {
                    if (mutation.target.tagName === 'INPUT' &&
                        mutation.target.type === 'checkbox' &&
                        mutation.attributeName === 'checked') {
                        return true;
                    }
                }

                return false;
            });

            if (relevantMutation) {
                // Update button visibility
                updateButtonVisibility();
            }
        });

        // Start observing
        dashboardObserver.observe(dashboardContainer, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['checked']
        });
    }

    /**
     * Injects the export button into the dashboard
     */
    function injectButton() {
        // Check if button already exists
        if (document.getElementById(config.buttonId)) {
            button = document.getElementById(config.buttonId);
            return;
        }

        console.log("Stashy Content GDocs: Injecting button");

        // Find or create button container
        const container = findOrCreateButtonContainer();
        if (!container) {
            console.warn("Stashy Content GDocs: Could not find or create button container");
            return;
        }

        // Create button
        button = document.createElement('button');
        button.id = config.buttonId;
        button.className = 'Stashy-action-button Stashy-gdocs-button';
        button.title = 'Export selected items to Google Docs';
        button.innerHTML = `<span class="Stashy-icon">${config.buttonIcon}</span> ${config.buttonText}`;

        // Style button
        button.style.backgroundColor = config.buttonColor;
        button.style.color = 'white';
        button.style.border = 'none';
        button.style.borderRadius = '4px';
        button.style.padding = '8px 12px';
        button.style.margin = '5px';
        button.style.cursor = 'pointer';
        button.style.fontWeight = 'bold';
        button.style.fontSize = '14px';
        button.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        button.style.transition = 'all 0.2s ease';
        button.style.display = 'none'; // Hidden by default
        button.style.zIndex = '1000'; // Ensure it's above other elements

        // Add hover effect
        button.addEventListener('mouseover', function() {
            this.style.backgroundColor = config.buttonHoverColor;
            this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
        });

        button.addEventListener('mouseout', function() {
            this.style.backgroundColor = config.buttonColor;
            this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        });

        // Add to container
        container.appendChild(button);

        // Check if there are already selected items
        updateButtonVisibility();
    }

    /**
     * Finds or creates a container for the button
     * @returns {HTMLElement} The container element
     */
    function findOrCreateButtonContainer() {
        // Try various container selectors based on actual dashboard structure
        const containerSelectors = [
            '.Stashy-dashboard-actions',
            '.dashboard-actions',
            '.header-stats', // Existing stats container in dashboard header
            '.dashboard-header', // Main dashboard header
            '.main-content .container', // Main content container
            '.dashboard-wrapper' // Wrapper container
        ];

        // Try to find an existing container
        for (const selector of containerSelectors) {
            const container = document.querySelector(selector);
            if (container) {
                console.log(`Stashy Content GDocs: Found existing container: ${selector}`);
                return container;
            }
        }

        // If no container found, create one in the dashboard header
        const dashboardHeader = document.querySelector('.dashboard-header');
        if (dashboardHeader) {
            console.log("Stashy Content GDocs: Creating new container in dashboard header");
            const container = document.createElement('div');
            container.className = 'Stashy-dashboard-actions';
            container.style.cssText = `
                padding: 15px 0 0 0;
                display: flex;
                justify-content: center;
                gap: 10px;
                margin-top: 20px;
                border-top: 1px solid #eee;
                z-index: 1000;
            `;

            // Add to dashboard header
            dashboardHeader.appendChild(container);
            return container;
        }

        // Fallback: try main content container
        const mainContent = document.querySelector('.main-content .container');
        if (mainContent) {
            console.log("Stashy Content GDocs: Creating new container in main content");
            const container = document.createElement('div');
            container.className = 'Stashy-dashboard-actions';
            container.style.cssText = `
                padding: 15px;
                display: flex;
                justify-content: center;
                gap: 10px;
                margin-bottom: 20px;
                border-bottom: 1px solid #eee;
                background: #f9f9f9;
                border-radius: 8px;
                z-index: 1000;
            `;

            // Add to top of main content
            mainContent.insertBefore(container, mainContent.firstChild);
            return container;
        }

        console.warn("Stashy Content GDocs: Could not find suitable container for button");
        return null;
    }

    /**
     * Updates the visibility of the button based on selected items
     */
    function updateButtonVisibility() {
        if (!button) {
            // Try to inject the button again
            injectButton();
            if (!button) return;
        }

        // Check for selected items
        const selectedItems = getSelectedItems();

        if (selectedItems.length > 0) {
            console.log(`Stashy Content GDocs: Found ${selectedItems.length} selected items, showing button`);
            button.style.display = 'inline-block';
        } else {
            button.style.display = 'none';
        }
    }

    /**
     * Gets selected items from the dashboard
     * @returns {Array} An array of selected items
     */
    function getSelectedItems() {
        // Try various selectors for checkboxes
        const checkboxSelectors = [
            '.Stashy-item-checkbox:checked',
            '.Stashy-dashboard-item input[type="checkbox"]:checked',
            '.dashboard-item input[type="checkbox"]:checked',
            '.note-item input[type="checkbox"]:checked',
            '.highlight-item input[type="checkbox"]:checked'
        ];

        // Combine all selectors
        const selector = checkboxSelectors.join(', ');
        const checkedCheckboxes = document.querySelectorAll(selector);

        if (checkedCheckboxes.length === 0) {
            return [];
        }

        // Map checkboxes to items
        return Array.from(checkedCheckboxes).map(checkbox => {
            const itemElement = checkbox.closest('.Stashy-dashboard-item, .dashboard-item, .note-item, .highlight-item');
            if (!itemElement) return null;

            // Get item data
            const itemId = itemElement.dataset.itemId || itemElement.id || '';
            const itemType = itemElement.dataset.itemType ||
                             (itemElement.classList.contains('note-item') ? 'note' :
                              itemElement.classList.contains('highlight-item') ? 'highlight' : 'unknown');
            const itemUrl = itemElement.dataset.itemUrl || '';
            const itemText = itemElement.querySelector('.Stashy-item-text, .item-content, .note-content, .highlight-content')?.textContent || '';

            return {
                id: itemId,
                type: itemType,
                url: itemUrl,
                text: itemText
            };
        }).filter(Boolean); // Remove null items
    }

    /**
     * Handles button click
     */
    function handleButtonClick() {
        console.log("Stashy Content GDocs: Button clicked");

        // First try to use the export modal from the main integration
        if (window.StashyGoogleDocsExport && typeof window.StashyGoogleDocsExport.showExportModal === 'function') {
            window.StashyGoogleDocsExport.showExportModal();
            return;
        }

        // Fallback to a simple confirmation
        const selectedItems = getSelectedItems();
        const confirmMessage = `Export ${selectedItems.length} item${selectedItems.length !== 1 ? 's' : ''} to Google Docs?`;

        if (confirm(confirmMessage)) {
            alert('This feature is coming soon! The Google Docs export functionality is currently being implemented.');
        }
    }

    // Initialize immediately
    init();

    // Also initialize when the document is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    }

    // Also initialize when the window loads
    window.addEventListener('load', init);
})();

// Google Docs Button Content Script Initialized
