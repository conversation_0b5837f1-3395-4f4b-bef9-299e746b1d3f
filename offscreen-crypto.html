<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Stashy Offscreen Crypto Document</title>
    <script src="lib/data-encryption.js"></script>
</head>
<body>
    <script>
        console.log("Offscreen Crypto document loaded.");

        /**
         * Initializes the encryption module
         * @returns {Promise<boolean>} A promise that resolves when initialization is complete
         */
        async function initializeEncryption() {
            try {
                if (!window.StashyEncryption) {
                    console.error("Offscreen Crypto: StashyEncryption module not available");
                    return false;
                }

                await window.StashyEncryption.init();
                console.log("Offscreen Crypto: Encryption initialized");
                return true;
            } catch (error) {
                console.error("Offscreen Crypto: Error initializing encryption:", error);
                return false;
            }
        }

        /**
         * Encrypts data
         * @param {Object} data - The data to encrypt
         * @returns {Promise<Object>} A promise that resolves with the encrypted data
         */
        async function encryptData(data) {
            try {
                if (!window.StashyEncryption) {
                    throw new Error("StashyEncryption module not available");
                }

                // Initialize encryption if not already done
                await initializeEncryption();

                // Encrypt the data
                const encryptedData = await window.StashyEncryption.encrypt(data);
                return encryptedData;
            } catch (error) {
                console.error("Offscreen Crypto: Error encrypting data:", error);
                throw error;
            }
        }

        /**
         * Decrypts data
         * @param {Object} encryptedData - The encrypted data to decrypt
         * @returns {Promise<Object>} A promise that resolves with the decrypted data
         */
        async function decryptData(encryptedData) {
            try {
                if (!window.StashyEncryption) {
                    throw new Error("StashyEncryption module not available");
                }

                // Initialize encryption if not already done
                await initializeEncryption();

                // Decrypt the data
                const decryptedData = await window.StashyEncryption.decrypt(encryptedData);
                return decryptedData;
            } catch (error) {
                console.error("Offscreen Crypto: Error decrypting data:", error);
                throw error;
            }
        }

        // Listen for messages from the background script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            // Check if message is targeted for this offscreen document
            if (message.target === 'offscreen-crypto') {
                console.log("Offscreen Crypto: Received message:", message.action);

                if (message.action === 'encryptApiKeys') {
                    (async () => {
                        try {
                            const encryptedData = await encryptData(message.data);
                            sendResponse({ success: true, encryptedData });
                        } catch (error) {
                            console.error("Offscreen Crypto: Error encrypting API keys:", error);
                            sendResponse({ success: false, error: error.message });
                        }
                    })();
                    return true; // Keep channel open for async response
                }

                if (message.action === 'decryptApiKeys') {
                    (async () => {
                        try {
                            const decryptedData = await decryptData(message.encryptedData);
                            sendResponse({ success: true, decryptedData });
                        } catch (error) {
                            console.error("Offscreen Crypto: Error decrypting API keys:", error);
                            sendResponse({ success: false, error: error.message });
                        }
                    })();
                    return true; // Keep channel open for async response
                }
            }

            return false; // Not handled
        });

        // Initialize encryption on load
        initializeEncryption().then(success => {
            console.log(`Offscreen Crypto: Initialization ${success ? 'successful' : 'failed'}`);
        });
    </script>
</body>
</html>
