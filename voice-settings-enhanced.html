<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Stashy Voice Settings</title>
    <link rel="stylesheet" href="popup.css">
    <link rel="stylesheet" href="voice-settings-enhanced.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>
                🎤 Voice Transcription Settings
            </h1>
            <div class="header-subtitle">Configure speech recognition options for Stashy</div>
            <a href="popup.html" class="back-button">
                ← Back to Stashy
            </a>
        </header>

        <main>
            <div class="settings-container">
                <!-- Provider Card -->
                <div class="setting-card">
                    <div class="card-header">
                        <div class="icon">
                            🖥️
                        </div>
                        <h2>Speech Recognition Provider</h2>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="voice-provider">Select Provider:</label>
                            <select id="voice-provider">
                                <option value="browser" data-logo="browser">Browser Speech API (Default)</option>
                                <option value="google" data-logo="google">Google Speech-to-Text</option>
                                <option value="azure" data-logo="azure">Azure Speech Service</option>
                                <option value="assembly" data-logo="assembly">AssemblyAI</option>
                            </select>
                            <p class="help-text">Choose the service that will convert your speech to text.</p>
                        </div>

                        <div id="provider-info-browser" class="provider-info">
                            ℹ️ Built-in browser capability. No API key required.
                            <span class="feature-badge free">Free</span>
                        </div>

                        <div id="provider-info-google" class="provider-info" style="display: none;">
                            ℹ️ Google's advanced speech recognition.
                            <a href="https://cloud.google.com/speech-to-text" target="_blank">Learn more</a>
                            <span class="feature-badge premium">Premium</span>
                        </div>

                        <div id="provider-info-azure" class="provider-info" style="display: none;">
                            ℹ️ Microsoft's AI-powered speech service.
                            <a href="https://azure.microsoft.com/en-us/services/cognitive-services/speech-services/" target="_blank">Learn more</a>
                            <span class="feature-badge premium">Premium</span>
                        </div>

                        <div id="provider-info-assembly" class="provider-info" style="display: none;">
                            ℹ️ Specialized speech-to-text API.
                            <a href="https://www.assemblyai.com/" target="_blank">Learn more</a>
                            <span class="feature-badge premium">Premium</span>
                        </div>

                        <div id="api-key-container" class="api-key-container hidden">
                            <div class="api-key-header">
                                <label for="voice-api-key">API Key:</label>
                                <button type="button" id="toggle-password" class="toggle-password">
                                    👁️
                                </button>
                            </div>
                            <input type="password" id="voice-api-key" placeholder="Enter your API key (will be encrypted)">
                            <div class="security-notice">
                                <div class="security-warning">
                                    🛡️ <strong>Security Notice:</strong> Your API key will be encrypted using AES-256 encryption before storage.
                                    Only encrypted keys are stored - no unencrypted fallback is used.
                                </div>
                                <div class="key-scope-info">
                                    ℹ️ <strong>Best Practice:</strong> Ensure your API key has minimal required permissions for speech recognition only.
                                </div>
                            </div>
                            <p class="help-text">Your API key is encrypted and stored locally. It is never transmitted unencrypted.</p>
                        </div>
                    </div>
                </div>

                <!-- Language Card -->
                <div class="setting-card">
                    <div class="card-header">
                        <div class="icon">
                            🌐
                        </div>
                        <h2>Language Settings</h2>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="voice-language">Recognition Language:</label>
                            <select id="voice-language">
                                <option value="en-US">English (United States)</option>
                                <option value="en-GB">English (United Kingdom)</option>
                                <option value="es-ES">Spanish (Spain)</option>
                                <option value="fr-FR">French (France)</option>
                                <option value="de-DE">German (Germany)</option>
                                <option value="it-IT">Italian (Italy)</option>
                                <option value="ja-JP">Japanese (Japan)</option>
                                <option value="ko-KR">Korean (South Korea)</option>
                                <option value="pt-BR">Portuguese (Brazil)</option>
                                <option value="ru-RU">Russian (Russia)</option>
                                <option value="zh-CN">Chinese (Simplified, China)</option>
                            </select>
                            <p class="help-text">Select the language you'll be speaking in.</p>
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-auto-detect">
                                        <span class="checkmark"></span>
                                    </span>
                                    Auto-detect language
                                </label>
                                <div class="tooltip">
                                    ❓
                                    <span class="tooltip-text">Only available with premium providers. May reduce accuracy.</span>
                                </div>
                            </div>
                            <p class="help-text">Attempt to automatically detect the spoken language.</p>
                        </div>
                    </div>
                </div>

                <!-- Advanced Options Card -->
                <div class="setting-card">
                    <div class="card-header">
                        <div class="icon">
                            ⚙️
                        </div>
                        <h2>Advanced Options</h2>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-punctuation" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Automatic punctuation
                                </label>
                            </div>

                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-capitalization" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Automatic capitalization
                                </label>
                            </div>

                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-profanity-filter">
                                        <span class="checkmark"></span>
                                    </span>
                                    Filter profanity
                                </label>
                            </div>

                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-interim-results" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Show interim results
                                </label>
                                <div class="tooltip">
                                    ❓
                                    <span class="tooltip-text">Display partial transcriptions while you're speaking.</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group slider-container">
                            <div class="slider-header">
                                <label for="voice-silence-threshold">Stop after silence:</label>
                                <span class="slider-value" id="silence-value">15 seconds</span>
                            </div>
                            <input type="range" min="5" max="60" value="15" class="slider" id="voice-silence-threshold">
                            <p class="help-text">Automatically stop recording after this period of silence.</p>
                        </div>
                    </div>
                </div>

                <!-- Performance Card -->
                <div class="setting-card">
                    <div class="card-header">
                        <div class="icon">
                            📊
                        </div>
                        <h2>Performance & Quality</h2>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label for="voice-quality">Transcription Quality:</label>
                            <select id="voice-quality">
                                <option value="standard">Standard</option>
                                <option value="enhanced">Enhanced (Premium only)</option>
                                <option value="high">High (Premium only)</option>
                            </select>
                            <p class="help-text">Higher quality requires more processing time and may only be available with premium providers.</p>
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-background-noise-reduction">
                                        <span class="checkmark"></span>
                                    </span>
                                    Background noise reduction
                                </label>
                                <div class="tooltip">
                                    ❓
                                    <span class="tooltip-text">Reduce background noise in the audio. Premium providers only.</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-speaker-diarization">
                                        <span class="checkmark"></span>
                                    </span>
                                    Speaker identification
                                </label>
                                <div class="tooltip">
                                    ❓
                                    <span class="tooltip-text">Identify different speakers in the transcription. Premium providers only.</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Audio Processing Card -->
                <div class="setting-card">
                    <div class="card-header">
                        <div class="icon">
                            🔊
                        </div>
                        <h2>Audio Processing</h2>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-audio-monitoring" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Real-time audio monitoring
                                </label>
                                <div class="tooltip">
                                    ❓
                                    <span class="tooltip-text">Show audio level indicator and quality feedback during recording.</span>
                                </div>
                            </div>

                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-adaptive-thresholds" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Adaptive audio thresholds
                                </label>
                                <div class="tooltip">
                                    ❓
                                    <span class="tooltip-text">Automatically adjust audio sensitivity based on your environment.</span>
                                </div>
                            </div>

                            <div class="checkbox-group">
                                <label>
                                    <span class="checkbox-container">
                                        <input type="checkbox" id="voice-enhanced-constraints" checked>
                                        <span class="checkmark"></span>
                                    </span>
                                    Enhanced audio constraints
                                </label>
                                <div class="tooltip">
                                    ❓
                                    <span class="tooltip-text">Use advanced microphone settings for better audio quality.</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group slider-container">
                            <div class="slider-header">
                                <label for="voice-audio-sensitivity">Audio sensitivity:</label>
                                <span class="slider-value" id="audio-sensitivity-value">50%</span>
                            </div>
                            <input type="range" min="10" max="100" value="50" class="slider" id="voice-audio-sensitivity">
                            <p class="help-text">Adjust microphone sensitivity for your environment.</p>
                        </div>

                        <div class="form-group">
                            <label for="voice-noise-suppression">Noise suppression level:</label>
                            <select id="voice-noise-suppression">
                                <option value="low">Low</option>
                                <option value="medium" selected>Medium</option>
                                <option value="high">High</option>
                                <option value="aggressive">Aggressive</option>
                            </select>
                            <p class="help-text">Level of background noise reduction to apply.</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <div class="action-bar">
            <button type="button" id="reset-settings" class="secondary-button">
                ↺ Reset to Defaults
            </button>
            <button type="button" id="save-voice-settings" class="primary-button">
                💾 Save Settings
            </button>
        </div>
    </div>

    <div id="status-message" class="status-message">
        ✅ <span id="status-text">Settings saved successfully!</span>
    </div>

    <script src="voice-settings-enhanced.js"></script>
</body>
</html>