<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stashy Activation Guide</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .privacy-badge {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .benefit {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .extension-icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            background: #007bff;
            border-radius: 3px;
            margin: 0 5px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="icon">🔒</div>
        <h1>How to Activate Stashy</h1>
        <p><span class="privacy-badge">Privacy-First Design</span></p>
        <p>Stashy now uses a privacy-friendly activation system that gives you full control.</p>
    </div>

    <div class="step">
        <span class="step-number">1</span>
        <strong>Navigate to any webpage</strong>
        <p>Go to any website where you want to take notes or create highlights (e.g., articles, documentation, research papers).</p>
    </div>

    <div class="step">
        <span class="step-number">2</span>
        <strong>Click the Stashy extension icon</strong>
        <p>Look for the Stashy icon <span class="extension-icon"></span> in your browser's extension toolbar and click it.</p>
        <p><em>Note: If you don't see the icon, click the puzzle piece icon and pin Stashy to your toolbar.</em></p>
    </div>

    <div class="step">
        <span class="step-number">3</span>
        <strong>Extension activates on that tab</strong>
        <p>Stashy will inject its features into the current tab only. You'll see the note interface appear and can start taking notes immediately.</p>
    </div>

    <div class="step">
        <span class="step-number">4</span>
        <strong>Use all features normally</strong>
        <p>Once activated, you can use all Stashy features: notes, highlights, voice recording, screenshots, and more.</p>
    </div>

    <h2>Privacy Benefits</h2>
    
    <div class="benefit">
        <strong>🛡️ No Background Monitoring</strong><br>
        Stashy doesn't run scripts on every website you visit. It only activates when you explicitly click the icon.
    </div>

    <div class="benefit">
        <strong>🎯 Targeted Access</strong><br>
        The extension only accesses the specific tab where you activate it, not all your browsing activity.
    </div>

    <div class="benefit">
        <strong>👤 User Control</strong><br>
        You decide exactly when and where the extension operates. No automatic access to your browsing.
    </div>

    <div class="benefit">
        <strong>🔒 Minimal Permissions</strong><br>
        Uses the privacy-friendly "activeTab" permission instead of broad website access.
    </div>

    <h2>Frequently Asked Questions</h2>

    <h3>Do I need to activate Stashy on every tab?</h3>
    <p>Yes, for privacy reasons. Each tab requires individual activation by clicking the extension icon. This ensures the extension only runs where you explicitly want it.</p>

    <h3>Will my existing notes still work?</h3>
    <p>Absolutely! All your existing notes, highlights, and data remain unchanged. The only difference is how you activate the extension.</p>

    <h3>What if I forget to activate it?</h3>
    <p>Simply click the Stashy icon whenever you want to use the extension. Your notes for that page will load automatically once activated.</p>

    <h3>Is this more secure than before?</h3>
    <p>Yes! This new system is much more privacy-friendly and secure. It follows browser security best practices and gives you complete control over the extension's access.</p>

    <div style="text-align: center; margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <p><strong>Questions or issues?</strong></p>
        <p>Check our <a href="help.html">Help Guide</a> or <a href="permissions-explanation.html">Permissions Explanation</a> for more information.</p>
    </div>
</body>
</html>
