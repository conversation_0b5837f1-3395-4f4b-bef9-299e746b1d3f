/**
 * Google Docs Export UI Component for Stashy
 *
 * This module provides UI components for exporting notes and highlights to Google Docs.
 */

// Create a namespace to avoid global pollution
window.StashyGoogleDocsExport = (function() {
    // Private variables
    let isInitialized = false;
    let exportButton = null;
    let exportModal = null;

    /**
     * Initializes the Google Docs export UI
     * @returns {Promise<boolean>} A promise that resolves to true if initialization was successful
     */
    async function init() {
        try {
            // Check if already initialized
            if (isInitialized) {
                return true;
            }

            // Create export button
            createExportButton();

            // Create export modal
            createExportModal();

            // Add event listeners
            addEventListeners();

            isInitialized = true;
            return true;
        } catch (error) {
            console.error('Error initializing Google Docs export UI:', error);
            return false;
        }
    }

    /**
     * Creates the export button
     */
    function createExportButton() {
        // Check if button already exists
        if (document.getElementById('Stashy-gdocs-export-btn')) {
            exportButton = document.getElementById('Stashy-gdocs-export-btn');
            return;
        }

        // Create button
        exportButton = document.createElement('button');
        exportButton.id = 'Stashy-gdocs-export-btn';
        exportButton.className = 'Stashy-button Stashy-gdocs-btn';
        exportButton.title = 'Export to Google Docs';
        exportButton.innerHTML = '<span class="Stashy-icon">📄</span> Export to Google Docs';

        // Style the button
        exportButton.style.display = 'none'; // Hidden by default, shown when items are selected
        exportButton.style.marginTop = '10px';
        exportButton.style.padding = '8px 12px';
        exportButton.style.backgroundColor = '#4285F4'; // Google blue
        exportButton.style.color = 'white';
        exportButton.style.border = 'none';
        exportButton.style.borderRadius = '4px';
        exportButton.style.cursor = 'pointer';
        exportButton.style.fontWeight = 'bold';
        exportButton.style.fontSize = '14px';
        exportButton.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        exportButton.style.transition = 'all 0.2s ease';

        // Add hover effect
        exportButton.addEventListener('mouseover', function() {
            this.style.backgroundColor = '#3367D6'; // Darker Google blue
            this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
        });

        exportButton.addEventListener('mouseout', function() {
            this.style.backgroundColor = '#4285F4';
            this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
        });

        // Add to dashboard
        const dashboardActions = document.querySelector('.Stashy-dashboard-actions');
        if (dashboardActions) {
            dashboardActions.appendChild(exportButton);
        } else {
            // If dashboard actions container doesn't exist, create it when the dashboard is loaded
            document.addEventListener('StashyDashboardLoaded', function() {
                const dashboardActions = document.querySelector('.Stashy-dashboard-actions');
                if (dashboardActions) {
                    dashboardActions.appendChild(exportButton);
                }
            });
        }
    }

    /**
     * Creates the export modal
     */
    function createExportModal() {
        // Check if modal already exists
        if (document.getElementById('Stashy-gdocs-export-modal')) {
            exportModal = document.getElementById('Stashy-gdocs-export-modal');
            return;
        }

        // Create modal container
        exportModal = document.createElement('div');
        exportModal.id = 'Stashy-gdocs-export-modal';
        exportModal.className = 'Stashy-modal';
        exportModal.style.display = 'none';

        // Style the modal
        exportModal.style.position = 'fixed';
        exportModal.style.top = '0';
        exportModal.style.left = '0';
        exportModal.style.width = '100%';
        exportModal.style.height = '100%';
        exportModal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        exportModal.style.zIndex = '10000';
        exportModal.style.display = 'none';
        exportModal.style.justifyContent = 'center';
        exportModal.style.alignItems = 'center';

        // Create modal content
        const modalContent = document.createElement('div');
        modalContent.className = 'Stashy-modal-content';
        modalContent.style.backgroundColor = 'white';
        modalContent.style.padding = '20px';
        modalContent.style.borderRadius = '8px';
        modalContent.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.2)';
        modalContent.style.width = '400px';
        modalContent.style.maxWidth = '90%';
        modalContent.style.maxHeight = '90%';
        modalContent.style.overflow = 'auto';

        // Create modal header
        const modalHeader = document.createElement('div');
        modalHeader.className = 'Stashy-modal-header';
        modalHeader.style.display = 'flex';
        modalHeader.style.justifyContent = 'space-between';
        modalHeader.style.alignItems = 'center';
        modalHeader.style.marginBottom = '15px';

        const modalTitle = document.createElement('h3');
        modalTitle.textContent = 'Export to Google Docs';
        modalTitle.style.margin = '0';
        modalTitle.style.fontSize = '18px';
        modalTitle.style.color = '#4285F4';

        const closeButton = document.createElement('button');
        closeButton.innerHTML = '&times;';
        closeButton.className = 'Stashy-modal-close';
        closeButton.style.background = 'none';
        closeButton.style.border = 'none';
        closeButton.style.fontSize = '24px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.color = '#666';
        closeButton.style.padding = '0';
        closeButton.style.lineHeight = '1';

        modalHeader.appendChild(modalTitle);
        modalHeader.appendChild(closeButton);

        // Create modal body
        const modalBody = document.createElement('div');
        modalBody.className = 'Stashy-modal-body';

        // Document title input
        const titleLabel = document.createElement('label');
        titleLabel.textContent = 'Document Title:';
        titleLabel.style.display = 'block';
        titleLabel.style.marginBottom = '5px';
        titleLabel.style.fontWeight = 'bold';

        const titleInput = document.createElement('input');
        titleInput.type = 'text';
        titleInput.id = 'Stashy-gdocs-title';
        titleInput.className = 'Stashy-input';
        titleInput.placeholder = 'Enter document title';
        titleInput.style.width = '100%';
        titleInput.style.padding = '8px';
        titleInput.style.marginBottom = '15px';
        titleInput.style.border = '1px solid #ccc';
        titleInput.style.borderRadius = '4px';
        titleInput.style.boxSizing = 'border-box';

        // Export options
        const optionsLabel = document.createElement('label');
        optionsLabel.textContent = 'Export Options:';
        optionsLabel.style.display = 'block';
        optionsLabel.style.marginBottom = '5px';
        optionsLabel.style.fontWeight = 'bold';

        // Include URL option
        const urlOption = document.createElement('div');
        urlOption.className = 'Stashy-option';
        urlOption.style.marginBottom = '10px';

        const urlCheckbox = document.createElement('input');
        urlCheckbox.type = 'checkbox';
        urlCheckbox.id = 'Stashy-gdocs-include-url';
        urlCheckbox.checked = true;
        urlCheckbox.style.marginRight = '5px';

        const urlLabel = document.createElement('label');
        urlLabel.htmlFor = 'Stashy-gdocs-include-url';
        urlLabel.textContent = 'Include source URLs';

        urlOption.appendChild(urlCheckbox);
        urlOption.appendChild(urlLabel);

        // Include tags option
        const tagsOption = document.createElement('div');
        tagsOption.className = 'Stashy-option';
        tagsOption.style.marginBottom = '10px';

        const tagsCheckbox = document.createElement('input');
        tagsCheckbox.type = 'checkbox';
        tagsCheckbox.id = 'Stashy-gdocs-include-tags';
        tagsCheckbox.checked = true;
        tagsCheckbox.style.marginRight = '5px';

        const tagsLabel = document.createElement('label');
        tagsLabel.htmlFor = 'Stashy-gdocs-include-tags';
        tagsLabel.textContent = 'Include tags';

        tagsOption.appendChild(tagsCheckbox);
        tagsOption.appendChild(tagsLabel);

        // Format options
        const formatLabel = document.createElement('label');
        formatLabel.textContent = 'Formatting Style:';
        formatLabel.style.display = 'block';
        formatLabel.style.marginTop = '15px';
        formatLabel.style.marginBottom = '5px';
        formatLabel.style.fontWeight = 'bold';

        const formatSelect = document.createElement('select');
        formatSelect.id = 'Stashy-gdocs-format';
        formatSelect.className = 'Stashy-select';
        formatSelect.style.width = '100%';
        formatSelect.style.padding = '8px';
        formatSelect.style.marginBottom = '15px';
        formatSelect.style.border = '1px solid #ccc';
        formatSelect.style.borderRadius = '4px';
        formatSelect.style.boxSizing = 'border-box';

        const formatOptions = [
            { value: 'simple', text: 'Simple (Plain text with minimal formatting)' },
            { value: 'standard', text: 'Standard (Default formatting with colors)' },
            { value: 'academic', text: 'Academic (Formal style with citations)' }
        ];

        formatOptions.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            formatSelect.appendChild(optionElement);
        });

        // Status message
        const statusMessage = document.createElement('div');
        statusMessage.id = 'Stashy-gdocs-status';
        statusMessage.className = 'Stashy-status';
        statusMessage.style.marginTop = '10px';
        statusMessage.style.padding = '8px';
        statusMessage.style.borderRadius = '4px';
        statusMessage.style.display = 'none';

        // Add elements to modal body
        modalBody.appendChild(titleLabel);
        modalBody.appendChild(titleInput);
        modalBody.appendChild(optionsLabel);
        modalBody.appendChild(urlOption);
        modalBody.appendChild(tagsOption);
        modalBody.appendChild(formatLabel);
        modalBody.appendChild(formatSelect);
        modalBody.appendChild(statusMessage);

        // Create modal footer
        const modalFooter = document.createElement('div');
        modalFooter.className = 'Stashy-modal-footer';
        modalFooter.style.display = 'flex';
        modalFooter.style.justifyContent = 'flex-end';
        modalFooter.style.marginTop = '20px';

        const cancelButton = document.createElement('button');
        cancelButton.textContent = 'Cancel';
        cancelButton.className = 'Stashy-button Stashy-cancel-btn';
        cancelButton.style.padding = '8px 16px';
        cancelButton.style.marginRight = '10px';
        cancelButton.style.backgroundColor = '#f1f1f1';
        cancelButton.style.color = '#333';
        cancelButton.style.border = 'none';
        cancelButton.style.borderRadius = '4px';
        cancelButton.style.cursor = 'pointer';

        const exportConfirmButton = document.createElement('button');
        exportConfirmButton.textContent = 'Export';
        exportConfirmButton.id = 'Stashy-gdocs-export-confirm';
        exportConfirmButton.className = 'Stashy-button Stashy-confirm-btn';
        exportConfirmButton.style.padding = '8px 16px';
        exportConfirmButton.style.backgroundColor = '#4285F4';
        exportConfirmButton.style.color = 'white';
        exportConfirmButton.style.border = 'none';
        exportConfirmButton.style.borderRadius = '4px';
        exportConfirmButton.style.cursor = 'pointer';
        exportConfirmButton.style.fontWeight = 'bold';

        modalFooter.appendChild(cancelButton);
        modalFooter.appendChild(exportConfirmButton);

        // Assemble modal
        modalContent.appendChild(modalHeader);
        modalContent.appendChild(modalBody);
        modalContent.appendChild(modalFooter);
        exportModal.appendChild(modalContent);

        // Add to body
        document.body.appendChild(exportModal);
    }

    /**
     * Adds event listeners to UI elements
     */
    function addEventListeners() {
        // Export button click
        if (exportButton) {
            exportButton.addEventListener('click', showExportModal);
        }

        // Close button click
        const closeButton = exportModal.querySelector('.Stashy-modal-close');
        if (closeButton) {
            closeButton.addEventListener('click', hideExportModal);
        }

        // Cancel button click
        const cancelButton = exportModal.querySelector('.Stashy-cancel-btn');
        if (cancelButton) {
            cancelButton.addEventListener('click', hideExportModal);
        }

        // Export confirm button click
        const exportConfirmButton = document.getElementById('Stashy-gdocs-export-confirm');
        if (exportConfirmButton) {
            exportConfirmButton.addEventListener('click', handleExport);
        }

        // Close modal when clicking outside
        exportModal.addEventListener('click', function(event) {
            if (event.target === exportModal) {
                hideExportModal();
            }
        });

        // Listen for selection changes in the dashboard
        document.addEventListener('StashySelectionChanged', handleSelectionChange);
    }

    /**
     * Shows the export modal
     */
    function showExportModal() {
        // Set default title based on what's being exported
        const titleInput = document.getElementById('Stashy-gdocs-title');
        if (titleInput) {
            const selectedType = getSelectedItemsType();
            if (selectedType === 'notes') {
                titleInput.value = 'Stashy Notes - ' + new Date().toLocaleDateString();
            } else if (selectedType === 'highlights') {
                titleInput.value = 'Stashy Highlights - ' + new Date().toLocaleDateString();
            } else {
                titleInput.value = 'Stashy Export - ' + new Date().toLocaleDateString();
            }
        }

        // Show modal
        exportModal.style.display = 'flex';
    }

    /**
     * Hides the export modal
     */
    function hideExportModal() {
        // Hide status message
        const statusMessage = document.getElementById('Stashy-gdocs-status');
        if (statusMessage) {
            statusMessage.style.display = 'none';
        }

        // Hide modal
        exportModal.style.display = 'none';
    }

    /**
     * Handles selection changes in the dashboard
     * @param {Event} event - The selection change event
     */
    function handleSelectionChange(event) {
        const selectedItems = event.detail.selectedItems || [];

        // Show export button if items are selected
        if (selectedItems.length > 0) {
            exportButton.style.display = 'block';
        } else {
            exportButton.style.display = 'none';
        }
    }

    /**
     * Gets the type of selected items (notes, highlights, or mixed)
     * @returns {string} The type of selected items
     */
    function getSelectedItemsType() {
        // Get selected items from dashboard
        const selectedItems = window.StashyDashboard ? window.StashyDashboard.getSelectedItems() : [];

        if (selectedItems.length === 0) {
            return 'none';
        }

        // Check if all items are notes
        const allNotes = selectedItems.every(item => item.type === 'note');
        if (allNotes) {
            return 'notes';
        }

        // Check if all items are highlights
        const allHighlights = selectedItems.every(item => item.type === 'highlight');
        if (allHighlights) {
            return 'highlights';
        }

        // Mixed items
        return 'mixed';
    }

    /**
     * Handles the export button click
     */
    async function handleExport() {
        try {
            // Get selected items using the dashboard API
            let selectedItems = [];
            if (window.StashyDashboard && typeof window.StashyDashboard.getSelectedItems === 'function') {
                selectedItems = window.StashyDashboard.getSelectedItems();
                console.log('Stashy: Got selected items from dashboard API:', selectedItems);
            }

            if (selectedItems.length === 0) {
                showStatus('No items selected for export', 'error');
                console.log('Stashy: No items selected for export');
                return;
            }

            // Get export options
            const title = document.getElementById('Stashy-gdocs-title').value || 'Stashy Export';
            const includeUrl = document.getElementById('Stashy-gdocs-include-url').checked;
            const includeTags = document.getElementById('Stashy-gdocs-include-tags').checked;
            const format = document.getElementById('Stashy-gdocs-format').value;

            // Show loading status
            showStatus('Exporting to Google Docs...', 'info');

            // Initialize Google Docs integration
            if (!window.StashyGoogleDocsIntegration) {
                showStatus('Google Docs integration not available', 'error');
                return;
            }

            await window.StashyGoogleDocsIntegration.init();

            // Separate notes and highlights
            const notes = selectedItems.filter(item => item.type === 'note');
            const highlights = selectedItems.filter(item => item.type === 'highlight');

            let result;

            // Export based on item types
            if (notes.length > 0 && highlights.length === 0) {
                // Export notes only
                result = await window.StashyGoogleDocsIntegration.exportNotes(notes, title);
            } else if (highlights.length > 0 && notes.length === 0) {
                // Export highlights only
                result = await window.StashyGoogleDocsIntegration.exportHighlights(highlights, title);
            } else {
                // Export both notes and highlights
                // Create a combined document
                result = await window.StashyGoogleDocsIntegration.exportNotes([...notes, ...highlights], title);
            }

            if (result.success) {
                showStatus(`Export successful! <a href="${result.documentUrl}" target="_blank">Open Document</a>`, 'success');
            } else {
                showStatus(`Export failed: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Error exporting to Google Docs:', error);
            showStatus(`Export failed: ${error.message || 'Unknown error'}`, 'error');
        }
    }

    /**
     * Shows a status message in the modal
     * @param {string} message - The message to show
     * @param {string} type - The type of message (success, error, info)
     */
    function showStatus(message, type) {
        const statusMessage = document.getElementById('Stashy-gdocs-status');
        if (!statusMessage) return;

        // Set message
        statusMessage.innerHTML = message;

        // Set style based on type
        statusMessage.style.display = 'block';

        if (type === 'success') {
            statusMessage.style.backgroundColor = '#d4edda';
            statusMessage.style.color = '#155724';
            statusMessage.style.border = '1px solid #c3e6cb';
        } else if (type === 'error') {
            statusMessage.style.backgroundColor = '#f8d7da';
            statusMessage.style.color = '#721c24';
            statusMessage.style.border = '1px solid #f5c6cb';
        } else {
            statusMessage.style.backgroundColor = '#cce5ff';
            statusMessage.style.color = '#004085';
            statusMessage.style.border = '1px solid #b8daff';
        }
    }

    // Return the public API
    return {
        init,
        showExportModal,
        hideExportModal,
        isInitialized: () => isInitialized
    };
})();

// Initialize when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize after a short delay to ensure dashboard is loaded
    setTimeout(() => {
        window.StashyGoogleDocsExport.init();
    }, 1000);
});

console.log("Stashy: Google Docs Export UI Module Loaded");
