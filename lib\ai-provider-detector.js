/**
 * AI Provider Detection Module
 * Automatically detects AI service providers based on API key patterns and validation
 */

(function() {
    'use strict';

    // AI Provider configurations
    const AI_PROVIDERS = {
        openai: {
            name: 'OpenAI',
            displayName: 'OpenAI (ChatGPT)',
            icon: '🤖',
            patterns: [
                /^sk-[a-zA-Z0-9]{48}$/,           // Standard OpenAI API key
                /^sk-proj-[a-zA-Z0-9]{48}$/,     // Project-based API key
                /^sk-[a-zA-Z0-9-_]{20,}$/        // General OpenAI pattern
            ],
            testEndpoint: 'https://api.openai.com/v1/models',
            testMethod: 'GET',
            authHeader: 'Authorization',
            authPrefix: 'Bearer ',
            models: {
                'gpt-3.5-turbo': { name: 'GPT-3.5 Turbo', maxTokens: 4096, contextWindow: 16385 },
                'gpt-3.5-turbo-1106': { name: 'GPT-3.5 Turbo (1106)', maxTokens: 4096, contextWindow: 16385 },
                'gpt-4o-mini': { name: 'GPT-4o Mini', maxTokens: 16384, contextWindow: 128000 },
                'gpt-4': { name: 'GPT-4 (Requires Plus/Pro)', maxTokens: 8192, contextWindow: 8192 },
                'gpt-4-turbo-preview': { name: 'GPT-4 Turbo Preview (Requires Plus/Pro)', maxTokens: 4096, contextWindow: 128000 },
                'gpt-4-1106-preview': { name: 'GPT-4 Turbo (1106) (Requires Plus/Pro)', maxTokens: 4096, contextWindow: 128000 }
            },
            defaultModel: 'gpt-3.5-turbo',
            apiVersion: 'v1'
        },
        anthropic: {
            name: 'Anthropic',
            displayName: 'Anthropic (Claude)',
            icon: '🧠',
            patterns: [
                /^sk-ant-[a-zA-Z0-9-_]{95,}$/,   // Anthropic API key pattern
                /^sk-ant-api03-[a-zA-Z0-9-_]{95,}$/ // Alternative pattern
            ],
            testEndpoint: 'https://api.anthropic.com/v1/messages',
            testMethod: 'POST',
            authHeader: 'x-api-key',
            authPrefix: '',
            models: {
                'claude-3-haiku-20240307': { name: 'Claude 3 Haiku', maxTokens: 4096, contextWindow: 200000 },
                'claude-3-sonnet-20240229': { name: 'Claude 3 Sonnet', maxTokens: 4096, contextWindow: 200000 },
                'claude-3-opus-20240229': { name: 'Claude 3 Opus', maxTokens: 4096, contextWindow: 200000 }
            },
            defaultModel: 'claude-3-haiku-20240307', // Most cost-effective model first
            apiVersion: '2023-06-01',
            additionalHeaders: {
                'anthropic-version': '2023-06-01'
            }
        },
        google: {
            name: 'Google',
            displayName: 'Google AI (Gemini)',
            icon: '🔵',
            patterns: [
                /^AIza[a-zA-Z0-9-_]{35}$/,        // Google AI API key pattern
                /^AIza[a-zA-Z0-9]{35,39}$/       // Alternative pattern
            ],
            testEndpoint: 'https://generativelanguage.googleapis.com/v1beta/models',
            testMethod: 'GET',
            authHeader: null, // Uses query parameter
            authPrefix: '',
            models: {
                'gemini-2.5-pro': { name: 'Gemini 2.5 Pro', maxTokens: 8192, contextWindow: 2000000 },
                'gemini-2.5-flash': { name: 'Gemini 2.5 Flash', maxTokens: 8192, contextWindow: 1000000 },
                'gemini-2.5-flash-preview-tts': { name: 'Gemini 2.5 Flash Preview TTS', maxTokens: 8192, contextWindow: 1000000 },
                'gemini-2.5-pro-preview-tts': { name: 'Gemini 2.5 Pro Preview TTS', maxTokens: 8192, contextWindow: 2000000 },
                'gemini-1.5-flash': { name: 'Gemini 1.5 Flash', maxTokens: 8192, contextWindow: 1000000 },
                'gemini-1.5-pro': { name: 'Gemini 1.5 Pro', maxTokens: 8192, contextWindow: 2000000 },
                'gemini-2.0-flash-exp': { name: 'Gemini 2.0 Flash (Experimental)', maxTokens: 8192, contextWindow: 1000000 },
                'gemini-exp-1206': { name: 'Gemini Experimental 1206', maxTokens: 8192, contextWindow: 2000000 },
                'gemini-pro': { name: 'Gemini Pro', maxTokens: 8192, contextWindow: 32768 }
            },
            defaultModel: 'gemini-2.5-flash',
            apiVersion: 'v1beta'
        },
        cohere: {
            name: 'Cohere',
            displayName: 'Cohere',
            icon: '🌟',
            patterns: [
                /^[a-zA-Z0-9]{40}$/,             // Cohere API key pattern
                /^co-[a-zA-Z0-9-_]{32,}$/       // Alternative pattern
            ],
            testEndpoint: 'https://api.cohere.ai/v1/models',
            testMethod: 'GET',
            authHeader: 'Authorization',
            authPrefix: 'Bearer ',
            models: {
                'command': { name: 'Command', maxTokens: 4096, contextWindow: 4096 },
                'command-nightly': { name: 'Command Nightly', maxTokens: 4096, contextWindow: 4096 }
            },
            defaultModel: 'command',
            apiVersion: 'v1'
        },
        huggingface: {
            name: 'HuggingFace',
            displayName: 'Hugging Face',
            icon: '🤗',
            patterns: [
                /^hf_[a-zA-Z0-9]{34}$/,          // Hugging Face API token
                /^hf_[a-zA-Z0-9-_]{30,}$/       // Alternative pattern
            ],
            testEndpoint: 'https://api-inference.huggingface.co/models',
            testMethod: 'GET',
            authHeader: 'Authorization',
            authPrefix: 'Bearer ',
            models: {
                'microsoft/DialoGPT-large': { name: 'DialoGPT Large', maxTokens: 1024, contextWindow: 1024 },
                'facebook/blenderbot-400M-distill': { name: 'BlenderBot', maxTokens: 512, contextWindow: 512 }
            },
            defaultModel: 'microsoft/DialoGPT-large',
            apiVersion: 'v1'
        }
    };

    /**
     * Detects AI provider based on API key pattern
     * @param {string} apiKey - The API key to analyze
     * @returns {Array} Array of potential providers with confidence scores
     */
    function detectProviderByPattern(apiKey) {
        if (!apiKey || typeof apiKey !== 'string') {
            return [];
        }

        const cleanKey = apiKey.trim();
        const detections = [];

        for (const [providerId, config] of Object.entries(AI_PROVIDERS)) {
            for (const pattern of config.patterns) {
                if (pattern.test(cleanKey)) {
                    // Calculate confidence based on pattern specificity
                    let confidence = 0.8; // Base confidence for pattern match
                    
                    // Increase confidence for more specific patterns
                    if (pattern.source.includes('sk-ant-')) confidence = 0.95;
                    if (pattern.source.includes('sk-proj-')) confidence = 0.95;
                    if (pattern.source.includes('AIza')) confidence = 0.9;
                    if (pattern.source.includes('hf_')) confidence = 0.9;
                    
                    detections.push({
                        providerId,
                        provider: config,
                        confidence,
                        method: 'pattern',
                        pattern: pattern.source
                    });
                    break; // Only match first pattern per provider
                }
            }
        }

        // Sort by confidence (highest first)
        return detections.sort((a, b) => b.confidence - a.confidence);
    }

    /**
     * Validates API key by making a test call to the provider
     * @param {string} providerId - The provider identifier
     * @param {string} apiKey - The API key to validate
     * @returns {Promise<Object>} Validation result with success status and details
     */
    async function validateApiKey(providerId, apiKey) {
        const provider = AI_PROVIDERS[providerId];
        if (!provider) {
            throw new Error(`Unknown provider: ${providerId}`);
        }

        try {
            console.log(`AI Provider Detector: Validating ${provider.displayName} API key...`);

            // Prepare test request
            const testUrl = provider.testEndpoint + (provider.name === 'Google' ? `?key=${apiKey}` : '');
            const headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Stashy-Extension/1.0'
            };

            // Add authentication header if required
            if (provider.authHeader) {
                headers[provider.authHeader] = provider.authPrefix + apiKey;
            }

            // Add additional headers if specified
            if (provider.additionalHeaders) {
                Object.assign(headers, provider.additionalHeaders);
            }

            // Make test request through background script for CORS handling
            const response = await chrome.runtime.sendMessage({
                action: 'validateApiKey',
                providerId,
                url: testUrl,
                method: provider.testMethod,
                headers
            });

            if (response && response.success) {
                console.log(`AI Provider Detector: ${provider.displayName} API key validated successfully`);
                return {
                    success: true,
                    providerId,
                    provider: provider.displayName,
                    models: response.models || Object.keys(provider.models),
                    details: response.details
                };
            } else {
                console.warn(`AI Provider Detector: ${provider.displayName} API key validation failed:`, response?.error);
                return {
                    success: false,
                    providerId,
                    provider: provider.displayName,
                    error: response?.error || 'Validation failed',
                    details: response?.details
                };
            }

        } catch (error) {
            console.error(`AI Provider Detector: Error validating ${provider.displayName} API key:`, error);
            return {
                success: false,
                providerId,
                provider: provider.displayName,
                error: error.message,
                details: { originalError: error }
            };
        }
    }

    /**
     * Performs comprehensive API key detection and validation
     * @param {string} apiKey - The API key to detect and validate
     * @param {Object} options - Detection options
     * @returns {Promise<Object>} Detection and validation results
     */
    async function detectAndValidateProvider(apiKey, options = {}) {
        const { skipValidation = true, testAllMatches = false } = options; // Default to skip validation to avoid rate limits

        try {
            console.log('AI Provider Detector: Starting provider detection...');

            // Step 1: Pattern-based detection
            const patternDetections = detectProviderByPattern(apiKey);
            
            if (patternDetections.length === 0) {
                return {
                    success: false,
                    error: 'No matching AI provider patterns found',
                    detections: [],
                    validations: []
                };
            }

            console.log(`AI Provider Detector: Found ${patternDetections.length} potential provider(s):`, 
                patternDetections.map(d => `${d.provider.displayName} (${Math.round(d.confidence * 100)}%)`));

            // Step 2: Validation (if not skipped)
            const validations = [];
            if (!skipValidation) {
                const detectionsToTest = testAllMatches ? patternDetections : [patternDetections[0]];
                
                for (const detection of detectionsToTest) {
                    const validation = await validateApiKey(detection.providerId, apiKey);
                    validations.push({
                        ...detection,
                        validation
                    });

                    // If we found a successful validation and not testing all, break
                    if (validation.success && !testAllMatches) {
                        break;
                    }
                }
            }

            // Step 3: Determine final result
            const successfulValidation = validations.find(v => v.validation.success);
            const bestDetection = successfulValidation || patternDetections[0];

            return {
                success: skipValidation || !!successfulValidation,
                detectedProvider: bestDetection.providerId,
                providerConfig: bestDetection.provider,
                confidence: bestDetection.confidence,
                detections: patternDetections,
                validations,
                finalValidation: successfulValidation?.validation || null
            };

        } catch (error) {
            console.error('AI Provider Detector: Error during detection:', error);
            return {
                success: false,
                error: error.message,
                detections: [],
                validations: []
            };
        }
    }

    // Public API
    window.aiProviderDetector = {
        detectProviderByPattern,
        validateApiKey,
        detectAndValidateProvider,
        getProviderConfig: (providerId) => AI_PROVIDERS[providerId],
        getAllProviders: () => AI_PROVIDERS,
        getSupportedProviders: () => Object.keys(AI_PROVIDERS)
    };

    console.log('AI Provider Detector: Module loaded with support for:', Object.keys(AI_PROVIDERS).join(', '));

})();
