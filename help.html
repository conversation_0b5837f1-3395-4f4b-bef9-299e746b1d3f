<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stashy Help Guide</title>
    <link rel="stylesheet" href="help.css">
</head>
<body>
    <h1>Stashy Help Guide</h1>

    <div class="toc">
        <h2>Table of Contents</h2>
        <ul>
            <li><a href="#activation">How to Activate Stashy</a></li>
            <li><a href="#getting-started">Getting Started</a></li>
            <li><a href="#creating-notes">Creating and Managing Notes</a></li>
            <li><a href="#highlighting">Text Highlighting</a></li>
            <li><a href="#ai-features">AI Features</a></li>

            <li><a href="#voice-transcription">Voice Transcription</a></li>
            <li><a href="#flashcards">Flashcards & Study Mode</a></li>
            <li><a href="#templates">Templates & Content Creation</a></li>
            <li><a href="#screenshots">Screenshots & Media Capture</a></li>
            <li><a href="#dashboard">Dashboard & Organization</a></li>
            <li><a href="#google-integration">Google Drive & Calendar Integration</a></li>
            <li><a href="#customization">UI Customization</a></li>
            <li><a href="#advanced-features">Advanced Features</a></li>
            <li><a href="#troubleshooting">Troubleshooting</a></li>
        </ul>
    </div>

    <h2 id="activation">How to Activate Stashy</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">⚡</div>
            <h3 class="feature-title">Activating Stashy on Webpages</h3>
        </div>

        <p>Stashy needs to be activated on each webpage before you can use its features. Here's how:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Automatic Activation:</strong> Click the Stashy extension icon in your browser toolbar. This will automatically activate Stashy on the current webpage.
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Manual Activation:</strong> If the extension doesn't activate automatically, you'll see a message in the popup. Click "Activate Extension" to enable Stashy on the current page.
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Verification:</strong> Once activated, you should see the note toggle button appear on the webpage (usually in the bottom-right corner).
            </div>
        </div>

        <div class="warning">
            <strong>Important:</strong> Some websites with strict security policies may prevent Stashy from activating. If you encounter issues, try refreshing the page and clicking the extension icon again.
        </div>

        <h4>Troubleshooting Activation Issues</h4>
        <ul>
            <li><strong>Extension not responding:</strong> Refresh the page and try again</li>
            <li><strong>No toggle button appears:</strong> Check if the website allows extensions to run</li>
            <li><strong>Permission errors:</strong> Make sure Stashy has permission to access the website</li>
        </ul>
    </div>

    <h2 id="getting-started">Getting Started</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">🚀</div>
            <h3 class="feature-title">First Steps with Stashy</h3>
        </div>

        <p>Welcome to Stashy! Here's how to get started:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> Click the Stashy icon in your browser toolbar to open the popup.
            </div>
            <div class="step">
                <span class="step-number">2.</span> On any webpage, press on Stashy icon to create a new note.
            </div>
            <div class="step">
                <span class="step-number">3.</span> Type your note content and it will be automatically saved.
            </div>
        </div>


    </div>

    <h2 id="creating-notes">Creating and Managing Notes</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">📝</div>
            <h3 class="feature-title">Working with Notes</h3>
        </div>

        <p>Stashy allows you to create and manage notes on any webpage:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> Click the Stashy icon to create a new note.
            </div>
            <div class="step">
                <span class="step-number">2.</span> Use the formatting toolbar to style your text.
            </div>
            <div class="step">
                <span class="step-number">3.</span> Add tags by typing in the tags field at the bottom of the note.
            </div>
            <div class="step">
                <span class="step-number">4.</span> Set reminders by clicking the calendar icon and selecting a date/time.
            </div>
        </div>

        <h4>Global vs. URL-specific Notes</h4>
        <p>You can create two types of notes:</p>
        <ul>
            <li><strong>URL-specific notes:</strong> Only appear on the specific webpage where they were created (default).</li>
            <li><strong>Global notes:</strong> Appear on all webpages. Toggle by clicking the globe icon in the note toolbar.</li>
        </ul>
    </div>

    <h2 id="highlighting">Text Highlighting</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">🖌️</div>
            <h3 class="feature-title">Highlighting Text</h3>
        </div>

        <p>Highlight important text on any webpage with multiple styles and colors:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> Select text on a webpage.
            </div>
            <div class="step">
                <span class="step-number">2.</span> Right-click and choose a highlight style from the Stashy menu.
            </div>
            <div class="step">
                <span class="step-number">3.</span> To remove a highlight, right-click on it and select "Remove Highlight".
            </div>
        </div>

        <h4>Available Highlight Styles</h4>
        <ul>
            <li><strong>Colors:</strong> Yellow, Pink, Blue, Green, Purple</li>
            <li><strong>Styles:</strong> Color fill, Underline, Wavy underline, Border, Strikethrough</li>
            <li><strong>Combinations:</strong> Mix colors with different styles for visual organization</li>
        </ul>


    </div>

    <h2 id="ai-features">AI Features</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">🤖</div>
            <h3 class="feature-title">Universal AI System</h3>
        </div>

        <p>Stashy features a powerful universal AI system that supports multiple AI providers. Choose from OpenAI (ChatGPT), Anthropic (Claude), Google AI (Gemini), Cohere, or Hugging Face - all with automatic provider detection and seamless integration.</p>

        <h4>🔑 AI Setup & Configuration</h4>
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Get an API Key:</strong> Choose your preferred AI provider and get an API key:
                <ul style="margin-top: 8px;">
                    <li><strong>Google AI (Recommended):</strong> <a href="https://aistudio.google.com/app/apikey" target="_blank">Get API key</a> - Generous free tier</li>
                    <li><strong>OpenAI:</strong> <a href="https://platform.openai.com/api-keys" target="_blank">Get API key</a> - Popular and reliable</li>
                    <li><strong>Anthropic:</strong> <a href="https://console.anthropic.com/" target="_blank">Get API key</a> - Safety-focused AI</li>
                    <li><strong>Cohere:</strong> <a href="https://dashboard.cohere.ai/api-keys" target="_blank">Get API key</a> - Enterprise-grade</li>
                    <li><strong>Hugging Face:</strong> <a href="https://huggingface.co/settings/tokens" target="_blank">Get token</a> - Open-source models</li>
                </ul>
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Configure in Stashy:</strong> Open Settings → AI Features, paste your API key, and click "Detect & Save"
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Automatic Detection:</strong> Stashy automatically detects your provider and configures optimal settings
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Test Connection:</strong> Use the "Test Connection" button to verify everything works
            </div>
        </div>

        <h4>🌐 Web Page AI (13 Features)</h4>
        <p>AI operations on webpage content:</p>
        <ul>
            <li><strong>Summarize Page:</strong> Generate comprehensive summaries of entire webpages</li>
            <li><strong>Quick Summary:</strong> Get brief, essential summaries for fast reading</li>
            <li><strong>Video Summary:</strong> Extract and summarize video content from YouTube and other platforms</li>
            <li><strong>Video Key Points:</strong> Identify and analyze key points from video content with timestamps</li>
            <li><strong>Video Deep Analysis:</strong> Comprehensive video analysis with clickable timestamps and topic breakdown</li>
            <li><strong>Transcript Analysis:</strong> AI-powered analysis of video transcripts with clickable timestamps and comprehensive insights</li>
            <li><strong>Complete Video Intelligence:</strong> Ultimate comprehensive video analysis with all intelligence components: main topics with timestamps, key insights timeline, action items, deep dive topics, metadata analysis, sentiment assessment, speaker analysis, content categorization, engagement insights, related content suggestions, and summary statistics</li>
            <li><strong>Extract Key Text:</strong> Pull out the most important text from complex pages</li>
            <li><strong>Key Points with Markers:</strong> Identify and highlight key information with visual markers</li>
            <li><strong>Deep Research:</strong> Analyze webpage topic and perform comprehensive research with additional insights</li>
            <li><strong>Generate Q&A:</strong> Create customizable question-answer pairs from webpage content (5-50 pairs)</li>
            <li><strong>Topic Explanation:</strong> Detailed explanations and background information about topics mentioned on the webpage</li>
            <li><strong>Selection Summary:</strong> Summarize only the text you've selected on the page</li>
        </ul>

        <h4>📝 Note AI (11 Features)</h4>
        <p>AI operations on your note content:</p>
        <ul>
            <li><strong>Summarize Note:</strong> Create concise summaries of your existing notes</li>
            <li><strong>Detailed Summary:</strong> Generate comprehensive summaries with more context</li>
            <li><strong>Bullet Points:</strong> Convert content into clear, organized bullet points</li>
            <li><strong>Action Items:</strong> Extract tasks and actionable items from your notes</li>
            <li><strong>Enhance Text:</strong> Improve clarity, structure, and readability</li>
            <li><strong>Simplify Text:</strong> Make complex content easier to understand</li>
            <li><strong>Expand Content:</strong> Add more detail and context to brief notes</li>
            <li><strong>Grammar & Style:</strong> Correct grammar and improve writing style</li>
            <li><strong>Generate Outline:</strong> Create structured outlines from your content</li>
            <li><strong>Flashcards:</strong> Generate Q&A flashcard pairs for studying</li>
            <li><strong>Q&A Format:</strong> Convert content into question-answer format</li>
        </ul>

        <h4>🔍 AI Search & Research (5 Features)</h4>
        <p>Intelligent search and content generation:</p>
        <ul>
            <li><strong>Semantic Search:</strong> Find notes using natural language queries</li>
            <li><strong>Content Generation:</strong> Create new content based on your existing notes</li>
            <li><strong>Topic Research:</strong> Generate comprehensive research on specific topics</li>
            <li><strong>Email Generation:</strong> Create emails about webpage topics with automatic context</li>
            <li><strong>Cross-Note Analysis:</strong> Find connections and patterns across all your notes</li>
        </ul>

        <h4>🎓 Academic Problem Solver</h4>
        <p>Advanced STEM problem solving with step-by-step solutions:</p>
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Select Problem Text:</strong> Highlight the academic problem or question on any webpage
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Access Feature:</strong> Click the "🎓 Academic Solver" button in your note interface
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>AI Analysis:</strong> Advanced AI analyzes the problem and provides detailed step-by-step solutions
            </div>
        </div>
        <ul>
            <li><strong>Subject Detection:</strong> Automatically identifies academic subjects (Math, Physics, Chemistry, etc.)</li>
            <li><strong>Step-by-Step Solutions:</strong> Detailed explanations with reasoning</li>
            <li><strong>Chain-of-Thought Processing:</strong> Enhanced reasoning for complex problems</li>
            <li><strong>Manual Selection Required:</strong> Works with any text you select, not automatic detection</li>
        </ul>

        <div class="tip">
            <strong>Privacy & Security:</strong> Your API key is stored securely in your browser and only used for AI requests. You control your usage and billing directly through your chosen provider. No data is shared with Stashy - all processing happens between your browser and your chosen AI provider.
        </div>
    </div>



    <h2 id="flashcards">Flashcards & Study Mode</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">🎓</div>
            <h3 class="feature-title">Interactive Study System</h3>
        </div>

        <p>Transform your notes into interactive flashcards for effective studying and knowledge retention.</p>

        <h4>📝 Creating Flashcards</h4>
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Manual Creation:</strong> Type flashcards in your notes using the format:<br>
                <code>Q: What is the capital of France?<br>A: Paris</code>
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>AI Generation:</strong> Use the Note AI "Flashcards" feature to automatically generate Q&A pairs from your content
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Mixed Format:</strong> Combine AI-generated and manually created flashcards in the same note
            </div>
        </div>

        <h4>🎯 Study Mode Features</h4>
        <ul>
            <li><strong>Interactive Cards:</strong> Click or press Space/Enter to reveal answers</li>
            <li><strong>Navigation:</strong> Use arrow keys or buttons to move between cards</li>
            <li><strong>Progress Tracking:</strong> See your position in the deck</li>
            <li><strong>Keyboard Shortcuts:</strong> Full keyboard navigation support</li>
            <li><strong>Accessibility:</strong> Screen reader compatible with ARIA labels</li>
        </ul>

        <h4>⚡ Quick Access</h4>
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> Open any note containing Q&A format content
            </div>
            <div class="step">
                <span class="step-number">2.</span> Click the "Study Mode" button in the note toolbar
            </div>
            <div class="step">
                <span class="step-number">3.</span> Start studying with interactive flashcards
            </div>
        </div>

        <h4>📚 Supported Formats</h4>
        <ul>
            <li><strong>Simple Q&A:</strong> <code>Q: Question<br>A: Answer</code></li>
            <li><strong>Multi-line Content:</strong> Questions and answers can span multiple lines</li>
            <li><strong>Rich Content:</strong> Supports basic HTML formatting in answers</li>
            <li><strong>Mixed Content:</strong> Combine flashcards with regular note content</li>
        </ul>

        <div class="tip">
            <strong>Study Tip:</strong> Use AI to generate initial flashcards from your notes, then manually refine and add your own cards for comprehensive study materials.
        </div>
    </div>

    <h2 id="templates">Templates & Content Creation</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">📋</div>
            <h3 class="feature-title">Advanced Smart Templates & Content Tools</h3>
        </div>

        <p>Create structured content quickly with Stashy's advanced template system featuring smart placeholders, conditional logic, visual builders, and professional content tools.</p>

        <h4>📝 Built-in Template Library</h4>
        <ul>
            <li><strong>Page Context:</strong> Automatically captures page URL, date, and selected text</li>
            <li><strong>Project Planning:</strong> Comprehensive project organization and tracking templates</li>
            <li><strong>Research Notes:</strong> Academic and research documentation formats</li>
            <li><strong>Daily Journal:</strong> Personal reflection and daily planning templates</li>
            <li><strong>Meeting Notes:</strong> Structured meeting documentation with action items</li>
            <li><strong>Study Templates:</strong> Learning-focused formats with Q&A sections</li>
        </ul>

        <h4>🔧 Smart Placeholders & Dynamic Content</h4>
        <p>Templates support advanced dynamic placeholders that auto-populate with intelligent content:</p>
        <ul>
            <li><strong>{DATE}:</strong> Current date (YYYY-MM-DD format)</li>
            <li><strong>{URL}:</strong> Current webpage URL</li>
            <li><strong>{SELECTION}:</strong> Currently selected text on the page</li>
            <li><strong>{{CurrentTime}}:</strong> Current time (HH:MM format)</li>
            <li><strong>{{Tags}}:</strong> Current note's tags</li>
            <li><strong>{{ReminderDate}}:</strong> Current note's reminder date</li>
            <li><strong>{{Prompt:question|default}}:</strong> Interactive user prompts with dropdown support</li>
            <li><strong>{{If:condition}}...{{EndIf}}:</strong> Conditional logic for dynamic content</li>
            <li><strong>{{DueDate+7days}}:</strong> Date calculations (add/subtract days)</li>
        </ul>

        <h4>🎨 Visual Template Builder</h4>
        <p>Create custom templates with an intuitive drag-and-drop interface:</p>
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Access Builder:</strong> Go to Insert dropdown → Visual Template Builder
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Design Layout:</strong> Drag and drop components to create custom layouts
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Configure Elements:</strong> Double-click elements to edit content and properties
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Preview & Test:</strong> Preview your template with live placeholder processing
            </div>
            <div class="step">
                <span class="step-number">5.</span> <strong>Save & Reuse:</strong> Save custom templates for future use
            </div>
        </div>

        <h4>🧩 Available Template Components</h4>
        <ul>
            <li><strong>Text Elements:</strong> Headers (H1-H6), paragraphs, quotes, dividers, code blocks</li>
            <li><strong>Lists & Organization:</strong> Bullet lists, numbered lists, checkboxes, task lists</li>
            <li><strong>Interactive Elements:</strong> Buttons, input fields, user prompts, dropdown selections</li>
            <li><strong>Data & Structure:</strong> Tables, date fields, progress bars, status indicators</li>
            <li><strong>Media & Visual:</strong> Image placeholders, video embeds, diagrams, charts</li>
            <li><strong>Advanced:</strong> Conditional blocks, loops, calculations, dynamic content</li>
        </ul>

        <h4>📊 Advanced Table Builder</h4>
        <ul>
            <li><strong>Multiple Styles:</strong> Basic, bordered, striped, data tables, comparison tables</li>
            <li><strong>Smart Syntax:</strong> Interactive prompts for table content and structure</li>
            <li><strong>Dynamic Content:</strong> Tables that populate from placeholders and user input</li>
            <li><strong>Responsive Design:</strong> Tables that adapt to all screen sizes</li>
            <li><strong>Header Options:</strong> Configurable headers with sorting and filtering</li>
            <li><strong>Export Ready:</strong> Tables render perfectly in all export formats</li>
        </ul>

        <h4>🔢 Professional Equation Editor</h4>
        <ul>
            <li><strong>Full LaTeX Support:</strong> Complete mathematical notation system</li>
            <li><strong>Live Preview:</strong> Real-time equation rendering as you type</li>
            <li><strong>Formula Library:</strong> Quick access to common equations and symbols</li>
            <li><strong>Advanced Features:</strong> Matrices, integrals, summations, complex expressions</li>
            <li><strong>Export Compatibility:</strong> Equations render properly in all export formats</li>
            <li><strong>Accessibility:</strong> Screen reader compatible with alt text</li>
        </ul>

        <h4>🎯 Smart Template Processing</h4>
        <ul>
            <li><strong>Conditional Logic:</strong> Show/hide content based on conditions</li>
            <li><strong>Date Calculations:</strong> Automatic date arithmetic and formatting</li>
            <li><strong>User Interactions:</strong> Prompts, dropdowns, and input validation</li>
            <li><strong>Context Awareness:</strong> Templates adapt to current page and note content</li>
            <li><strong>Error Handling:</strong> Graceful fallbacks for missing data</li>
            <li><strong>Performance:</strong> Optimized processing for complex templates</li>
        </ul>

        <div class="tip">
            <strong>Pro Tips:</strong> Use Ctrl+Z/Ctrl+Y for undo/redo in the Visual Template Builder. Double-click any element to edit directly. Combine multiple placeholders for powerful dynamic content. Test templates with different scenarios to ensure they work in all contexts.
        </div>
    </div>

    <h2 id="dashboard">Dashboard & Organization</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">📊</div>
            <h3 class="feature-title">Comprehensive Management Interface</h3>
        </div>

        <p>The Stashy Dashboard is your command center for managing all notes, highlights, and content across every webpage you visit. Access it by clicking "📊 View Dashboard" in the popup.</p>

        <h4>📚 Advanced Notebook Organization</h4>
        <ul>
            <li><strong>Custom Notebooks:</strong> Create unlimited themed collections for different projects, subjects, or purposes</li>
            <li><strong>Drag & Drop Management:</strong> Effortlessly move notes between notebooks with visual feedback</li>
            <li><strong>Smart Categories:</strong> Built-in "All Notes" and "Ungrouped" views plus your custom notebooks</li>
            <li><strong>Notebook Operations:</strong> Create, rename, delete, and reorganize notebooks with full control</li>
            <li><strong>Sidebar Navigation:</strong> Quick access to all notebooks with note counts and status indicators</li>
            <li><strong>Hierarchical Organization:</strong> Organize complex projects with nested notebook structures</li>
        </ul>

        <h4>🔍 Powerful Search & Filtering System</h4>
        <ul>
            <li><strong>Global Search:</strong> Search across all notes by content, title, tags, URL, or any text</li>
            <li><strong>Advanced Filters:</strong> Filter by reminders, important notes, highlight colors, and styles</li>
            <li><strong>View Options:</strong> All notes, recent (last 7 days), current page only, or custom date ranges</li>
            <li><strong>Sort Controls:</strong> Sort by creation date, modification date, URL, title, or alphabetically</li>
            <li><strong>Semantic Search:</strong> AI-powered search that understands context and meaning</li>
            <li><strong>Real-time Results:</strong> Instant search results as you type with highlighting</li>
            <li><strong>Search History:</strong> Quick access to recent searches and saved search queries</li>
        </ul>

        <h4>⚡ Bulk Operations & Batch Processing</h4>
        <ul>
            <li><strong>Multi-Select Interface:</strong> Select multiple notes or highlights with checkboxes</li>
            <li><strong>Bulk Export:</strong> Export selected items in multiple formats (TXT, MD, HTML, PDF)</li>
            <li><strong>Batch Move:</strong> Move multiple items to different notebooks simultaneously</li>
            <li><strong>Bulk Delete:</strong> Remove multiple items with safety confirmation</li>
            <li><strong>Select All/None:</strong> Quick selection controls for entire pages or filtered results</li>
            <li><strong>Bulk Tagging:</strong> Add or remove tags from multiple notes at once</li>
            <li><strong>Status Updates:</strong> Batch mark as important, set reminders, or update properties</li>
        </ul>

        <h4>📄 Advanced Content Management</h4>
        <ul>
            <li><strong>Dual Tab System:</strong> Separate, optimized views for Notes and Highlights</li>
            <li><strong>Rich Content Preview:</strong> Full content modal with zoom controls and formatting</li>
            <li><strong>Inline Editing:</strong> Edit notes directly from the dashboard without leaving the page</li>
            <li><strong>Smart Navigation:</strong> Previous/Next navigation through filtered results</li>
            <li><strong>Quick Actions:</strong> One-click access to edit, delete, export, or open in original page</li>
            <li><strong>Content Thumbnails:</strong> Visual previews of notes with images and formatting</li>
            <li><strong>Metadata Display:</strong> Creation date, modification date, word count, and source URL</li>
        </ul>

        <h4>📈 Statistics & Analytics Dashboard</h4>
        <ul>
            <li><strong>Usage Statistics:</strong> Total notes, highlights, notebooks, and storage usage</li>
            <li><strong>Activity Tracking:</strong> Recent activity timeline and modification history</li>
            <li><strong>Performance Metrics:</strong> Storage optimization status and system health</li>
            <li><strong>Content Analytics:</strong> Most used tags, popular websites, and content patterns</li>
            <li><strong>Growth Tracking:</strong> Notes created over time and productivity insights</li>
            <li><strong>Export Statistics:</strong> Track your most exported content and formats</li>
        </ul>

        <h4>🎯 Smart Features & Automation</h4>
        <ul>
            <li><strong>Auto-categorization:</strong> AI-suggested notebook assignments for new notes</li>
            <li><strong>Duplicate Detection:</strong> Identify and merge similar notes automatically</li>
            <li><strong>Smart Suggestions:</strong> Related notes and content recommendations</li>
            <li><strong>Backup Monitoring:</strong> Automatic backup status and sync health checks</li>
            <li><strong>Performance Optimization:</strong> Automatic cleanup and storage optimization</li>
            <li><strong>Accessibility:</strong> Full keyboard navigation and screen reader support</li>
        </ul>

        <div class="tip">
            <strong>Pro Tips:</strong> Use semantic search with natural language queries like "meeting notes about project planning" or "research on AI technologies." Set up custom notebooks for different projects and use bulk operations to organize large collections efficiently. The dashboard remembers your view preferences and filter settings.
        </div>
    </div>

    <h2 id="google-integration">Google Drive & Calendar Integration</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">☁️</div>
            <h3 class="feature-title">Comprehensive Google Integration</h3>
        </div>

        <p>Seamlessly integrate with Google services for cloud storage, document export, and calendar management.</p>

        <h4>☁️ Google Drive Sync</h4>
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> Click the Stashy icon to open the popup
            </div>
            <div class="step">
                <span class="step-number">2.</span> Click "Connect Drive" to authorize Google Drive access
            </div>
            <div class="step">
                <span class="step-number">3.</span> Your notes, highlights, and screenshots automatically sync to Google Drive
            </div>
        </div>

        <h4>📄 Google Docs Export</h4>
        <ul>
            <li><strong>Direct Export:</strong> Export notes and highlights directly to Google Docs</li>
            <li><strong>Format Preservation:</strong> Maintains formatting, images, and structure</li>
            <li><strong>Styled Documents:</strong> Professional formatting with "Stashy Notes" titles</li>
            <li><strong>Batch Export:</strong> Export multiple notes to a single document</li>
            <li><strong>Automatic Organization:</strong> Creates organized folders in your Google Drive</li>
        </ul>

        <h4>📅 Google Calendar Integration</h4>
        <ul>
            <li><strong>Reminder Events:</strong> Automatically create calendar events from note reminders</li>

            <li><strong>Smart Scheduling:</strong> AI-suggested optimal times for follow-ups</li>
        </ul>

        <h4>🔒 Privacy & Security</h4>
        <ul>
            <li><strong>Secure OAuth:</strong> Uses Google's secure authentication system</li>
            <li><strong>Limited Access:</strong> Only accesses specific Stashy folders</li>
            <li><strong>Data Control:</strong> You maintain full control over your data</li>
            <li><strong>Encryption:</strong> All data is encrypted during transmission</li>
        </ul>

        <div class="warning">
            <strong>Privacy Note:</strong> Stashy only accesses the specific folders it creates for your data. You can disconnect at any time, and all local data remains on your device.
        </div>
    </div>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">📅</div>
            <h3 class="feature-title">Google Calendar Reminders</h3>
        </div>

        <p>Create calendar events from your notes:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> In a note, click the calendar icon to set a reminder date/time.
            </div>
            <div class="step">
                <span class="step-number">2.</span> Check the "Add to Calendar" option.
            </div>
            <div class="step">
                <span class="step-number">3.</span> Save the note, and a calendar event will be created in your Google Calendar.
            </div>
        </div>
    </div>

    <h2 id="voice-transcription">Voice Transcription</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">🎤</div>
            <h3 class="feature-title">Advanced Voice-to-Text System</h3>
        </div>

        <p>Transform your voice into text with Stashy's advanced voice transcription system supporting multiple providers and enhanced features:</p>

        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Configure Provider:</strong> Go to Settings → Voice Settings to choose your speech recognition provider
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Start Recording:</strong> In any note, click the microphone icon in the toolbar
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Speak Clearly:</strong> Your words will be transcribed in real-time with interim results
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Stop Recording:</strong> Click the microphone icon again or wait for automatic silence detection
            </div>
        </div>

        <h4>🎙️ Supported Providers</h4>
        <p>Choose from four professional-grade speech recognition providers:</p>

        <div class="provider-list">
            <div class="provider">
                <strong>🌐 Browser Speech API (Default)</strong>
                <p>Uses your browser's built-in speech recognition. No setup required.</p>
                <ul>
                    <li><strong>Pros:</strong> No API key needed, instant setup, privacy-focused</li>
                    <li><strong>Cons:</strong> Quality varies by browser, limited language support</li>
                    <li><strong>Best for:</strong> Quick testing and basic transcription needs</li>
                </ul>
            </div>

            <div class="provider">
                <strong>🔊 Google Cloud Speech-to-Text</strong>
                <p>High-accuracy transcription with extensive language support and advanced features.</p>
                <ul>
                    <li><strong>Setup:</strong> <a href="https://cloud.google.com/speech-to-text" target="_blank">Get API key</a> from Google Cloud Console</li>
                    <li><strong>Features:</strong> 125+ languages, punctuation, speaker diarization</li>
                    <li><strong>Best for:</strong> Professional use, multilingual content, high accuracy needs</li>
                </ul>
            </div>

            <div class="provider">
                <strong>🏢 Microsoft Azure Speech</strong>
                <p>Enterprise-grade speech recognition with advanced noise reduction and customization.</p>
                <ul>
                    <li><strong>Setup:</strong> <a href="https://azure.microsoft.com/en-us/services/cognitive-services/speech-services/" target="_blank">Get API key</a> from Azure Portal</li>
                    <li><strong>Features:</strong> Custom models, background noise reduction, real-time processing</li>
                    <li><strong>Best for:</strong> Enterprise environments, noisy conditions, custom vocabularies</li>
                </ul>
            </div>

            <div class="provider">
                <strong>🚀 AssemblyAI</strong>
                <p>Advanced AI-powered transcription with sentiment analysis and content understanding.</p>
                <ul>
                    <li><strong>Setup:</strong> <a href="https://www.assemblyai.com/" target="_blank">Get API key</a> from AssemblyAI Dashboard</li>
                    <li><strong>Features:</strong> Sentiment analysis, topic detection, content moderation</li>
                    <li><strong>Best for:</strong> Content analysis, meeting transcription, research applications</li>
                </ul>
            </div>
        </div>

        <h4>⚙️ Advanced Configuration Options</h4>
        <ul>
            <li><strong>Language Settings:</strong> Choose from 100+ languages with auto-detection</li>
            <li><strong>Quality Control:</strong> Standard, Enhanced, High, Ultra quality levels</li>
            <li><strong>Processing Options:</strong> Automatic punctuation, capitalization, profanity filtering</li>
            <li><strong>Real-time Features:</strong> Interim results, silence threshold (5-60 seconds)</li>
            <li><strong>Advanced Features:</strong> Background noise reduction, speaker identification</li>
            <li><strong>Security:</strong> Encrypted API key storage, secure transmission</li>
        </ul>

        <h4>🎯 Enhanced Features</h4>
        <ul>
            <li><strong>Real-time Transcription:</strong> See your words appear as you speak</li>
            <li><strong>Smart Punctuation:</strong> Automatic punctuation and capitalization</li>
            <li><strong>Silence Detection:</strong> Configurable auto-stop after silence</li>
            <li><strong>Multi-language Support:</strong> Switch languages on the fly</li>
            <li><strong>Audio Monitoring:</strong> Visual feedback during recording</li>
            <li><strong>Error Recovery:</strong> Automatic retry and fallback systems</li>
        </ul>

        <div class="tip">
            <strong>Pro Tip:</strong> Start with the Browser provider for immediate use, then upgrade to a cloud provider for professional-grade accuracy. Each provider offers free tiers for personal use, and you can switch providers anytime without losing your settings.
        </div>

        <div class="warning">
            <strong>Privacy Note:</strong> Browser provider keeps all processing local. Cloud providers send audio to their servers for processing - review each provider's privacy policy. All API keys are encrypted and stored locally in your browser.
        </div>
    </div>

    <h2 id="screenshots">Screenshots & Media Capture</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">📸</div>
            <h3 class="feature-title">Advanced Screenshot System</h3>
        </div>

        <p>Capture high-quality screenshots with multiple options and intelligent storage management.</p>

        <h4>📷 Capture Options</h4>
        <div class="steps">
            <div class="step">
                <span class="step-number">1.</span> <strong>Visible Area:</strong> Use screenshot options to capture what's currently visible
            </div>
            <div class="step">
                <span class="step-number">2.</span> <strong>Full Page:</strong> Use screenshot options to capture the entire webpage
            </div>
            <div class="step">
                <span class="step-number">3.</span> <strong>Video Screenshots:</strong> Ctrl+click on video timestamps for high-quality captures
            </div>
            <div class="step">
                <span class="step-number">4.</span> <strong>Auto-hide Interface:</strong> Screenshot options automatically hide after capture
            </div>
        </div>

        <h4>🎯 Quality Options</h4>
        <ul>
            <li><strong>Standard Quality:</strong> Good balance of quality and file size</li>
            <li><strong>High Quality:</strong> Better image quality, larger files</li>
            <li><strong>Maximum Quality:</strong> Best quality for professional use</li>
            <li><strong>4K/2K Support:</strong> Ultra-high resolution capture for detailed screenshots</li>
            <li><strong>High-DPI Support:</strong> Optimized for retina and high-resolution displays</li>
            <li><strong>Anti-aliasing:</strong> Smoother lines and text rendering</li>
        </ul>

        <h4>💾 Storage Management</h4>
        <ul>
            <li><strong>Local Storage:</strong> Save directly to your computer</li>
            <li><strong>Google Drive:</strong> Automatic cloud backup and sync</li>
            <li><strong>Dual Storage:</strong> Save to both local and cloud simultaneously</li>
            <li><strong>Smart Compression:</strong> Compressed thumbnails for display, original quality for download</li>
            <li><strong>Auto-deletion:</strong> Configurable automatic cleanup after specified periods</li>
        </ul>

        <h4>🔧 Advanced Features</h4>
        <ul>
            <li><strong>Annotation Tools:</strong> Mark up screenshots with text, arrows, and shapes</li>
            <li><strong>Privacy Controls:</strong> Choose between private and visible Google Drive storage</li>
            <li><strong>Batch Operations:</strong> Manage multiple screenshots efficiently</li>
            <li><strong>Export Integration:</strong> Include screenshots in note exports</li>
            <li><strong>Performance Optimization:</strong> Prevents browser lag with large image collections</li>
        </ul>

        <div class="warning">
            <strong>Storage Note:</strong> High-quality screenshots can be large files. Configure auto-deletion and compression settings in Screenshot Settings to manage storage space effectively.
        </div>
    </div>

    <h2 id="customization">UI Customization</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">🎨</div>
            <h3 class="feature-title">Complete Interface Personalization</h3>
        </div>

        <p>Transform Stashy's interface to perfectly match your workflow with comprehensive customization options. Access through Settings → UI Customization in the popup menu.</p>

        <h4>🔧 Toolbar Dropdowns (5 Items - Reorderable)</h4>
        <p>Customize the main toolbar dropdown menus with full control over visibility and order:</p>
        <ul>
            <li><strong>📝 Format Dropdown:</strong> Text formatting options (bold, italic, headers, etc.)</li>
            <li><strong>🎨 Style Dropdown:</strong> Visual styling and appearance controls</li>
            <li><strong>➕ Insert Dropdown:</strong> Content insertion tools (templates, tables, equations)</li>
            <li><strong>🔧 Tools Dropdown:</strong> Utility functions and advanced features</li>
            <li><strong>👁️ View Dropdown:</strong> Display and layout options</li>
        </ul>
        <p><strong>Customization Options:</strong> Show/hide any dropdown, drag to reorder, or use up/down arrows for precise positioning.</p>

        <h4>👁️ UI Elements (6 Items - Visibility Control)</h4>
        <p>Control the visibility of core interface components:</p>
        <ul>
            <li><strong>📝 Note Title Input:</strong> Show/hide the note title field</li>
            <li><strong>🏷️ Tags Input:</strong> Control tags input field visibility</li>
            <li><strong>📚 Notebook Selector:</strong> Show/hide notebook selection dropdown</li>
            <li><strong>⏰ Reminder Input:</strong> Control reminder date/time input visibility</li>
            <li><strong>📑 Note Switcher:</strong> Show/hide navigation between multiple notes</li>
            <li><strong>🕐 Timestamp Display:</strong> Control timestamp visibility in notes</li>
        </ul>
        <p><strong>Purpose:</strong> Create a clean, minimal interface by hiding unused elements or keep everything visible for full functionality.</p>

        <h4>📅 Snippet Buttons (9 Items - Reorderable)</h4>
        <p>Customize the quick-access snippet buttons with full reordering capabilities:</p>
        <ul>
            <li><strong>📅 Date/Time Button:</strong> Insert current date and time</li>
            <li><strong>📄 Page Info Button:</strong> Add webpage information and metadata</li>
            <li><strong>🔆 Highlight Button:</strong> Quick access to highlighting tools</li>
            <li><strong>⏰ Video Timestamp Button:</strong> Insert clickable video timestamps</li>
            <li><strong>🌐 Web Page AI Button:</strong> AI analysis of current webpage</li>
            <li><strong>📹 Video AI Button:</strong> AI analysis of video content</li>
            <li><strong>📝 Note AI Button:</strong> AI operations on note content</li>
            <li><strong>🔍 AI Search Button:</strong> Semantic search and content generation</li>
            <li><strong>🎓 Academic Solver Button:</strong> STEM problem solving with AI</li>
        </ul>
        <p><strong>Customization Options:</strong> Show/hide any button, drag to reorder, or use arrow controls for precise positioning.</p>

        <h4>⚙️ Advanced Customization Options</h4>
        <ul>
            <li><strong>Default Note Dimensions:</strong> Set preferred width (200-800px) and height (150-800px)</li>
            <li><strong>Default Positioning:</strong> Configure initial note placement (top: 0-500px, right: 0-500px)</li>
            <li><strong>Interface Themes:</strong> Adjust colors, contrast, and visual styling preferences</li>
            <li><strong>Performance Optimization:</strong> Customize for your device capabilities and usage patterns</li>
            <li><strong>Accessibility Settings:</strong> Enhanced options for screen readers and keyboard navigation</li>
            <li><strong>Animation Controls:</strong> Enable/disable interface animations and transitions</li>
        </ul>

        <h4>🎯 Customization Strategies</h4>
        <ul>
            <li><strong>Minimal Setup:</strong> Hide unused elements for a clean, distraction-free interface</li>
            <li><strong>Power User:</strong> Keep all elements visible and reorder for optimal workflow</li>
            <li><strong>Content Creator:</strong> Prioritize AI and template buttons for content generation</li>
            <li><strong>Student/Researcher:</strong> Emphasize academic solver and research tools</li>
            <li><strong>Professional:</strong> Focus on organization, export, and collaboration features</li>
        </ul>

        <h4>💾 Settings Management</h4>
        <ul>
            <li><strong>Instant Apply:</strong> All changes take effect immediately without restart</li>
            <li><strong>Cloud Sync:</strong> Settings sync across devices when using Google Drive</li>
            <li><strong>Reset Options:</strong> Restore defaults for any category or all settings</li>
            <li><strong>Export/Import:</strong> Share customization profiles with others</li>
            <li><strong>Backup:</strong> Automatic backup of customization settings</li>
        </ul>

        <div class="tip">
            <strong>Customization Tips:</strong> Start with the default layout and gradually hide elements you don't use. Use drag-and-drop for quick reordering, or arrow buttons for precise control. Your customizations are automatically saved and will persist across browser sessions. Try different layouts for different types of work - you can always reset to defaults.
        </div>
    </div>

    <h2 id="advanced-features">Advanced Features</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">⚡</div>
            <h3 class="feature-title">Power User Capabilities</h3>
        </div>

        <p>Discover advanced features that make Stashy a powerful productivity and research tool.</p>

        <h4>🔢 Mathematical Content</h4>
        <ul>
            <li><strong>Equation Editor:</strong> Full LaTeX support for mathematical notation</li>
            <li><strong>Live Preview:</strong> See equations rendered as you type</li>
            <li><strong>Formula Library:</strong> Quick access to common mathematical expressions</li>
            <li><strong>Export Compatibility:</strong> Equations render properly in all export formats</li>
        </ul>

        <h4>📊 Diagrams & Visual Content</h4>
        <ul>
            <li><strong>Diagram Support:</strong> Create flowcharts, mind maps, and technical diagrams</li>
            <li><strong>Visual Elements:</strong> Add shapes, arrows, and annotations</li>
            <li><strong>Interactive Components:</strong> Clickable elements and dynamic content</li>
        </ul>

        <h4>🚀 Performance Optimization</h4>
        <ul>
            <li><strong>Virtual Scrolling:</strong> Smooth performance with thousands of notes</li>
            <li><strong>Lazy Loading:</strong> Efficient memory usage for large collections</li>
            <li><strong>Background Processing:</strong> Non-blocking operations for better responsiveness</li>
            <li><strong>Cache Management:</strong> Intelligent caching for faster load times</li>
        </ul>

        <h4>🔒 Security & Privacy</h4>
        <ul>
            <li><strong>Local Storage:</strong> All data stored securely in your browser</li>
            <li><strong>Encryption:</strong> Sensitive data encrypted at rest and in transit</li>
            <li><strong>Permission-Based:</strong> Minimal permissions with user control</li>
            <li><strong>Privacy-First:</strong> No tracking or data collection</li>
        </ul>

        <h4>🔧 Developer Features</h4>
        <ul>
            <li><strong>Content Security Policy:</strong> Strict CSP implementation for security</li>
            <li><strong>Event-Driven Architecture:</strong> Modular, maintainable codebase</li>
            <li><strong>API Integration:</strong> Extensible design for future enhancements</li>
            <li><strong>Cross-Platform:</strong> Works across all major browsers</li>
        </ul>

        <div class="warning">
            <strong>Performance Note:</strong> Advanced features may require additional browser permissions or resources. Configure settings based on your device capabilities and usage needs.
        </div>
    </div>

    <h2 id="troubleshooting">Troubleshooting</h2>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">🔧</div>
            <h3 class="feature-title">Common Issues & Solutions</h3>
        </div>

        <h4>🚫 Extension Activation Issues</h4>
        <p>If Stashy won't activate on certain websites:</p>
        <ul>
            <li><strong>Security Policies:</strong> Some sites block extensions - try refreshing and clicking the extension icon again</li>
            <li><strong>Permission Issues:</strong> Ensure Stashy has permission to access the website</li>
            <li><strong>No Toggle Button:</strong> Check if the website allows extensions to run</li>
            <li><strong>ChatGPT Sites:</strong> Stashy has special handling for React-based sites - wait a moment after page load</li>
        </ul>

        <h4>☁️ Google Drive Sync Problems</h4>
        <p>If you're experiencing sync issues:</p>
        <ul>
            <li><strong>Connection Issues:</strong> Disconnect and reconnect your Google account in settings</li>
            <li><strong>Permission Problems:</strong> Ensure you've granted all necessary Google Drive permissions</li>
            <li><strong>Network Issues:</strong> Check your internet connection and try again</li>
            <li><strong>Storage Limits:</strong> Verify you have sufficient Google Drive storage space</li>
        </ul>

        <h4>🎤 Voice Issues</h4>
        <p>If voice features aren't working:</p>
        <ul>
            <li><strong>Microphone Access:</strong> Grant microphone permissions to your browser</li>
            <li><strong>Provider Issues:</strong> Try switching voice recognition providers in Voice Settings</li>
            <li><strong>Audio Quality:</strong> Check microphone settings and reduce background noise</li>
        </ul>

        <h4>🤖 AI Features Not Working</h4>
        <p>If AI features are unavailable:</p>
        <ul>
            <li><strong>Authentication:</strong> Ensure you're signed in to your Google account</li>
            <li><strong>API Limits:</strong> Check if you've exceeded daily usage limits</li>
            <li><strong>Content Length:</strong> Very long content may need to be shortened</li>
            <li><strong>Network Issues:</strong> Verify internet connection for AI processing</li>
        </ul>

        <h4>📊 Performance Issues</h4>
        <p>If Stashy is running slowly:</p>
        <ul>
            <li><strong>Large Collections:</strong> Use pagination and filtering in the dashboard</li>
            <li><strong>Memory Usage:</strong> Close unused tabs and restart browser if needed</li>
            <li><strong>Storage Cleanup:</strong> Use bulk delete to remove old, unused notes</li>
            <li><strong>Cache Issues:</strong> Clear browser cache and reload the extension</li>
        </ul>

        <h4>📱 Mobile & Responsive Issues</h4>
        <p>If using on mobile devices:</p>
        <ul>
            <li><strong>Touch Interface:</strong> Some features work better with mouse/trackpad</li>
            <li><strong>Screen Size:</strong> Use landscape mode for better dashboard experience</li>
            <li><strong>Browser Support:</strong> Use Chrome or Edge for best compatibility</li>
        </ul>
    </div>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">📞</div>
            <h3 class="feature-title">Getting Help & Support</h3>
        </div>

        <p>Stashy provides comprehensive support resources and documentation:</p>

        <h4>📚 Documentation</h4>
        <ul>
            <li><strong>Privacy Policy:</strong> Detailed information about data handling and privacy protection</li>
            <li><strong>Permissions Guide:</strong> Explanation of required browser permissions and their purposes</li>
            <li><strong>Data Deletion:</strong> Information about data removal and account management</li>
            <li><strong>Activation Guide:</strong> Step-by-step instructions for getting started</li>
        </ul>

        <h4>⚙️ Settings & Configuration</h4>
        <ul>
            <li><strong>Voice Settings:</strong> Configure speech recognition providers and languages</li>
            <li><strong>AI Features:</strong> Manage AI authentication and preferences</li>
            <li><strong>Screenshot Settings:</strong> Customize capture quality and storage options</li>
            <li><strong>UI Customization:</strong> Personalize interface elements and layout</li>
        </ul>

        <h4>🔍 Quick Diagnostics</h4>
        <ul>
            <li><strong>Test Connections:</strong> Verify Google Drive and AI service connectivity</li>
            <li><strong>Permission Check:</strong> Ensure all required permissions are granted</li>
            <li><strong>Performance Monitor:</strong> Check storage usage and optimization status</li>
            <li><strong>Feature Status:</strong> Verify which features are available and working</li>
        </ul>

        <h4>💡 Tips for Best Experience</h4>
        <ul>
            <li><strong>Regular Updates:</strong> Keep Stashy updated for latest features and fixes</li>
            <li><strong>Backup Strategy:</strong> Use Google Drive sync for automatic backups</li>
            <li><strong>Organization:</strong> Use notebooks and tags for better note management</li>

        </ul>

        <div class="tip">
            <strong>Pro Tip:</strong> Most issues can be resolved by refreshing the page, checking permissions, or reconnecting Google services. The extension is designed to be self-healing and will often resolve temporary issues automatically.
        </div>
    </div>

    <div class="feature-card">
        <div class="feature-header">
            <div class="feature-icon">🎯</div>
            <h3 class="feature-title">Stashy Complete Overview</h3>
        </div>

        <p><strong>Stashy</strong> is the most comprehensive browser extension for web content interaction, featuring universal AI integration, advanced note-taking, and powerful organization tools. Transform any webpage into your personal knowledge base with professional-grade features.</p>

        <h4>🌟 Core Capabilities</h4>
        <ul>
            <li><strong>Universal AI System:</strong> Support for 5 major AI providers with automatic detection</li>
            <li><strong>Advanced Note-Taking:</strong> Rich formatting, templates, and smart content creation</li>
            <li><strong>Intelligent Highlighting:</strong> 5 colors × 5 styles with persistent storage</li>
            <li><strong>Video Intelligence:</strong> Complete video analysis with transcript processing</li>
            <li><strong>Voice Transcription:</strong> 4 provider options with enhanced features</li>
            <li><strong>Study System:</strong> Flashcards, academic solver, and learning tools</li>
            <li><strong>Visual Organization:</strong> Comprehensive dashboard with notebook management</li>
            <li><strong>Cloud Integration:</strong> Full Google ecosystem integration</li>
            <li><strong>Complete Customization:</strong> 20+ customizable interface elements</li>
        </ul>

        <h4>📊 Feature Statistics</h4>
        <ul>
            <li><strong>AI Features:</strong> 30+ AI-powered functions across web, note, and search categories</li>
            <li><strong>Content Tools:</strong> Templates, tables, equations, diagrams, and media capture</li>
            <li><strong>Export Options:</strong> 4 formats (TXT, MD, HTML, PDF) with bulk operations</li>
            <li><strong>Integration Points:</strong> Google Drive, Docs, Calendar, and OAuth2 security</li>
            <li><strong>Customization:</strong> 20 UI elements, 9 snippet buttons, 5 toolbar dropdowns</li>
            <li><strong>Platform Support:</strong> All major browsers with cross-platform compatibility</li>
        </ul>

        <h4>🔒 Privacy & Security</h4>
        <ul>
            <li><strong>Privacy-First Design:</strong> ActiveTab permission - only activates when you choose</li>
            <li><strong>Local Storage:</strong> All data stored securely in your browser</li>
            <li><strong>Encrypted Security:</strong> API keys and sensitive data encrypted at rest</li>
            <li><strong>User Control:</strong> You own your data with optional cloud sync</li>
            <li><strong>No Tracking:</strong> Zero data collection or user tracking</li>
        </ul>

        <h4>🚀 Performance & Reliability</h4>
        <ul>
            <li><strong>Optimized Architecture:</strong> Modular design with lazy loading and caching</li>
            <li><strong>Background Processing:</strong> Non-blocking operations for smooth performance</li>
            <li><strong>Error Recovery:</strong> Robust error handling with automatic fallbacks</li>
            <li><strong>Accessibility:</strong> Full keyboard navigation and screen reader support</li>
            <li><strong>Cross-Platform:</strong> Consistent experience across all major browsers</li>
        </ul>

        <p><strong>Version:</strong> 1.0 | <strong>Architecture:</strong> Manifest V3 | <strong>Last Updated:</strong> January 2025</p>
        <p><strong>Total Features:</strong> 100+ functions | <strong>Content Scripts:</strong> 40+ modules | <strong>Supported Platforms:</strong> Chrome, Edge, Firefox</p>
    </div>

    <p class="last-updated">This comprehensive help guide covers Stashy v1.0 with complete feature documentation, setup instructions, and advanced usage guidance. All features are actively maintained and regularly updated.</p>
</body>
</html>
