// --- START OF FILE content-quick-snippet.js ---

/**
 * Quick Snippet Capture Feature
 * Allows users to quickly add selected text to the current note
 * with a single click, without opening any additional UI.
 */

// Constants
const QUICK_SNIPPET_BTN_ID = 'Stashy-quick-snippet-btn';

// Use existing constants if defined, otherwise use fallback values
// This avoids redeclaring constants that are already defined in content-state.js
const _NOTE_ID = typeof NOTE_ID !== 'undefined' ? NOTE_ID : 'Stashy-container';
const _TEXTAREA_ID = typeof TEXTAREA_ID !== 'undefined' ? TEXTAREA_ID : 'Stashy-text';
const _TEXT_EDITOR_ID = _TEXTAREA_ID; // Alias for clarity
const _FLASHCARD_MODAL_ID = typeof FLASHCARD_MODAL_ID !== 'undefined' ? FLASHCARD_MODAL_ID : 'Stashy-flashcard-overlay';

// State
let quickSnippetButton = null;
let isSelectionInProgress = false;

/**
 * Initialize the quick snippet feature
 */
function initQuickSnippetCapture() {
    // Add selection event listeners
    document.addEventListener('selectionchange', handleSelectionChange);
    document.addEventListener('mouseup', handleMouseUp);
}

/**
 * Handle selection change event
 */
function handleSelectionChange() {
    // Set flag to indicate selection is in progress
    isSelectionInProgress = true;
}

/**
 * Handle mouse up event to show the quick snippet button
 */
function handleMouseUp(e) {
    // Short delay to ensure selection is complete
    setTimeout(() => {
        if (!isSelectionInProgress) return;
        isSelectionInProgress = false;

        const selection = window.getSelection();
        if (!selection || selection.isCollapsed || !selection.rangeCount) return;

        // Prevent capturing from Stashy's own UI elements
        const container = selection.anchorNode?.parentElement;
        if (container?.closest(`#${_NOTE_ID}, .Stashy-diagram-editor, #${_FLASHCARD_MODAL_ID}, #${QUICK_SNIPPET_BTN_ID}`)) {
            return;
        }

        const selectedText = selection.toString().trim();
        if (!selectedText) return;

        // Show the quick snippet button near the selection
        showQuickSnippetButton(selection, e);
    }, 10);
}

/**
 * Show the quick snippet button near the selection
 */
function showQuickSnippetButton(selection, mouseEvent) {
    // Remove any existing button
    removeQuickSnippetButton();

    // Create the button
    quickSnippetButton = document.createElement('button');
    quickSnippetButton.id = QUICK_SNIPPET_BTN_ID;
    quickSnippetButton.className = 'Stashy-quick-snippet-btn';
    quickSnippetButton.title = 'Copy to Note';
    quickSnippetButton.innerHTML = '✂️'; // Scissors emoji
    quickSnippetButton.addEventListener('click', handleQuickSnippetCapture);

    // Position the button near the selection
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();

    // Add to document
    document.body.appendChild(quickSnippetButton);

    // Position the button near the end of the selection
    const buttonRect = quickSnippetButton.getBoundingClientRect();

    // Position near the mouse cursor or selection end
    let left = mouseEvent ? mouseEvent.clientX : rect.right;
    let top = mouseEvent ? mouseEvent.clientY : rect.bottom;

    // Adjust position to ensure button is visible
    left = Math.min(left, window.innerWidth - buttonRect.width - 10);
    top = Math.min(top, window.innerHeight - buttonRect.height - 10);

    quickSnippetButton.style.left = `${left + window.scrollX}px`;
    quickSnippetButton.style.top = `${top + window.scrollY}px`;

    // Add click outside to close
    document.addEventListener('click', handleClickOutside);

    // Auto-remove after 3 seconds if not clicked
    setTimeout(removeQuickSnippetButton, 3000);
}

/**
 * Handle click outside the quick snippet button
 */
function handleClickOutside(e) {
    if (quickSnippetButton && !quickSnippetButton.contains(e.target)) {
        removeQuickSnippetButton();
    }
}

/**
 * Remove the quick snippet button
 */
function removeQuickSnippetButton() {
    if (quickSnippetButton) {
        quickSnippetButton.remove();
        quickSnippetButton = null;
        document.removeEventListener('click', handleClickOutside);
    }
}

/**
 * Handle quick snippet capture - adds selected text to the current note
 */
function handleQuickSnippetCapture() {
    try {
        // Get the selected text
        const selection = window.getSelection();
        if (!selection || selection.isCollapsed) return;

        const selectedText = selection.toString().trim();
        if (!selectedText) return;

        // Check if the note editor is open
        const textEditor = document.getElementById(_TEXT_EDITOR_ID);
        if (!textEditor) {
            showSnippetFeedback('No note is open. Please open a note first.', true);
            removeQuickSnippetButton();
            return;
        }

        // Format the selected text
        const formattedText = formatSelectedText(selectedText);

        // Add the text to the editor
        addTextToEditor(textEditor, formattedText);

        // Show success feedback
        showSnippetFeedback('Text added to note!');

        // Clear the selection
        selection.collapseToEnd();

        // Remove the button
        removeQuickSnippetButton();
    } catch (error) {
        console.error('Stashy: Error adding text to note:', error);
        showSnippetFeedback('Error adding text', true);
    }
}

/**
 * Format the selected text for insertion into the note
 * Simply returns the text as is - no formatting
 */
function formatSelectedText(text) {
    // Return the text exactly as it is
    return text;
}

/**
 * Add text to the editor at the current cursor position
 */
function addTextToEditor(editor, text) {
    // If the editor uses contentEditable
    if (editor.contentEditable === 'true') {
        // Insert at cursor position if possible
        const selection = window.getSelection();
        if (selection.rangeCount > 0 && selection.getRangeAt(0).commonAncestorContainer.closest(`#${_TEXT_EDITOR_ID}`)) {
            // Insert at cursor position
            const range = selection.getRangeAt(0);
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = text;

            // Insert each node from the HTML
            const fragment = document.createDocumentFragment();
            while (tempDiv.firstChild) {
                fragment.appendChild(tempDiv.firstChild);
            }

            range.deleteContents();
            range.insertNode(fragment);

            // Move cursor to end of inserted content
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
        } else {
            // Append to the end if cursor is not in editor
            editor.innerHTML += text;
        }

        // Trigger input event to save changes
        editor.dispatchEvent(new Event('input', { bubbles: true }));
    } else {
        // For textarea elements
        const startPos = editor.selectionStart;
        const endPos = editor.selectionEnd;

        // Convert HTML to plain text for textarea
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = text;
        const plainText = tempDiv.textContent || tempDiv.innerText || '';

        // Insert at cursor position
        editor.value = editor.value.substring(0, startPos) + plainText + editor.value.substring(endPos);

        // Move cursor after inserted text
        editor.selectionStart = editor.selectionEnd = startPos + plainText.length;

        // Trigger input event to save changes
        editor.dispatchEvent(new Event('input', { bubbles: true }));
    }

    // Trigger save
    if (typeof scheduleSave === 'function') {
        scheduleSave();
    }
}

/**
 * Show feedback when a snippet is added
 */
function showSnippetFeedback(message, isError = false) {
    // Create feedback element
    const feedback = document.createElement('div');
    feedback.className = `Stashy-snippet-feedback ${isError ? 'error' : 'success'}`;
    feedback.textContent = message;

    // Add to document
    document.body.appendChild(feedback);

    // Position in the bottom right corner
    feedback.style.position = 'fixed';
    feedback.style.bottom = '20px';
    feedback.style.right = '20px';
    feedback.style.zIndex = '2147483647';
    feedback.style.backgroundColor = isError ? '#f44336' : '#4CAF50';
    feedback.style.color = 'white';
    feedback.style.padding = '10px 20px';
    feedback.style.borderRadius = '4px';
    feedback.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    feedback.style.transition = 'opacity 0.3s ease-in-out';

    // Remove after 2 seconds
    setTimeout(() => {
        feedback.style.opacity = '0';
        setTimeout(() => feedback.remove(), 300);
    }, 2000);
}

// Initialize when the script loads
initQuickSnippetCapture();

console.log("Stashy: Quick Snippet Capture Loaded");
// --- END OF FILE content-quick-snippet.js ---
