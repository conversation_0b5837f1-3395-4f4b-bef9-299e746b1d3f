/**
 * Stashy Security Audit Module
 * Monitors and reports security compliance status
 * Helps identify potential security vulnerabilities
 */

(function() {
    'use strict';

    const SecurityAudit = {
        // Security check results
        auditResults: {
            dynamicScriptLoading: false,
            externalCDNUsage: false,
            unsafeEval: false,
            blobURLs: false,
            unsafeWorkers: false,
            cspCompliance: false
        },

        /**
         * Performs a comprehensive security audit
         * @returns {Object} Audit results
         */
        performAudit: function() {
            console.log('Stashy Security: Starting security audit...');

            // Reset results to default secure state
            this.auditResults = {
                dynamicScriptLoading: true,  // Default to secure
                externalCDNUsage: true,      // Default to secure
                unsafeEval: true,            // Default to secure
                blobURLs: true,              // Default to secure
                unsafeWorkers: true,         // Default to secure
                cspCompliance: true          // Default to secure
            };

            // Run all checks
            this.checkDynamicScriptLoading();
            this.checkExternalCDNUsage();
            this.checkUnsafeEval();
            this.checkBlobURLs();
            this.checkUnsafeWorkers();
            this.checkCSPCompliance();

            // Wait a bit for async checks to complete
            setTimeout(() => {
                this.reportResults();
            }, 1500);

            return this.auditResults;
        },

        /**
         * Checks for dynamic script loading by the extension (not by the host page)
         */
        checkDynamicScriptLoading: function() {
            const scripts = document.querySelectorAll('script[src]');
            let hasExtensionDynamicLoading = false;

            scripts.forEach(script => {
                const src = script.getAttribute('src');
                if (src && (src.startsWith('http') || src.startsWith('//'))) {
                    // Only flag scripts that could be loaded by our extension
                    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getURL) {
                        // Check if it's from our extension but loading external content
                        if (src.startsWith(chrome.runtime.getURL('')) &&
                            (src.includes('cdn.') || src.includes('unpkg.') || src.includes('jsdelivr.'))) {
                            hasExtensionDynamicLoading = true;
                            console.warn('Stashy Security: Extension loading external script:', src);
                        }
                        // Note: Host page scripts (like YouTube's) are expected and not a security issue
                    }
                }
            });

            this.auditResults.dynamicScriptLoading = !hasExtensionDynamicLoading;
            if (!hasExtensionDynamicLoading) {
                console.log('Stashy Security: Dynamic script loading check: ✅ PASS - Extension not loading external scripts');
            } else {
                console.warn('Stashy Security: Dynamic script loading check: ❌ FAIL - Extension loading external scripts');
            }
        },

        /**
         * Checks for external CDN usage by the Stashy extension (not host website)
         */
        checkExternalCDNUsage: function() {
            const cdnPatterns = [
                /cdn\.jsdelivr\.net/i,
                /unpkg\.com/i,
                /cdnjs\.cloudflare\.com/i,
                /ajax\.googleapis\.com/i,
                /code\.jquery\.com/i
            ];

            let hasExtensionExternalCDN = false;
            const allElements = document.querySelectorAll('*[src], *[href]');

            allElements.forEach(element => {
                const url = element.getAttribute('src') || element.getAttribute('href');
                if (url) {
                    // Only check elements that could be from our extension
                    const isExtensionElement = this.isElementFromExtension(element, url);

                    if (isExtensionElement) {
                        cdnPatterns.forEach(pattern => {
                            if (pattern.test(url)) {
                                hasExtensionExternalCDN = true;
                                console.warn('Stashy Security: Extension using external CDN:', url);
                            }
                        });
                    }
                }
            });

            this.auditResults.externalCDNUsage = !hasExtensionExternalCDN;
            if (!hasExtensionExternalCDN) {
                console.log('Stashy Security: External CDN usage check: ✅ PASS - Extension uses only local libraries');
            } else {
                console.warn('Stashy Security: External CDN usage check: ❌ FAIL - Extension using external CDNs');
            }
        },

        /**
         * Determines if an element is likely from the Stashy extension
         * @param {Element} element - DOM element to check
         * @param {string} url - URL to check
         * @returns {boolean} True if element is from extension
         */
        isElementFromExtension: function(element, url) {
            // Check if element has Stashy-specific classes or IDs
            const hasExtensionClass = element.className &&
                (element.className.includes('Stashy') ||
                 element.className.includes('Stashy'));

            const hasExtensionId = element.id &&
                (element.id.includes('Stashy') ||
                 element.id.includes('Stashy'));

            // Check if URL is from extension
            const isExtensionURL = typeof chrome !== 'undefined' &&
                chrome.runtime &&
                chrome.runtime.getURL &&
                url.startsWith(chrome.runtime.getURL(''));

            // Check if element is in a Stashy container
            const isInExtensionContainer = element.closest('.Stashy-note-container, .Stashy-toolbar, .Stashy-popup, [id*="Stashy"], [class*="Stashy"]');

            return hasExtensionClass || hasExtensionId || isExtensionURL || isInExtensionContainer;
        },

        /**
         * Checks for unsafe eval usage
         */
        checkUnsafeEval: function() {
            // Override eval to detect usage
            const originalEval = window.eval;
            let evalUsed = false;

            window.eval = function() {
                evalUsed = true;
                console.warn('Stashy Security: eval() usage detected');
                return originalEval.apply(this, arguments);
            };

            // Check for Function constructor
            const originalFunction = window.Function;
            let functionConstructorUsed = false;

            window.Function = function() {
                functionConstructorUsed = true;
                console.warn('Stashy Security: Function constructor usage detected');
                return originalFunction.apply(this, arguments);
            };

            // Set result after a delay to catch any immediate usage
            setTimeout(() => {
                this.auditResults.unsafeEval = !evalUsed && !functionConstructorUsed;
            }, 1000);
        },

        /**
         * Checks for blob URL creation
         */
        checkBlobURLs: function() {
            const originalCreateObjectURL = URL.createObjectURL;
            let javascriptBlobURLCreated = false;

            URL.createObjectURL = function(blob) {
                if (blob instanceof Blob &&
                    (blob.type.includes('javascript') ||
                     blob.type.includes('application/javascript') ||
                     blob.type.includes('text/javascript'))) {
                    javascriptBlobURLCreated = true;
                    console.warn('Stashy Security: JavaScript blob URL creation detected');
                }
                return originalCreateObjectURL.apply(this, arguments);
            };

            setTimeout(() => {
                this.auditResults.blobURLs = !javascriptBlobURLCreated;
            }, 1000);
        },

        /**
         * Checks for unsafe worker usage
         */
        checkUnsafeWorkers: function() {
            const originalWorker = window.Worker;
            let unsafeWorkerCreated = false;

            window.Worker = function(scriptURL) {
                if (typeof scriptURL === 'string') {
                    if (scriptURL.startsWith('blob:') || scriptURL.startsWith('data:')) {
                        unsafeWorkerCreated = true;
                        console.warn('Stashy Security: Unsafe worker creation detected:', scriptURL);
                    } else if (scriptURL.startsWith('http')) {
                        // Check if it's from our extension
                        if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.getURL) {
                            if (!scriptURL.startsWith(chrome.runtime.getURL(''))) {
                                unsafeWorkerCreated = true;
                                console.warn('Stashy Security: Unsafe worker creation detected:', scriptURL);
                            }
                        } else {
                            // If chrome.runtime is not available, consider any external worker as unsafe
                            unsafeWorkerCreated = true;
                            console.warn('Stashy Security: Unsafe worker creation detected:', scriptURL);
                        }
                    }
                }
                return originalWorker.apply(this, arguments);
            };

            setTimeout(() => {
                this.auditResults.unsafeWorkers = !unsafeWorkerCreated;
            }, 1000);
        },

        /**
         * Checks CSP compliance
         */
        checkCSPCompliance: function() {
            // Check if CSP is properly configured
            const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
            const hasCSPModule = (typeof window.StashyCSP !== 'undefined');
            const isExtensionContext = (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id);

            // For extension contexts, CSP is configured in manifest.json
            const hasCSP = metaCSP || hasCSPModule || isExtensionContext;

            this.auditResults.cspCompliance = hasCSP;

            if (!hasCSP) {
                console.warn('Stashy Security: CSP not properly configured');
            } else {
                console.log('Stashy Security: CSP properly configured');
            }
        },

        /**
         * Verifies that remote code execution vulnerabilities have been fixed
         */
        verifyRemoteCodeExecutionFixes: function() {
            console.log('Stashy Security: Verifying remote code execution fixes...');

            // Check that pako library is loaded locally
            const pakoAvailable = typeof window.pako !== 'undefined';
            console.log(`Pako library (local): ${pakoAvailable ? '✅ LOADED' : '❌ NOT LOADED'}`);

            // Check that LZ-String library is loaded locally
            const lzStringAvailable = typeof window.LZString !== 'undefined';
            console.log(`LZ-String library (local): ${lzStringAvailable ? '✅ LOADED' : '❌ NOT LOADED'}`);

            // Verify no dynamic script loading functions exist
            const noDynamicLoading = typeof window.loadScript === 'undefined' &&
                                   typeof window.importScript === 'undefined';
            console.log(`Dynamic script loading disabled: ${noDynamicLoading ? '✅ YES' : '❌ NO'}`);

            const remoteCodeExecutionSecure = pakoAvailable && lzStringAvailable && noDynamicLoading;
            console.log(`🔒 Remote Code Execution Security: ${remoteCodeExecutionSecure ? 'SECURE' : 'VULNERABLE'}`);

            return remoteCodeExecutionSecure;
        },

        /**
         * Reports audit results
         */
        reportResults: function() {
            console.log('Stashy Security Audit Results:');
            console.log('================================');

            Object.keys(this.auditResults).forEach(check => {
                const status = this.auditResults[check] ? '✅ PASS' : '❌ FAIL';
                console.log(`${check}: ${status}`);
            });

            // Run remote code execution verification
            const remoteCodeSecure = this.verifyRemoteCodeExecutionFixes();

            const overallScore = Object.values(this.auditResults).filter(Boolean).length;
            const totalChecks = Object.keys(this.auditResults).length;

            console.log(`Overall Security Score: ${overallScore}/${totalChecks}`);

            if (overallScore === totalChecks && remoteCodeSecure) {
                console.log('🔒 All security checks passed! Remote code execution vulnerabilities have been eliminated.');
            } else {
                console.warn('⚠️ Some security issues detected. Please review the warnings above.');
            }
        },

        /**
         * Gets a security summary
         * @returns {Object} Security summary
         */
        getSecuritySummary: function() {
            const passed = Object.values(this.auditResults).filter(Boolean).length;
            const total = Object.keys(this.auditResults).length;

            return {
                score: passed,
                total: total,
                percentage: Math.round((passed / total) * 100),
                isSecure: passed === total,
                results: { ...this.auditResults }
            };
        }
    };

    // Make it available globally
    window.StashySecurityAudit = SecurityAudit;

    // Perform initial audit after page load with proper initialization
    function initializeSecurityAudit() {
        // Wait for all security modules to load
        setTimeout(() => {
            console.log('Stashy Security: Initializing security monitoring...');
            SecurityAudit.performAudit();
        }, 3000); // Increased delay to ensure all modules are loaded
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeSecurityAudit);
    } else {
        initializeSecurityAudit();
    }

    console.log('Stashy: Security audit module loaded');
})();
