/**
 * Google Docs Integration Module for Stashy
 *
 * This module provides functionality to export notes and highlights to Google Docs.
 */

// Create a namespace to avoid global pollution
window.StashyGoogleDocsIntegration = (function() {
    // Constants
    const DOCS_API_ENDPOINT = 'https://docs.googleapis.com/v1/documents';
    const DRIVE_API_ENDPOINT = 'https://www.googleapis.com/drive/v3';

    // Private variables
    let isInitialized = false;
    let authToken = null;

    // Rate limiting variables
    let lastApiCall = 0;
    const MIN_API_DELAY = 250; // Minimum delay between API calls (ms) - increased for more conservative rate limiting
    const RATE_LIMIT_DELAY = 2000; // Base delay for rate limit errors (ms)

    /**
     * Enforces rate limiting between API calls
     * @returns {Promise<void>} A promise that resolves after the required delay
     */
    async function enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastCall = now - lastApiCall;

        if (timeSinceLastCall < MIN_API_DELAY) {
            const delay = MIN_API_DELAY - timeSinceLastCall;
            console.log(`Rate limiting: waiting ${delay}ms before next API call`);
            await new Promise(resolve => setTimeout(resolve, delay));
        }

        lastApiCall = Date.now();
    }

    /**
     * Makes a rate-limited API call with retry logic for rate limit errors
     * @param {string} url - The API endpoint URL
     * @param {Object} options - Fetch options
     * @param {number} retryCount - Current retry count
     * @returns {Promise<Response>} The API response
     */
    async function makeRateLimitedApiCall(url, options, retryCount = 0) {
        const maxRetries = 3;

        // Enforce rate limiting
        await enforceRateLimit();

        try {
            const response = await fetch(url, options);

            // Handle rate limit errors (403 and 429)
            if ((response.status === 403 || response.status === 429) && retryCount < maxRetries) {
                const errorText = await response.text();
                console.warn(`Rate limit hit (${response.status}). Retrying in ${RATE_LIMIT_DELAY * (retryCount + 1)}ms... (attempt ${retryCount + 1}/${maxRetries})`);

                // Exponential backoff for rate limit errors
                const delay = RATE_LIMIT_DELAY * Math.pow(2, retryCount);
                await new Promise(resolve => setTimeout(resolve, delay));

                return await makeRateLimitedApiCall(url, options, retryCount + 1);
            }

            return response;
        } catch (error) {
            // Retry on network errors
            if (retryCount < maxRetries && (error.message.includes('timeout') || error.message.includes('network'))) {
                console.warn(`Network error: ${error.message}. Retrying in ${RATE_LIMIT_DELAY}ms... (attempt ${retryCount + 1}/${maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY));
                return await makeRateLimitedApiCall(url, options, retryCount + 1);
            }

            throw error;
        }
    }

    /**
     * Initializes the Google Docs integration
     * @returns {Promise<boolean>} A promise that resolves to true if initialization was successful
     */
    async function init() {
        try {
            // Check if already initialized
            if (isInitialized) {
                return true;
            }

            // Get auth token from background script
            const response = await chrome.runtime.sendMessage({
                action: 'getAuthToken',
                scopes: ['https://www.googleapis.com/auth/documents', 'https://www.googleapis.com/auth/drive.file']
            });

            if (!response || !response.success || !response.token) {
                console.error('Failed to get auth token for Google Docs integration');
                return false;
            }

            authToken = response.token;
            isInitialized = true;
            return true;
        } catch (error) {
            console.error('Error initializing Google Docs integration:', error);
            return false;
        }
    }

    /**
     * Creates a new Google Doc
     * @param {string} title - The title of the document
     * @returns {Promise<Object>} A promise that resolves to the created document
     */
    async function createDoc(title) {
        try {
            if (!isInitialized) {
                await init();
            }

            console.log('Creating Google Doc with rate limiting...');
            const response = await makeRateLimitedApiCall(`${DRIVE_API_ENDPOINT}/files`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    name: title,
                    mimeType: 'application/vnd.google-apps.document'
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`Failed to create Google Doc: ${response.status} ${errorText}`);
            }

            const result = await response.json();
            console.log('Google Doc created successfully:', result.id);
            return result;
        } catch (error) {
            console.error('Error creating Google Doc:', error);
            throw error;
        }
    }

    /**
     * Appends content to a Google Doc with batch splitting for large requests
     * @param {string} docId - The ID of the document
     * @param {Array} requests - The requests to append content
     * @returns {Promise<Object>} A promise that resolves to the updated document
     */
    async function batchUpdate(docId, requests) {
        try {
            if (!isInitialized) {
                await init();
            }

            const MAX_BATCH_SIZE = 100; // Google Docs API limit

            if (requests.length <= MAX_BATCH_SIZE) {
                // Single batch update
                console.log(`Updating Google Doc with ${requests.length} requests using rate limiting...`);
                const response = await makeRateLimitedApiCall(`${DOCS_API_ENDPOINT}/${docId}:batchUpdate`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        requests: requests
                    })
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Failed to update Google Doc: ${response.status} ${errorText}`);
                }

                const result = await response.json();
                console.log('Google Doc updated successfully');
                return result;
            } else {
                // Split into multiple batches
                console.log(`Splitting ${requests.length} requests into batches of ${MAX_BATCH_SIZE} to avoid API limits...`);
                let lastResult = null;

                for (let i = 0; i < requests.length; i += MAX_BATCH_SIZE) {
                    const batch = requests.slice(i, i + MAX_BATCH_SIZE);
                    const batchNumber = Math.floor(i / MAX_BATCH_SIZE) + 1;
                    const totalBatches = Math.ceil(requests.length / MAX_BATCH_SIZE);

                    console.log(`Processing batch ${batchNumber}/${totalBatches} (${batch.length} requests)...`);

                    const response = await makeRateLimitedApiCall(`${DOCS_API_ENDPOINT}/${docId}:batchUpdate`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${authToken}`,
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            requests: batch
                        })
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`Failed to update Google Doc (batch ${batchNumber}): ${response.status} ${errorText}`);
                    }

                    lastResult = await response.json();

                    // Add extra delay between batches to be more conservative
                    if (i + MAX_BATCH_SIZE < requests.length) {
                        console.log('Adding extra delay between batches...');
                        await new Promise(resolve => setTimeout(resolve, 1000)); // Increased to 1 second
                    }
                }

                console.log('All batches processed successfully');
                return lastResult;
            }
        } catch (error) {
            console.error('Error updating Google Doc:', error);
            throw error;
        }
    }





    /**
     * Parses HTML content and extracts text and images for Google Docs export
     * @param {string} htmlContent - The HTML content to parse
     * @param {number} startIndex - The starting index in the document
     * @returns {Promise<Object>} Object containing requests array and new index
     */
    async function parseContentWithImages(htmlContent, startIndex) {
        const requests = [];
        let currentIndex = startIndex;

        try {
            console.log('Parsing HTML content for Google Docs:', htmlContent.substring(0, 200) + '...');

            // Create a temporary DOM element to parse HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;

            // Log what we found
            const images = tempDiv.querySelectorAll('img');
            const equations = tempDiv.querySelectorAll('.Stashy-equation');
            console.log(`Found ${images.length} images and ${equations.length} equations in content`);

            // Pre-process and optimize images sequentially to avoid rate limiting
            if (images.length > 0) {
                console.log(`Pre-optimizing ${images.length} images sequentially to avoid rate limiting...`);

                for (let i = 0; i < images.length; i++) {
                    const img = images[i];
                    if (img.src && img.src.startsWith('data:image/')) {
                        try {
                            const optimizedSrc = await optimizeImageForUpload(img.src);
                            if (optimizedSrc) {
                                img.src = optimizedSrc;
                            }

                            // Add delay between image processing to avoid overwhelming the API
                            if (i < images.length - 1) {
                                await new Promise(resolve => setTimeout(resolve, 200)); // Increased to 200ms
                            }
                        } catch (error) {
                            console.warn(`Failed to optimize image ${i + 1}:`, error);
                        }
                    }
                }

                console.log('Image optimization complete');
            }

            // Process the content by walking through the DOM tree
            const result = await processNodeRecursively(tempDiv, currentIndex);
            requests.push(...result.requests);
            currentIndex = result.newIndex;

        } catch (error) {
            console.error('Error parsing content with images:', error);
            // Fallback: extract text and try to find images/equations manually
            const plainText = extractTextWithPlaceholders(htmlContent);
            requests.push({
                insertText: {
                    location: { index: currentIndex },
                    text: plainText
                }
            });
            currentIndex += plainText.length;
        }

        return {
            requests: requests,
            newIndex: currentIndex
        };
    }

    /**
     * Processes a DOM node recursively to extract content for Google Docs
     * @param {Node} node - The DOM node to process
     * @param {number} currentIndex - The current index in the document
     * @returns {Promise<Object>} Object containing requests array and new index
     */
    async function processNodeRecursively(node, currentIndex) {
        const requests = [];
        let index = currentIndex;

        if (node.nodeType === Node.TEXT_NODE) {
            // Handle text nodes
            const textContent = node.textContent;
            if (textContent && textContent.trim()) {
                requests.push({
                    insertText: {
                        location: { index: index },
                        text: textContent
                    }
                });
                index += textContent.length;
            }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node;

            if (element.tagName === 'IMG') {
                // Handle image elements
                console.log('Processing IMG element:', element.src?.substring(0, 50) + '...', element.alt);
                const imageResult = await processImageForGoogleDocs(element, index);
                if (imageResult.success) {
                    requests.push(...imageResult.requests);
                    index = imageResult.newIndex;
                } else {
                    // If image processing fails, add descriptive placeholder
                    const altText = element.alt || 'Image';
                    const timestamp = element.getAttribute('data-timestamp');
                    let placeholderText = `[${altText}`;
                    if (timestamp) {
                        placeholderText += ` at ${timestamp}`;
                    }
                    placeholderText += ']';

                    requests.push({
                        insertText: {
                            location: { index: index },
                            text: placeholderText
                        }
                    });
                    index += placeholderText.length;
                }
            } else if (element.tagName === 'SPAN' && element.classList.contains('Stashy-equation')) {
                // Handle equation elements
                const latex = element.getAttribute('data-latex');
                console.log('Processing equation:', latex);
                if (latex && latex.trim() !== '') {
                    // Has actual LaTeX content
                    const equationText = `[Equation: ${latex}]`;
                    requests.push({
                        insertText: {
                            location: { index: index },
                            text: equationText
                        }
                    });
                    index += equationText.length;
                } else {
                    // Empty equation or no LaTeX - check text content
                    const textContent = element.textContent.trim();
                    if (textContent && textContent !== '[ New Equation ]') {
                        // Has text content that's not the placeholder
                        requests.push({
                            insertText: {
                                location: { index: index },
                                text: textContent
                            }
                        });
                        index += textContent.length;
                    } else {
                        // Skip empty equations or placeholder text
                        console.log('Skipping empty equation or placeholder');
                    }
                }
            } else if (element.tagName === 'BR') {
                // Handle line breaks
                requests.push({
                    insertText: {
                        location: { index: index },
                        text: '\n'
                    }
                });
                index += 1;
            } else if (['P', 'DIV', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6'].includes(element.tagName)) {
                // Handle block elements - process children and add line breaks
                for (const childNode of element.childNodes) {
                    const childResult = await processNodeRecursively(childNode, index);
                    requests.push(...childResult.requests);
                    index = childResult.newIndex;
                }
                // Add line break after block elements
                requests.push({
                    insertText: {
                        location: { index: index },
                        text: '\n'
                    }
                });
                index += 1;
            } else {
                // Handle other elements - process children without adding extra formatting
                for (const childNode of element.childNodes) {
                    const childResult = await processNodeRecursively(childNode, index);
                    requests.push(...childResult.requests);
                    index = childResult.newIndex;
                }
            }
        }

        return {
            requests: requests,
            newIndex: index
        };
    }

    /**
     * Extracts text with placeholders for images and equations as fallback
     * @param {string} htmlContent - The HTML content
     * @returns {string} Text with placeholders
     */
    function extractTextWithPlaceholders(htmlContent) {
        try {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;

            // Replace images with placeholders
            const images = tempDiv.querySelectorAll('img');
            images.forEach(img => {
                const alt = img.alt || 'Image';
                const timestamp = img.getAttribute('data-timestamp');
                let placeholder = `[${alt}`;
                if (timestamp) {
                    placeholder += ` at ${timestamp}`;
                }
                placeholder += ']';
                img.replaceWith(document.createTextNode(placeholder));
            });

            // Replace equations with placeholders
            const equations = tempDiv.querySelectorAll('.Stashy-equation');
            equations.forEach(eq => {
                const latex = eq.getAttribute('data-latex');
                const placeholder = latex ? `[Equation: ${latex}]` : '[Equation]';
                eq.replaceWith(document.createTextNode(placeholder));
            });

            return tempDiv.textContent || tempDiv.innerText || '';
        } catch (error) {
            console.error('Error extracting text with placeholders:', error);
            return htmlContent.replace(/<[^>]*>/g, '');
        }
    }



    /**
     * Processes an image element for Google Docs insertion
     * @param {HTMLImageElement} imgElement - The image element to process
     * @param {number} currentIndex - The current index in the document
     * @returns {Promise<Object>} Object containing success status, requests, and new index
     */
    async function processImageForGoogleDocs(imgElement, currentIndex) {
        try {
            const src = imgElement.src;
            const alt = imgElement.alt || 'Embedded Image';
            const timestamp = imgElement.getAttribute('data-timestamp');
            const className = imgElement.className || '';

            console.log('Processing image for Google Docs:', { src: src?.substring(0, 50) + '...', alt, timestamp, className });

            if (!src) {
                console.log('No src found for image');
                return {
                    success: false,
                    requests: [],
                    newIndex: currentIndex
                };
            }

            // Handle data URLs (base64 images)
            if (src.startsWith('data:image/')) {
                console.log('Processing base64 image...');

                // Image should already be optimized from pre-processing
                // First try to upload to Google Drive for actual image embedding
                try {
                    const driveImageUrl = await uploadBase64ImageToDrive(src, alt);
                    if (driveImageUrl) {
                        console.log('Successfully uploaded image to Drive:', driveImageUrl);
                        // Insert the image using the Drive URL
                        const imageRequest = {
                            insertInlineImage: {
                                location: { index: currentIndex },
                                uri: driveImageUrl,
                                objectSize: {
                                    height: { magnitude: 200, unit: 'PT' },
                                    width: { magnitude: 300, unit: 'PT' }
                                }
                            }
                        };

                        return {
                            success: true,
                            requests: [imageRequest],
                            newIndex: currentIndex + 1 // Images take 1 character space in Google Docs
                        };
                    }
                } catch (error) {
                    console.error('Error uploading base64 image to Drive:', error);
                }

                // If Drive upload fails, try direct base64 insertion (Google Docs supports this for some formats)
                console.log('Drive upload failed, trying direct base64 insertion...');
                try {
                    const imageRequest = {
                        insertInlineImage: {
                            location: { index: currentIndex },
                            uri: src, // Try using the data URL directly
                            objectSize: {
                                height: { magnitude: 200, unit: 'PT' },
                                width: { magnitude: 300, unit: 'PT' }
                            }
                        }
                    };

                    return {
                        success: true,
                        requests: [imageRequest],
                        newIndex: currentIndex + 1
                    };
                } catch (error) {
                    console.error('Error inserting base64 image directly:', error);
                }

                // Final fallback: create descriptive placeholder
                console.log('All image insertion methods failed, using descriptive placeholder');
                let placeholderText = `[Image: ${alt}`;
                if (timestamp) {
                    placeholderText += ` at ${timestamp}`;
                }
                if (className.includes('video-screenshot')) {
                    placeholderText += ' (Video Screenshot)';
                }
                placeholderText += ']\n';

                return {
                    success: true,
                    requests: [{
                        insertText: {
                            location: { index: currentIndex },
                            text: placeholderText
                        }
                    }],
                    newIndex: currentIndex + placeholderText.length
                };
            } else {
                // Handle regular URLs
                try {
                    // Insert the image using Google Docs API
                    const imageRequest = {
                        insertInlineImage: {
                            location: { index: currentIndex },
                            uri: src,
                            objectSize: {
                                height: { magnitude: 200, unit: 'PT' },
                                width: { magnitude: 300, unit: 'PT' }
                            }
                        }
                    };

                    return {
                        success: true,
                        requests: [imageRequest],
                        newIndex: currentIndex + 1 // Images take 1 character space in Google Docs
                    };
                } catch (error) {
                    console.error('Error inserting image:', error);
                    // Fallback to alt text
                    const fallbackText = `[Image: ${alt}]\n`;
                    return {
                        success: true,
                        requests: [{
                            insertText: {
                                location: { index: currentIndex },
                                text: fallbackText
                            }
                        }],
                        newIndex: currentIndex + fallbackText.length
                    };
                }
            }
        } catch (error) {
            console.error('Error processing image for Google Docs:', error);
            return {
                success: false,
                requests: [],
                newIndex: currentIndex
            };
        }
    }

    /**
     * Optimizes an image for faster upload by reducing size if necessary
     * @param {string} dataUrl - The base64 data URL of the image
     * @returns {Promise<string>} The optimized data URL
     */
    async function optimizeImageForUpload(dataUrl) {
        try {
            // No compression - user has unlimited storage
            console.log(`Image upload: ${dataUrl.length} bytes (no compression applied)`);
            return dataUrl;
        } catch (error) {
            console.error('Error processing image:', error);
            return dataUrl; // Return original on error
        }
    }

    /**
     * Uploads a base64 image to Google Drive and returns a public URL
     * @param {string} dataUrl - The base64 data URL of the image
     * @param {string} altText - Alt text for the image (used as filename)
     * @returns {Promise<string|null>} The public URL of the uploaded image or null if failed
     */
    async function uploadBase64ImageToDrive(dataUrl, altText, retryCount = 0) {
        const maxRetries = 2;

        try {
            console.log('Starting image upload to Drive...', { altText, dataUrlLength: dataUrl.length, attempt: retryCount + 1 });

            // Check if we have an auth token
            if (!authToken) {
                console.error('No auth token available for Google Drive upload');
                return null;
            }

            // Extract the base64 data and mime type
            const matches = dataUrl.match(/^data:([^;]+);base64,(.+)$/);
            if (!matches) {
                console.error('Invalid data URL format');
                return null;
            }

            const mimeType = matches[1];
            const base64Data = matches[2];
            console.log('Image details:', { mimeType, base64Length: base64Data.length });

            // Generate a filename
            const extension = mimeType.split('/')[1] || 'png';
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const filename = `Stashy-image-${timestamp}-${altText.replace(/[^a-zA-Z0-9]/g, '_')}.${extension}`;

            // Create multipart body manually for Google Drive API
            const boundary = '-------314159265358979323846';
            const delimiter = "\r\n--" + boundary + "\r\n";
            const close_delim = "\r\n--" + boundary + "--";

            const metadata = {
                name: filename,
                description: `Stashy screenshot: ${altText}`
            };

            let multipartRequestBody =
                delimiter +
                'Content-Type: application/json\r\n\r\n' +
                JSON.stringify(metadata) +
                delimiter +
                'Content-Type: ' + mimeType + '\r\n' +
                'Content-Transfer-Encoding: base64\r\n' +
                '\r\n' +
                base64Data +
                close_delim;

            // Upload to Google Drive with rate limiting and timeout
            console.log('Uploading image to Google Drive with rate limiting...');
            const uploadResponse = await Promise.race([
                makeRateLimitedApiCall('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'multipart/related; boundary="' + boundary + '"'
                    },
                    body: multipartRequestBody
                }),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Upload timeout')), 30000)
                )
            ]);

            if (!uploadResponse.ok) {
                const errorText = await uploadResponse.text();
                console.error('Failed to upload image to Drive:', uploadResponse.statusText, errorText);

                // Enhanced retry logic for rate limit errors
                if (retryCount < maxRetries && (uploadResponse.status === 403 || uploadResponse.status === 429 || uploadResponse.status >= 500)) {
                    const delay = RATE_LIMIT_DELAY * Math.pow(2, retryCount);
                    console.log(`Retrying image upload in ${delay}ms... (attempt ${retryCount + 1}/${maxRetries})`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    return await uploadBase64ImageToDrive(dataUrl, altText, retryCount + 1);
                }

                return null;
            }

            const uploadResult = await uploadResponse.json();
            const fileId = uploadResult.id;
            console.log('File uploaded successfully:', fileId);

            // Make the file publicly viewable (with error handling and rate limiting)
            try {
                console.log('Setting file permissions with rate limiting...');
                const permissionResponse = await makeRateLimitedApiCall(`https://www.googleapis.com/drive/v3/files/${fileId}/permissions`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        role: 'reader',
                        type: 'anyone'
                    })
                });

                if (permissionResponse.ok) {
                    console.log('File permissions set successfully');
                } else {
                    console.warn('Failed to set file permissions, but continuing...');
                }
            } catch (permError) {
                console.warn('Error setting permissions, but file uploaded successfully:', permError);
            }

            // Return the public URL
            const publicUrl = `https://drive.google.com/uc?id=${fileId}`;
            console.log('Generated public URL:', publicUrl);
            return publicUrl;

        } catch (error) {
            console.error('Error uploading base64 image to Drive:', error);

            // Retry on network errors and rate limit errors
            if (retryCount < maxRetries && (
                error.message.includes('timeout') ||
                error.message.includes('network') ||
                error.message.includes('403') ||
                error.message.includes('rate limit')
            )) {
                const delay = RATE_LIMIT_DELAY * Math.pow(2, retryCount);
                console.log(`Retrying upload due to ${error.message} in ${delay}ms... (attempt ${retryCount + 1}/${maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, delay));
                return await uploadBase64ImageToDrive(dataUrl, altText, retryCount + 1);
            }

            return null;
        }
    }

    /**
     * Exports notes to a Google Doc
     * @param {Array} notes - The notes to export
     * @param {string} [title='Stashy Notes'] - The title of the document
     * @returns {Promise<Object>} A promise that resolves to the created document
     */
    async function exportNotes(notes, title = 'Stashy Notes') {
        try {
            if (!isInitialized) {
                await init();
            }

            console.log(`Starting export of ${notes.length} notes with rate limiting...`);

            // Create a new document
            const doc = await createDoc(title);

            // Build requests array for batch update
            const requests = [];
            let currentIndex = 1; // Start after the default paragraph

            // Add title and export date
            const headerText = title + '\n\nExported on: ' + new Date().toLocaleDateString() + '\n\n';
            requests.push({
                insertText: {
                    location: { index: currentIndex },
                    text: headerText
                }
            });
            currentIndex += headerText.length;

            // Process each note
            for (let i = 0; i < notes.length; i++) {
                const note = notes[i];

                // Add note title
                const noteTitle = note.title || note.data?.title || `Note ${i + 1}`;
                const noteTitleText = noteTitle + '\n';
                requests.push({
                    insertText: {
                        location: { index: currentIndex },
                        text: noteTitleText
                    }
                });
                currentIndex += noteTitleText.length;

                // Process note content with images
                const noteContent = note.text || note.data?.text || '';
                console.log(`Processing note ${i + 1}/${notes.length}: ${noteTitle}`);

                if (noteContent.length > 0) {
                    const imageCount = (noteContent.match(/<img/g) || []).length;
                    const equationCount = (noteContent.match(/class="Stashy-equation"/g) || []).length;
                    if (imageCount > 0 || equationCount > 0) {
                        console.log(`  - Found ${imageCount} images and ${equationCount} equations`);
                    }
                }

                if (noteContent) {
                    const contentRequests = await parseContentWithImages(noteContent, currentIndex);
                    requests.push(...contentRequests.requests);
                    currentIndex = contentRequests.newIndex;
                }

                // Add URL if available
                const noteUrl = note.url || note.data?.url || '';
                if (noteUrl) {
                    const urlText = 'URL: ' + noteUrl + '\n';
                    requests.push({
                        insertText: {
                            location: { index: currentIndex },
                            text: urlText
                        }
                    });
                    currentIndex += urlText.length;
                }

                // Add separator between notes
                if (i < notes.length - 1) {
                    const separatorText = '\n---\n\n';
                    requests.push({
                        insertText: {
                            location: { index: currentIndex },
                            text: separatorText
                        }
                    });
                    currentIndex += separatorText.length;
                } else {
                    const endText = '\n';
                    requests.push({
                        insertText: {
                            location: { index: currentIndex },
                            text: endText
                        }
                    });
                    currentIndex += endText.length;
                }
            }

            // Update the document with all requests
            if (requests.length > 0) {
                await batchUpdate(doc.id, requests);
            }

            return {
                success: true,
                documentId: doc.id,
                documentUrl: `https://docs.google.com/document/d/${doc.id}/edit`,
                message: 'Notes exported successfully'
            };
        } catch (error) {
            console.error('Error exporting notes to Google Docs:', error);
            return {
                success: false,
                error: error.message || 'Unknown error'
            };
        }
    }

    /**
     * Exports highlights to a Google Doc
     * @param {Array} highlights - The highlights to export
     * @param {string} [title='Stashy Highlights'] - The title of the document
     * @returns {Promise<Object>} A promise that resolves to the created document
     */
    async function exportHighlights(highlights, title = 'Stashy Highlights') {
        try {
            if (!isInitialized) {
                await init();
            }

            console.log(`Starting export of ${highlights.length} highlights with rate limiting...`);

            // Create a new document
            const doc = await createDoc(title);

            // Build requests array for batch update
            const requests = [];
            let currentIndex = 1; // Start after the default paragraph

            // Add title and export date
            const headerText = title + '\n\nExported on: ' + new Date().toLocaleDateString() + '\n\n';
            requests.push({
                insertText: {
                    location: { index: currentIndex },
                    text: headerText
                }
            });
            currentIndex += headerText.length;

            // Process each highlight with progress logging
            for (let i = 0; i < highlights.length; i++) {
                const highlight = highlights[i];
                console.log(`Processing highlight ${i + 1}/${highlights.length}...`);

                // Add highlight text
                const highlightText = highlight.text || highlight.data?.text || '';
                if (highlightText) {
                    // Process highlight text for images (in case it contains HTML)
                    const contentRequests = await parseContentWithImages(highlightText, currentIndex);
                    requests.push(...contentRequests.requests);
                    currentIndex = contentRequests.newIndex;
                }

                // Add highlight note if available
                const highlightNote = highlight.data?.noteText || '';
                if (highlightNote) {
                    const noteText = 'Note: ';
                    requests.push({
                        insertText: {
                            location: { index: currentIndex },
                            text: noteText
                        }
                    });
                    currentIndex += noteText.length;

                    // Process note text for images
                    const noteRequests = await parseContentWithImages(highlightNote, currentIndex);
                    requests.push(...noteRequests.requests);
                    currentIndex = noteRequests.newIndex;
                }

                // Add URL if available
                const highlightUrl = highlight.url || highlight.data?.url || '';
                if (highlightUrl) {
                    const urlText = 'URL: ' + highlightUrl + '\n';
                    requests.push({
                        insertText: {
                            location: { index: currentIndex },
                            text: urlText
                        }
                    });
                    currentIndex += urlText.length;
                }

                // Add separator between highlights
                if (i < highlights.length - 1) {
                    const separatorText = '\n---\n\n';
                    requests.push({
                        insertText: {
                            location: { index: currentIndex },
                            text: separatorText
                        }
                    });
                    currentIndex += separatorText.length;
                } else {
                    const endText = '\n';
                    requests.push({
                        insertText: {
                            location: { index: currentIndex },
                            text: endText
                        }
                    });
                    currentIndex += endText.length;
                }
            }

            // Update the document with all requests
            if (requests.length > 0) {
                await batchUpdate(doc.id, requests);
            }

            return {
                success: true,
                documentId: doc.id,
                documentUrl: `https://docs.google.com/document/d/${doc.id}/edit`,
                message: 'Highlights exported successfully'
            };
        } catch (error) {
            console.error('Error exporting highlights to Google Docs:', error);
            return {
                success: false,
                error: error.message || 'Unknown error'
            };
        }
    }



    // Return the public API
    return {
        init,
        exportNotes,
        exportHighlights,
        isInitialized: () => isInitialized
    };
})();

console.log("Stashy: Google Docs Integration Module Loaded");
