<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stashy Note Settings</title>
    <link rel="stylesheet" href="settings.css">
</head>
<body>
    <div class="settings-container">
        <header>
            <h1>Stashy Note Settings</h1>
            <p>Customize your Stashy note appearance and behavior</p>
        </header>

        <main>

            <section class="settings-section">
                <h2>Default Note Size</h2>
                <p class="section-description">Set the default size for new notes</p>

                <div class="setting-item">
                    <div class="setting-label">
                        <label for="default-width">Width</label>
                    </div>
                    <div class="setting-control">
                        <input type="number" id="default-width" min="200" max="800" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <label for="default-height">Height</label>
                    </div>
                    <div class="setting-control">
                        <input type="number" id="default-height" min="150" max="800" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>
            </section>

            <section class="settings-section">
                <h2>Default Note Position</h2>
                <p class="section-description">Set the default position for new notes</p>

                <div class="setting-item">
                    <div class="setting-label">
                        <label for="default-top">Top</label>
                    </div>
                    <div class="setting-control">
                        <input type="number" id="default-top" min="0" max="500" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>

                <div class="setting-item">
                    <div class="setting-label">
                        <label for="default-right">Right</label>
                    </div>
                    <div class="setting-control">
                        <input type="number" id="default-right" min="0" max="500" step="10">
                        <span class="unit">px</span>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <button id="reset-button" class="secondary-button">Reset to Defaults</button>
            <button id="save-button" class="primary-button">Save Settings</button>
        </footer>
    </div>

    <script src="settings.js"></script>
    <script src="settings-ui.js"></script>
</body>
</html>
