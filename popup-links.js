// Update documentation links with the correct extension ID
document.addEventListener('DOMContentLoaded', function() {
    // Get the current extension ID
    const extensionId = chrome.runtime.id;

    // Get all help links (now in the help-info-panel)
    const helpLinks = document.querySelectorAll('.help-link');

    // Update each link with the correct extension ID
    helpLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && href.includes('chrome-extension://')) {
            // Replace the hardcoded extension ID with the current one
            const newHref = href.replace(/chrome-extension:\/\/[^\/]+/, `chrome-extension://${extensionId}`);
            link.setAttribute('href', newHref);
        } else if (href && !href.includes('://')) {
            // For relative URLs, convert to absolute with the extension ID
            link.setAttribute('href', `chrome-extension://${extensionId}/${href}`);
        }
    });
});
