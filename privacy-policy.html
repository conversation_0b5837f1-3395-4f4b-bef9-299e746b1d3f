<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stashy - Privacy Policy</title>
    <link rel="stylesheet" href="privacy-policy.css">
</head>
<body>
    <h1>🔒 Privacy Policy</h1>

    <div class="effective-date">
        <strong>Effective Date:</strong> January 2025 | <strong>Version:</strong> 1.0 | <strong>Last Updated:</strong> January 2025
    </div>

    <div class="privacy-highlight">
        <strong>Privacy-First Promise:</strong> Stashy is built with privacy as our foundational principle. Your data stays on your device, under your control, always.
    </div>

    <h2>🛡️ Our Privacy Commitment</h2>
    <div class="privacy-section">
        <p>Stashy is designed with privacy-by-design principles. We believe your digital notes, highlights, and browsing data belong exclusively to you. Our extension operates with a "local-first" approach, ensuring your information never leaves your device unless you explicitly choose to sync it to your own cloud accounts.</p>

        <p><strong>Core Privacy Principles:</strong></p>
        <ul>
            <li><strong>Zero Data Collection:</strong> We don't collect, analyze, or monetize your personal data</li>
            <li><strong>Local Storage First:</strong> All data stored securely in your browser by default</li>
            <li><strong>User Control:</strong> You decide what data to share and with whom</li>
            <li><strong>Transparency:</strong> Open about our practices and technical implementation</li>
            <li><strong>Minimal Permissions:</strong> Only request access necessary for core functionality</li>
        </ul>
    </div>

    <h2>📊 Data We Do NOT Collect</h2>
    <div class="privacy-section">
        <p><strong>We explicitly do not collect, store, transmit, or have access to:</strong></p>
        <ul>
            <li><strong>Personal Information:</strong> Names, email addresses, phone numbers, or contact details</li>
            <li><strong>Browsing History:</strong> Websites you visit, pages you view, or browsing patterns</li>
            <li><strong>Note Content:</strong> Text, highlights, or any content you create</li>
            <li><strong>Usage Analytics:</strong> How you use the extension or feature usage statistics</li>
            <li><strong>Device Information:</strong> Hardware details, operating system, or device identifiers</li>
            <li><strong>Location Data:</strong> Geographic location or IP address tracking</li>
            <li><strong>Behavioral Data:</strong> Interaction patterns, preferences, or user behavior analysis</li>
        </ul>
    </div>

    <h2>💾 Local Data Storage</h2>
    <div class="privacy-section">
        <p>All your data is stored locally in your browser using Chrome's secure extension storage APIs:</p>

        <h3>What's Stored Locally:</h3>
        <ul>
            <li><strong>Notes & Highlights:</strong> All text content, formatting, and highlight data</li>
            <li><strong>Notebooks & Organization:</strong> Custom notebooks, tags, and organizational structure</li>
            <li><strong>Settings & Preferences:</strong> UI customizations, feature configurations, and user preferences</li>
            <li><strong>Templates:</strong> Custom templates and content creation tools</li>
            <li><strong>API Keys (Encrypted):</strong> If you use AI features, keys are encrypted before local storage</li>
            <li><strong>Export History:</strong> Records of your data exports for your reference</li>
        </ul>

        <h3>Storage Security:</h3>
        <ul>
            <li><strong>Browser Encryption:</strong> Chrome automatically encrypts extension storage</li>
            <li><strong>Additional Encryption:</strong> Sensitive data like API keys receive extra encryption</li>
            <li><strong>Isolated Storage:</strong> Data isolated from other extensions and websites</li>
            <li><strong>User Profile Bound:</strong> Data tied to your browser profile, not shared across users</li>
        </ul>
    </div>

    <h2>☁️ Optional Cloud Integration</h2>
    <div class="privacy-section">
        <p>Stashy offers optional integration with your personal cloud accounts. This is entirely opt-in and under your control:</p>

        <h3>Google Integration (Optional):</h3>
        <ul>
            <li><strong>OAuth2 Authentication:</strong> Secure, industry-standard authentication</li>
            <li><strong>Limited Scope Access:</strong> Only access services you explicitly authorize</li>
            <li><strong>Direct Communication:</strong> Your browser communicates directly with Google</li>
            <li><strong>No Data Interception:</strong> We don't see or store your Google account data</li>
            <li><strong>Revocable Access:</strong> Disconnect at any time through Google account settings</li>
            <li><strong>Specific Permissions:</strong> Drive (file storage), Docs (export), Calendar (reminders)</li>
        </ul>

        <h3>What Happens When You Sync:</h3>
        <ul>
            <li><strong>Direct Upload:</strong> Your data goes directly from your browser to your Google Drive</li>
            <li><strong>Your Ownership:</strong> Files stored in your personal Google Drive account</li>
            <li><strong>Google's Policies:</strong> Synced data subject to Google's privacy policy</li>
            <li><strong>Encryption in Transit:</strong> All transfers use HTTPS encryption</li>
        </ul>
    </div>

    <h2>🤖 AI Features Privacy</h2>
    <div class="privacy-section">
        <p>Stashy's AI features are designed with privacy and user control as priorities:</p>

        <h3>How AI Features Work:</h3>
        <ul>
            <li><strong>Your API Keys:</strong> You provide your own API keys from AI providers</li>
            <li><strong>Direct Communication:</strong> Your browser sends requests directly to AI providers</li>
            <li><strong>No Proxy Servers:</strong> We don't route your data through our servers</li>
            <li><strong>Encrypted Storage:</strong> API keys encrypted before local storage</li>
            <li><strong>Provider Choice:</strong> Choose from multiple AI providers (OpenAI, Anthropic, Google, etc.)</li>
        </ul>

        <h3>AI Data Flow:</h3>
        <ul>
            <li><strong>Content Processing:</strong> Only content you explicitly select is sent to AI providers</li>
            <li><strong>Provider Policies:</strong> AI processing subject to your chosen provider's privacy policy</li>
            <li><strong>No Stashy Involvement:</strong> We don't see, store, or process your AI requests</li>
            <li><strong>Response Handling:</strong> AI responses stored locally in your browser</li>
        </ul>
    </div>

    <h2>🔐 Security Measures</h2>
    <div class="privacy-section">
        <h3>Technical Security:</h3>
        <ul>
            <li><strong>Content Security Policy (CSP):</strong> Strict CSP prevents unauthorized code execution</li>
            <li><strong>HTTPS Only:</strong> All external communications use encrypted HTTPS</li>
            <li><strong>No Remote Code Loading:</strong> All extension code is bundled and reviewed</li>
            <li><strong>Sandboxed Execution:</strong> Extension runs in Chrome's secure sandbox</li>
            <li><strong>Regular Security Updates:</strong> Prompt security patches and updates</li>
        </ul>

        <h3>Data Protection:</h3>
        <ul>
            <li><strong>Encryption at Rest:</strong> Sensitive data encrypted before storage</li>
            <li><strong>Secure Key Management:</strong> API keys use additional encryption layers</li>
            <li><strong>Input Sanitization:</strong> All user input sanitized to prevent injection attacks</li>
            <li><strong>Permission Minimization:</strong> Only request necessary browser permissions</li>
        </ul>
    </div>

    <h2>🔑 Browser Permissions Explained</h2>
    <div class="privacy-section">
        <p>Stashy requests minimal permissions necessary for functionality:</p>

        <ul>
            <li><strong>activeTab:</strong> Access current tab only when you click the extension icon (privacy-first design)</li>
            <li><strong>storage:</strong> Store your notes, settings, and preferences locally</li>
            <li><strong>unlimitedStorage:</strong> Allow large note collections without browser limits</li>
            <li><strong>identity:</strong> Enable optional Google account integration</li>
            <li><strong>downloads:</strong> Export your notes and data to your computer</li>
            <li><strong>notifications:</strong> Show helpful notifications and reminders</li>
            <li><strong>alarms:</strong> Schedule reminders and background tasks</li>
            <li><strong>offscreen:</strong> Secure processing for encryption and AI features</li>
        </ul>

        <p><strong>Notable Permissions We DON'T Request:</strong></p>
        <ul>
            <li><strong>tabs:</strong> We don't monitor or access all your browser tabs</li>
            <li><strong>history:</strong> We don't access your browsing history</li>
            <li><strong>bookmarks:</strong> We don't access your bookmarks</li>
            <li><strong>webNavigation:</strong> We don't track your navigation patterns</li>
        </ul>
    </div>

    <div class="contact-info">
        <h3>📞 Contact Information</h3>
        <p>Questions about privacy, data handling, or this policy?</p>
        <p><strong>Privacy Email:</strong> <EMAIL></p>
        <p><strong>General Support:</strong> <EMAIL></p>
        <p><strong>Website:</strong> <a href="https://stashy.app" target="_blank">https://stashy.app</a></p>
        <p><strong>Response Time:</strong> We aim to respond to privacy inquiries within 48 hours</p>
    </div>

    <p class="last-updated">
        This privacy policy is effective as of January 2025 and applies to Stashy version 1.0 and later.
        <br>Last reviewed and updated: January 2025
        <br>Policy version: 1.0
    </p>
</body>
</html>
