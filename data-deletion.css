/* Modern CSS Variables for Consistent Theming */
:root {
    --primary-color: #DC2626;
    --primary-light: #EF4444;
    --primary-bg: #FEF2F2;
    --secondary-color: #7C3AED;
    --secondary-light: #8B5CF6;
    --secondary-bg: #EDE9FE;
    --accent-color: #059669;
    --accent-light: #10B981;
    --accent-bg: #D1FAE5;
    --warning-color: #F59E0B;
    --warning-light: #FCD34D;
    --warning-bg: #FEF3C7;
    --info-color: #2563EB;
    --info-light: #3B82F6;
    --info-bg: #DBEAFE;
    --text-primary: #1F2937;
    --text-secondary: #6B7280;
    --text-muted: #9CA3AF;
    --bg-primary: #FFFFFF;
    --bg-secondary: #F9FAFB;
    --bg-tertiary: #F3F4F6;
    --border-light: #E5E7EB;
    --border-medium: #D1D5DB;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
}

/* Enhanced Typography and Layout */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.7;
    max-width: 900px;
    margin: 0 auto;
    padding: 24px;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    font-size: 16px;
}

/* Enhanced Header Styling */
h1 {
    color: var(--primary-color);
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 16px;
    margin-bottom: 32px;
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

h2 {
    color: var(--primary-color);
    margin-top: 48px;
    margin-bottom: 24px;
    border-bottom: 2px solid var(--primary-bg);
    padding-bottom: 12px;
    font-size: 1.875rem;
    font-weight: 600;
    letter-spacing: -0.025em;
    position: relative;
}

h2::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

/* Enhanced Section Cards */
.section {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

/* Enhanced Step-by-Step Instructions */
.steps {
    margin: 24px 0;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.step {
    padding: 20px;
    background: linear-gradient(135deg, var(--info-bg) 0%, #E0F2FE 100%);
    border: 1px solid var(--info-light);
    border-left: 4px solid var(--info-color);
    border-radius: var(--radius-md);
    position: relative;
    transition: all 0.3s ease;
}

.step:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: var(--info-color);
    color: white;
    border-radius: 50%;
    font-weight: 700;
    font-size: 14px;
    margin-right: 12px;
    flex-shrink: 0;
}

/* Enhanced Alert Boxes */
.note {
    background: linear-gradient(135deg, var(--warning-bg) 0%, #FEF9C3 100%);
    border: 1px solid var(--warning-light);
    border-radius: var(--radius-md);
    padding: 20px;
    margin: 24px 0;
    border-left: 4px solid var(--warning-color);
    position: relative;
}

.note::before {
    content: '⚠️';
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 20px;
}

.note strong {
    color: var(--warning-color);
    margin-left: 32px;
}

.note p {
    margin-left: 32px;
}

/* Enhanced Code Styling */
code {
    background-color: var(--bg-tertiary);
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.875em;
    color: var(--text-primary);
    border: 1px solid var(--border-light);
}

/* Enhanced Typography */
p {
    margin-bottom: 16px;
    line-height: 1.7;
    color: var(--text-secondary);
}

ul {
    padding-left: 24px;
    margin: 16px 0;
}

li {
    margin: 12px 0;
    line-height: 1.6;
    color: var(--text-secondary);
}

li strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* Enhanced Links */
a {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--accent-light);
    text-decoration: underline;
}

/* Enhanced Footer */
.last-updated {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    margin-top: 48px;
    padding-top: 24px;
    border-top: 2px solid var(--border-light);
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 16px;
        font-size: 14px;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .section {
        padding: 20px;
    }

    .step {
        padding: 16px;
    }

    .note {
        padding: 16px;
    }
}
