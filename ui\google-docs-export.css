/* Google Docs Export UI Styles */

/* Export Button */
.Stashy-gdocs-btn {
    display: inline-block;
    margin-top: 10px;
    padding: 8px 12px;
    background-color: #4285F4; /* Google blue */
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.Stashy-gdocs-btn:hover {
    background-color: #3367D6; /* Darker Google blue */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.Stashy-gdocs-btn .Stashy-icon {
    margin-right: 5px;
}

/* Modal */
.Stashy-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: none;
    justify-content: center;
    align-items: center;
}

.Stashy-modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    width: 400px;
    max-width: 90%;
    max-height: 90%;
    overflow: auto;
}

.Stashy-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.Stashy-modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #4285F4;
}

.Stashy-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    line-height: 1;
}

.Stashy-modal-body {
    margin-bottom: 20px;
}

.Stashy-modal-body label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.Stashy-input {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.Stashy-option {
    margin-bottom: 10px;
}

.Stashy-option input[type="checkbox"] {
    margin-right: 5px;
}

.Stashy-select {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.Stashy-status {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    display: none;
}

.Stashy-status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.Stashy-status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.Stashy-status.info {
    background-color: #cce5ff;
    color: #004085;
    border: 1px solid #b8daff;
}

.Stashy-status a {
    color: inherit;
    text-decoration: underline;
    font-weight: bold;
}

.Stashy-modal-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.Stashy-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.Stashy-cancel-btn {
    background-color: #f1f1f1;
    color: #333;
    margin-right: 10px;
}

.Stashy-confirm-btn {
    background-color: #4285F4;
    color: white;
    font-weight: bold;
}

.Stashy-confirm-btn:hover {
    background-color: #3367D6;
}

/* Dashboard Integration */
.Stashy-dashboard-action-btn {
    display: inline-block;
    background-color: #4285F4;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    margin: 5px;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.Stashy-dashboard-action-btn:hover {
    background-color: #3367D6;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.Stashy-dashboard-action-btn .Stashy-icon {
    margin-right: 5px;
}
