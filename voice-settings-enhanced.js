// Enhanced Voice Settings Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const providerSelect = document.getElementById('voice-provider');
    const apiKeyContainer = document.getElementById('api-key-container');
    const apiKeyInput = document.getElementById('voice-api-key');
    const languageSelect = document.getElementById('voice-language');
    const punctuationCheckbox = document.getElementById('voice-punctuation');
    const capitalizationCheckbox = document.getElementById('voice-capitalization');
    const profanityFilterCheckbox = document.getElementById('voice-profanity-filter');
    const interimResultsCheckbox = document.getElementById('voice-interim-results');
    const silenceThresholdInput = document.getElementById('voice-silence-threshold');
    const autoDetectCheckbox = document.getElementById('voice-auto-detect');
    const backgroundNoiseReductionCheckbox = document.getElementById('voice-background-noise-reduction');
    const speakerDiarizationCheckbox = document.getElementById('voice-speaker-diarization');
    const qualitySelect = document.getElementById('voice-quality');

    // Audio processing elements
    const audioMonitoringCheckbox = document.getElementById('voice-audio-monitoring');
    const adaptiveThresholdsCheckbox = document.getElementById('voice-adaptive-thresholds');
    const enhancedConstraintsCheckbox = document.getElementById('voice-enhanced-constraints');
    const audioSensitivityInput = document.getElementById('voice-audio-sensitivity');
    const noiseSuppressionSelect = document.getElementById('voice-noise-suppression');

    const saveButton = document.getElementById('save-voice-settings');
    const resetButton = document.getElementById('reset-settings');
    const statusMessage = document.getElementById('status-message');
    const statusText = document.getElementById('status-text');

    // Function to check for missing language display names and add them
    function checkAndAddMissingLanguageNames() {
        // Get all languages from all providers
        const allLanguages = new Set();

        Object.values(providerLanguages).forEach(languages => {
            languages.forEach(lang => allLanguages.add(lang));
        });

        // Check each language for a display name
        allLanguages.forEach(lang => {
            if (!languageDisplayNames[lang]) {
                // Create a display name from the language code
                let displayName = lang;

                // If it's a language code with region (e.g., en-US)
                if (lang.includes('-')) {
                    const [langCode, regionCode] = lang.split('-');

                    // Try to get the base language name
                    const baseLangName = languageDisplayNames[langCode] || getLanguageNameFromCode(langCode);

                    // Format as "Language (Region)"
                    displayName = `${baseLangName} (${regionCode})`;
                } else {
                    // It's just a language code without region
                    displayName = getLanguageNameFromCode(lang);
                }

                // Add to our display names
                languageDisplayNames[lang] = displayName;
                console.log(`Added missing language display name: ${lang} -> ${displayName}`);
            }
        });
    }

    // Check for missing language names before initializing the UI
    checkAndAddMissingLanguageNames();

    // Provider language maps - Simplified for better user experience
    const providerLanguages = {
        // Browser API - Common languages that work well with browser API
        browser: [
            'en-US', 'en-GB', 'es-ES', 'fr-FR', 'de-DE', 'it-IT', 'pt-BR', 'ru-RU',
            'zh-CN', 'ja-JP', 'ko-KR', 'nl-NL', 'pl-PL', 'sv-SE', 'tr-TR', 'ar-SA',
            'cs-CZ', 'da-DK', 'fi-FI', 'hi-IN', 'hu-HU', 'id-ID', 'nb-NO', 'pt-PT',
            'th-TH', 'vi-VN', 'el-GR', 'he-IL', 'ro-RO', 'sk-SK', 'uk-UA'
        ],

        // Google Speech API - Comprehensive language support
        google: [
            // English variants
            'en', 'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-PH', 'en-SG', 'en-ZA',
            // Spanish variants
            'es', 'es-ES', 'es-AR', 'es-BO', 'es-CL', 'es-CO', 'es-CR', 'es-DO', 'es-EC', 'es-GT', 'es-HN',
            'es-MX', 'es-NI', 'es-PA', 'es-PE', 'es-PR', 'es-PY', 'es-SV', 'es-US', 'es-UY', 'es-VE',
            // French variants
            'fr', 'fr-FR', 'fr-BE', 'fr-CA', 'fr-CH',
            // German variants
            'de', 'de-DE', 'de-AT', 'de-CH',
            // Italian variants
            'it', 'it-IT', 'it-CH',
            // Portuguese variants
            'pt', 'pt-BR', 'pt-PT',
            // Russian
            'ru', 'ru-RU',
            // Chinese variants
            'zh', 'zh-CN', 'zh-HK', 'zh-TW',
            // Japanese
            'ja', 'ja-JP',
            // Korean
            'ko', 'ko-KR',
            // Dutch variants
            'nl', 'nl-NL', 'nl-BE',
            // Polish
            'pl', 'pl-PL',
            // Swedish
            'sv', 'sv-SE',
            // Turkish
            'tr', 'tr-TR',
            // Arabic variants
            'ar', 'ar-AE', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-KW', 'ar-LB',
            'ar-MA', 'ar-OM', 'ar-QA', 'ar-SA', 'ar-TN',
            // Other European languages
            'cs', 'cs-CZ', 'da', 'da-DK', 'fi', 'fi-FI', 'el', 'el-GR', 'hu', 'hu-HU',
            'no', 'nb-NO', 'ro', 'ro-RO', 'sk', 'sk-SK', 'sl', 'sl-SI', 'bg', 'bg-BG',
            'hr', 'hr-HR', 'lt-LT', 'lv-LV', 'sr', 'sr-RS',
            // Asian languages
            'hi', 'hi-IN', 'id', 'id-ID', 'ms', 'ms-MY', 'fil', 'fil-PH', 'vi', 'vi-VN',
            'th', 'th-TH', 'bn', 'bn-IN', 'ta', 'ta-IN', 'te-IN', 'ml-IN', 'kn-IN',
            'mr-IN', 'gu-IN', 'pa-IN',
            // Middle Eastern languages
            'fa', 'fa-IR', 'he', 'he-IL', 'ur', 'ur-PK',
            // Other languages
            'uk', 'uk-UA', 'si-LK', 'km-KH', 'lo-LA', 'my-MM', 'ne-NP', 'ka-GE',
            'am-ET', 'sw-KE', 'zu-ZA'
        ],

        // Azure Speech Service - Most comprehensive language support
        azure: [
            // English variants
            'en', 'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ', 'en-PH', 'en-SG', 'en-ZA',
            // Spanish variants
            'es', 'es-ES', 'es-AR', 'es-BO', 'es-CL', 'es-CO', 'es-CR', 'es-DO', 'es-EC', 'es-GT', 'es-HN',
            'es-MX', 'es-NI', 'es-PA', 'es-PE', 'es-PR', 'es-PY', 'es-SV', 'es-US', 'es-UY', 'es-VE',
            // French variants
            'fr', 'fr-FR', 'fr-BE', 'fr-CA', 'fr-CH',
            // German variants
            'de', 'de-DE', 'de-AT', 'de-CH',
            // Italian variants
            'it', 'it-IT', 'it-CH',
            // Portuguese variants
            'pt', 'pt-BR', 'pt-PT',
            // Russian
            'ru', 'ru-RU',
            // Chinese variants
            'zh', 'zh-CN', 'zh-HK', 'zh-TW',
            // Japanese
            'ja', 'ja-JP',
            // Korean
            'ko', 'ko-KR',
            // Dutch variants
            'nl', 'nl-NL', 'nl-BE',
            // Polish
            'pl', 'pl-PL',
            // Swedish
            'sv', 'sv-SE',
            // Turkish
            'tr', 'tr-TR',
            // Arabic variants
            'ar', 'ar-AE', 'ar-BH', 'ar-DZ', 'ar-EG', 'ar-IQ', 'ar-JO', 'ar-KW', 'ar-LB',
            'ar-MA', 'ar-OM', 'ar-QA', 'ar-SA', 'ar-TN',
            // Other European languages
            'cs', 'cs-CZ', 'da', 'da-DK', 'fi', 'fi-FI', 'el', 'el-GR', 'hu', 'hu-HU',
            'no', 'nb-NO', 'ro', 'ro-RO', 'sk', 'sk-SK', 'sl', 'sl-SI', 'bg', 'bg-BG',
            'hr', 'hr-HR', 'lt-LT', 'lv-LV', 'sr', 'sr-RS', 'et-EE', 'gl-ES', 'is-IS',
            // Asian languages
            'hi', 'hi-IN', 'id', 'id-ID', 'ms', 'ms-MY', 'fil', 'fil-PH', 'vi', 'vi-VN',
            'th', 'th-TH', 'bn', 'bn-IN', 'ta', 'ta-IN', 'te-IN', 'ml-IN', 'kn-IN',
            'mr-IN', 'gu-IN', 'pa-IN', 'jv-ID',
            // Middle Eastern languages
            'fa', 'fa-IR', 'he', 'he-IL', 'ur', 'ur-PK',
            // Other languages
            'uk', 'uk-UA', 'si-LK', 'km-KH', 'lo-LA', 'my-MM', 'ne-NP', 'ka-GE',
            'am-ET', 'sw-KE', 'zu-ZA', 'af-ZA', 'sq-AL', 'hy-AM', 'az-AZ', 'eu-ES',
            'be-BY', 'bs-BA', 'ca-ES', 'cy-GB', 'kk-KZ', 'ky-KG', 'lb-LU', 'mk-MK',
            'mt-MT', 'mn-MN', 'ps-AF', 'so-SO', 'tg-TJ', 'tk-TM', 'tt-RU', 'uz-UZ', 'yi-YD'
        ],

        // AssemblyAI - Focused on high-quality transcription for major languages
        assembly: [
            // English variants
            'en', 'en-US', 'en-GB', 'en-AU', 'en-CA', 'en-IN', 'en-IE', 'en-NZ',
            // Spanish variants
            'es', 'es-ES', 'es-MX', 'es-AR',
            // French variants
            'fr', 'fr-FR', 'fr-CA',
            // German variants
            'de', 'de-DE', 'de-AT', 'de-CH',
            // Italian variants
            'it', 'it-IT',
            // Portuguese variants
            'pt', 'pt-BR', 'pt-PT',
            // Dutch variants
            'nl', 'nl-NL', 'nl-BE',
            // Other major languages
            'hi', 'hi-IN', 'ja', 'ja-JP', 'zh', 'zh-CN', 'zh-TW', 'fi', 'fi-FI',
            'ko', 'ko-KR', 'pl', 'pl-PL', 'ru', 'ru-RU', 'tr', 'tr-TR', 'uk', 'uk-UA',
            'vi', 'vi-VN', 'ar', 'ar-SA', 'ar-EG', 'cs', 'cs-CZ', 'da', 'da-DK',
            'el', 'el-GR', 'he', 'he-IL', 'hu', 'hu-HU', 'id', 'id-ID', 'no', 'nb-NO',
            'ro', 'ro-RO', 'sv', 'sv-SE', 'th', 'th-TH', 'bg', 'bg-BG', 'hr', 'hr-HR',
            'sk', 'sk-SK', 'sl', 'sl-SI', 'sr', 'sr-RS', 'fa', 'fa-IR', 'ms', 'ms-MY',
            'fil', 'fil-PH', 'ta', 'ta-IN', 'bn', 'bn-IN', 'ur', 'ur-PK'
        ]
    };

    // Language display names
    const languageDisplayNames = {
        // English variants
        'en': 'English',
        'en-US': 'English (United States)',
        'en-GB': 'English (United Kingdom)',
        'en-AU': 'English (Australia)',
        'en-CA': 'English (Canada)',
        'en-IN': 'English (India)',
        'en-IE': 'English (Ireland)',
        'en-NZ': 'English (New Zealand)',
        'en-PH': 'English (Philippines)',
        'en-SG': 'English (Singapore)',
        'en-ZA': 'English (South Africa)',

        // Spanish variants
        'es': 'Spanish',
        'es-ES': 'Spanish (Spain)',
        'es-AR': 'Spanish (Argentina)',
        'es-BO': 'Spanish (Bolivia)',
        'es-CL': 'Spanish (Chile)',
        'es-CO': 'Spanish (Colombia)',
        'es-CR': 'Spanish (Costa Rica)',
        'es-DO': 'Spanish (Dominican Republic)',
        'es-EC': 'Spanish (Ecuador)',
        'es-GT': 'Spanish (Guatemala)',
        'es-HN': 'Spanish (Honduras)',
        'es-MX': 'Spanish (Mexico)',
        'es-NI': 'Spanish (Nicaragua)',
        'es-PA': 'Spanish (Panama)',
        'es-PE': 'Spanish (Peru)',
        'es-PR': 'Spanish (Puerto Rico)',
        'es-PY': 'Spanish (Paraguay)',
        'es-SV': 'Spanish (El Salvador)',
        'es-US': 'Spanish (United States)',
        'es-UY': 'Spanish (Uruguay)',
        'es-VE': 'Spanish (Venezuela)',

        // French variants
        'fr': 'French',
        'fr-FR': 'French (France)',
        'fr-BE': 'French (Belgium)',
        'fr-CA': 'French (Canada)',
        'fr-CH': 'French (Switzerland)',

        // German variants
        'de': 'German',
        'de-DE': 'German (Germany)',
        'de-AT': 'German (Austria)',
        'de-CH': 'German (Switzerland)',

        // Italian variants
        'it': 'Italian',
        'it-IT': 'Italian (Italy)',
        'it-CH': 'Italian (Switzerland)',

        // Portuguese variants
        'pt': 'Portuguese',
        'pt-BR': 'Portuguese (Brazil)',
        'pt-PT': 'Portuguese (Portugal)',

        // Russian
        'ru': 'Russian',
        'ru-RU': 'Russian (Russia)',

        // Chinese variants
        'zh': 'Chinese',
        'zh-CN': 'Chinese (Simplified, China)',
        'zh-HK': 'Chinese (Traditional, Hong Kong)',
        'zh-TW': 'Chinese (Traditional, Taiwan)',

        // Japanese
        'ja': 'Japanese',
        'ja-JP': 'Japanese (Japan)',

        // Korean
        'ko': 'Korean',
        'ko-KR': 'Korean (South Korea)',

        // Dutch variants
        'nl': 'Dutch',
        'nl-NL': 'Dutch (Netherlands)',
        'nl-BE': 'Dutch (Belgium)',

        // Polish
        'pl': 'Polish',
        'pl-PL': 'Polish (Poland)',

        // Swedish
        'sv': 'Swedish',
        'sv-SE': 'Swedish (Sweden)',

        // Turkish
        'tr': 'Turkish',
        'tr-TR': 'Turkish (Turkey)',

        // Arabic variants
        'ar': 'Arabic',
        'ar-AE': 'Arabic (United Arab Emirates)',
        'ar-BH': 'Arabic (Bahrain)',
        'ar-DZ': 'Arabic (Algeria)',
        'ar-EG': 'Arabic (Egypt)',
        'ar-IQ': 'Arabic (Iraq)',
        'ar-JO': 'Arabic (Jordan)',
        'ar-KW': 'Arabic (Kuwait)',
        'ar-LB': 'Arabic (Lebanon)',
        'ar-MA': 'Arabic (Morocco)',
        'ar-OM': 'Arabic (Oman)',
        'ar-QA': 'Arabic (Qatar)',
        'ar-SA': 'Arabic (Saudi Arabia)',
        'ar-TN': 'Arabic (Tunisia)',

        // Czech
        'cs': 'Czech',
        'cs-CZ': 'Czech (Czech Republic)',

        // Danish
        'da': 'Danish',
        'da-DK': 'Danish (Denmark)',

        // Finnish
        'fi': 'Finnish',
        'fi-FI': 'Finnish (Finland)',

        // Hindi
        'hi': 'Hindi',
        'hi-IN': 'Hindi (India)',

        // Hungarian
        'hu': 'Hungarian',
        'hu-HU': 'Hungarian (Hungary)',

        // Indonesian
        'id': 'Indonesian',
        'id-ID': 'Indonesian (Indonesia)',

        // Norwegian
        'no': 'Norwegian',
        'nb-NO': 'Norwegian (Norway)',

        // Romanian
        'ro': 'Romanian',
        'ro-RO': 'Romanian (Romania)',

        // Slovak
        'sk': 'Slovak',
        'sk-SK': 'Slovak (Slovakia)',

        // Thai
        'th': 'Thai',
        'th-TH': 'Thai (Thailand)',

        // Ukrainian
        'uk': 'Ukrainian',
        'uk-UA': 'Ukrainian (Ukraine)',

        // Vietnamese
        'vi': 'Vietnamese',
        'vi-VN': 'Vietnamese (Vietnam)',

        // Greek
        'el': 'Greek',
        'el-GR': 'Greek (Greece)',

        // Bulgarian
        'bg': 'Bulgarian',
        'bg-BG': 'Bulgarian (Bulgaria)',

        // Croatian
        'hr': 'Croatian',
        'hr-HR': 'Croatian (Croatia)',

        // Lithuanian
        'lt-LT': 'Lithuanian (Lithuania)',

        // Latvian
        'lv-LV': 'Latvian (Latvia)',

        // Slovenian
        'sl': 'Slovenian',
        'sl-SI': 'Slovenian (Slovenia)',

        // Serbian
        'sr': 'Serbian',
        'sr-RS': 'Serbian (Serbia)',

        // Malay
        'ms': 'Malay',
        'ms-MY': 'Malay (Malaysia)',

        // Filipino
        'fil': 'Filipino',
        'fil-PH': 'Filipino (Philippines)',

        // Persian
        'fa': 'Persian',
        'fa-IR': 'Persian (Iran)',

        // Hebrew
        'he': 'Hebrew',
        'he-IL': 'Hebrew (Israel)',

        // Urdu
        'ur': 'Urdu',
        'ur-PK': 'Urdu (Pakistan)',

        // Bengali
        'bn': 'Bengali',
        'bn-IN': 'Bengali (India)',

        // Tamil
        'ta': 'Tamil',
        'ta-IN': 'Tamil (India)',

        // Telugu
        'te-IN': 'Telugu (India)',

        // Malayalam
        'ml-IN': 'Malayalam (India)',

        // Kannada
        'kn-IN': 'Kannada (India)',

        // Marathi
        'mr-IN': 'Marathi (India)',

        // Gujarati
        'gu-IN': 'Gujarati (India)',

        // Punjabi
        'pa-IN': 'Punjabi (India)',

        // Sinhala
        'si-LK': 'Sinhala (Sri Lanka)',

        // Khmer
        'km-KH': 'Khmer (Cambodia)',

        // Lao
        'lo-LA': 'Lao (Laos)',

        // Burmese
        'my-MM': 'Burmese (Myanmar)',

        // Nepali
        'ne-NP': 'Nepali (Nepal)',

        // Georgian
        'ka-GE': 'Georgian (Georgia)',

        // Amharic
        'am-ET': 'Amharic (Ethiopia)',

        // Swahili
        'sw-KE': 'Swahili (Kenya)',

        // Zulu
        'zu-ZA': 'Zulu (South Africa)',

        // Additional languages for Azure
        'af-ZA': 'Afrikaans (South Africa)',
        'sq-AL': 'Albanian (Albania)',
        'hy-AM': 'Armenian (Armenia)',
        'az-AZ': 'Azerbaijani (Azerbaijan)',
        'eu-ES': 'Basque (Spain)',
        'be-BY': 'Belarusian (Belarus)',
        'bs-BA': 'Bosnian (Bosnia and Herzegovina)',
        'ca-ES': 'Catalan (Spain)',
        'cy-GB': 'Welsh (United Kingdom)',
        'et-EE': 'Estonian (Estonia)',
        'gl-ES': 'Galician (Spain)',
        'is-IS': 'Icelandic (Iceland)',
        'jv-ID': 'Javanese (Indonesia)',
        'kk-KZ': 'Kazakh (Kazakhstan)',
        'ky-KG': 'Kyrgyz (Kyrgyzstan)',
        'lb-LU': 'Luxembourgish (Luxembourg)',
        'mk-MK': 'Macedonian (North Macedonia)',
        'mt-MT': 'Maltese (Malta)',
        'mn-MN': 'Mongolian (Mongolia)',
        'ps-AF': 'Pashto (Afghanistan)',
        'so-SO': 'Somali (Somalia)',
        'tg-TJ': 'Tajik (Tajikistan)',
        'tk-TM': 'Turkmen (Turkmenistan)',
        'tt-RU': 'Tatar (Russia)',
        'uz-UZ': 'Uzbek (Uzbekistan)',
        'yi-YD': 'Yiddish'
    };

    // Load saved settings
    loadSettings();

    // Additional UI Enhancement Functions
    // Provider info display management
    const providerInfos = {
        browser: document.getElementById('provider-info-browser'),
        google: document.getElementById('provider-info-google'),
        azure: document.getElementById('provider-info-azure'),
        assembly: document.getElementById('provider-info-assembly')
    };

    /**
     * Update provider info display based on selected provider
     * @param {string} provider - The selected provider
     */
    function updateProviderInfo(provider) {
        Object.keys(providerInfos).forEach(key => {
            if (providerInfos[key]) {
                providerInfos[key].style.display = key === provider ? 'flex' : 'none';
            }
        });

        // Show/hide API key input
        if (provider === 'browser') {
            apiKeyContainer.classList.remove('visible');
            apiKeyContainer.classList.add('hidden');
        } else {
            apiKeyContainer.classList.remove('hidden');
            apiKeyContainer.classList.add('visible');
        }
    }

    /**
     * Toggle password visibility for API key input
     */
    function setupPasswordToggle() {
        const togglePasswordBtn = document.getElementById('toggle-password');
        if (togglePasswordBtn && apiKeyInput) {
            togglePasswordBtn.addEventListener('click', function() {
                const type = apiKeyInput.getAttribute('type') === 'password' ? 'text' : 'password';
                apiKeyInput.setAttribute('type', type);

                // Update icon text (since we removed Font Awesome)
                this.textContent = type === 'text' ? '🙈' : '👁️';
            });
        }
    }

    /**
     * Setup slider value display updates
     */
    function setupSliders() {
        // Silence threshold slider
        const silenceSlider = document.getElementById('voice-silence-threshold');
        const silenceValue = document.getElementById('silence-value');

        if (silenceSlider && silenceValue) {
            silenceSlider.addEventListener('input', function() {
                silenceValue.textContent = this.value + ' seconds';
            });
        }

        // Audio sensitivity slider
        const audioSensitivitySlider = document.getElementById('voice-audio-sensitivity');
        const audioSensitivityValue = document.getElementById('audio-sensitivity-value');

        if (audioSensitivitySlider && audioSensitivityValue) {
            audioSensitivitySlider.addEventListener('input', function() {
                audioSensitivityValue.textContent = this.value + '%';
            });
        }
    }

    /**
     * Show status message with animation
     * @param {string} message - The message to display
     * @param {string} type - The type of message (success, error, info)
     */
    function showStatus(message, type = 'success') {
        const statusMessage = document.getElementById('status-message');
        const statusText = document.getElementById('status-text');

        if (statusMessage && statusText) {
            statusText.textContent = message;
            statusMessage.className = 'status-message ' + type;
            statusMessage.classList.add('visible');

            setTimeout(() => {
                statusMessage.classList.remove('visible');
            }, 3000);
        }
    }

    /**
     * Reset all settings to default values
     */
    function resetToDefaults() {
        if (confirm('Reset all voice settings to default values?')) {
            // Reset form to defaults
            if (providerSelect) providerSelect.value = 'browser';
            if (languageSelect) languageSelect.value = 'en-US';
            if (punctuationCheckbox) punctuationCheckbox.checked = true;
            if (capitalizationCheckbox) capitalizationCheckbox.checked = true;
            if (profanityFilterCheckbox) profanityFilterCheckbox.checked = false;
            if (interimResultsCheckbox) interimResultsCheckbox.checked = true;
            if (autoDetectCheckbox) autoDetectCheckbox.checked = false;
            if (backgroundNoiseReductionCheckbox) backgroundNoiseReductionCheckbox.checked = false;
            if (speakerDiarizationCheckbox) speakerDiarizationCheckbox.checked = false;
            if (qualitySelect) qualitySelect.value = 'standard';

            // Reset sliders
            const silenceSlider = document.getElementById('voice-silence-threshold');
            const silenceValue = document.getElementById('silence-value');
            if (silenceSlider) {
                silenceSlider.value = 15;
                if (silenceValue) silenceValue.textContent = '15 seconds';
            }

            if (apiKeyInput) apiKeyInput.value = '';

            // Reset audio processing settings
            if (audioMonitoringCheckbox) audioMonitoringCheckbox.checked = true;
            if (adaptiveThresholdsCheckbox) adaptiveThresholdsCheckbox.checked = true;
            if (enhancedConstraintsCheckbox) enhancedConstraintsCheckbox.checked = true;

            const audioSensitivitySlider = document.getElementById('voice-audio-sensitivity');
            const audioSensitivityValue = document.getElementById('audio-sensitivity-value');
            if (audioSensitivitySlider) {
                audioSensitivitySlider.value = 50;
                if (audioSensitivityValue) audioSensitivityValue.textContent = '50%';
            }

            if (noiseSuppressionSelect) noiseSuppressionSelect.value = 'medium';

            // Update UI
            updateProviderInfo('browser');
            showStatus('Settings reset to defaults', 'info');
        }
    }

    /**
     * Enhanced save function with visual feedback
     */
    function enhancedSaveSettings() {
        const saveButton = document.getElementById('save-voice-settings');
        if (!saveButton) return;

        const originalText = saveButton.innerHTML;

        // Show loading state
        saveButton.innerHTML = '⏳ Saving...';
        saveButton.disabled = true;

        // Call the original save function if it exists
        let result = true;
        if (typeof window.saveSettings === 'function') {
            result = window.saveSettings();
        }

        // Simulate a delay for visual feedback
        setTimeout(() => {
            saveButton.innerHTML = originalText;
            saveButton.disabled = false;
            showStatus('Settings saved successfully!');
        }, 800);

        return result;
    }

    // Initialize UI enhancements
    setupPasswordToggle();
    setupSliders();

    // Initialize provider info display
    if (providerSelect) {
        updateProviderInfo(providerSelect.value);
    }

    // Setup reset button
    if (resetButton) {
        resetButton.addEventListener('click', resetToDefaults);
    }

    // Setup enhanced save button
    if (saveButton) {
        saveButton.addEventListener('click', enhancedSaveSettings);
    }

    // Event Listeners
    providerSelect.addEventListener('change', function() {
        updateProviderUI(this.value);
        updateProviderInfo(this.value);
    });

    silenceThresholdInput.addEventListener('input', function() {
        document.getElementById('silence-value').textContent = this.value + ' seconds';
    });

    // Audio sensitivity slider
    audioSensitivityInput.addEventListener('input', function() {
        document.getElementById('audio-sensitivity-value').textContent = this.value + '%';
    });

    // Functions
    function loadSettings() {
        chrome.storage.local.get(['voiceApiPreferences', 'voiceApiKeys'], function(result) {
            if (result.voiceApiPreferences) {
                const prefs = result.voiceApiPreferences;

                // Set provider
                if (prefs.provider) {
                    providerSelect.value = prefs.provider;
                    updateProviderUI(prefs.provider);
                }

                // Set language
                if (prefs.language && languageSelect.querySelector(`option[value="${prefs.language}"]`)) {
                    languageSelect.value = prefs.language;
                }

                // Set checkboxes
                punctuationCheckbox.checked = prefs.enablePunctuation !== false;
                capitalizationCheckbox.checked = prefs.enableAutomaticCapitalization !== false;
                profanityFilterCheckbox.checked = prefs.enableProfanityFilter === true;
                interimResultsCheckbox.checked = prefs.enableInterimResults !== false;

                // Set silence threshold
                if (prefs.silenceThreshold) {
                    const seconds = Math.floor(prefs.silenceThreshold / 1000);
                    silenceThresholdInput.value = seconds;
                    document.getElementById('silence-value').textContent = seconds + ' seconds';
                }

                // Set enhanced options
                autoDetectCheckbox.checked = prefs.autoDetectLanguage === true;
                backgroundNoiseReductionCheckbox.checked = prefs.backgroundNoiseReduction === true;
                speakerDiarizationCheckbox.checked = prefs.speakerDiarization === true;

                if (prefs.quality) {
                    qualitySelect.value = prefs.quality;
                }

                // Load audio processing settings
                if (prefs.audioMonitoring !== undefined) {
                    audioMonitoringCheckbox.checked = prefs.audioMonitoring;
                }
                if (prefs.adaptiveThresholds !== undefined) {
                    adaptiveThresholdsCheckbox.checked = prefs.adaptiveThresholds;
                }
                if (prefs.enhancedConstraints !== undefined) {
                    enhancedConstraintsCheckbox.checked = prefs.enhancedConstraints;
                }
                if (prefs.audioSensitivity !== undefined) {
                    audioSensitivityInput.value = prefs.audioSensitivity;
                    document.getElementById('audio-sensitivity-value').textContent = prefs.audioSensitivity + '%';
                }
                if (prefs.noiseSuppression) {
                    noiseSuppressionSelect.value = prefs.noiseSuppression;
                }
            }

            // Try to load encrypted API keys
            chrome.storage.local.get(['encryptedVoiceApiKeys'], function(encryptedResult) {
                if (encryptedResult.encryptedVoiceApiKeys && window.StashyEncryption) {
                    // Show that encrypted keys are available
                    const provider = providerSelect.value;
                    if (provider !== 'browser') {
                        apiKeyInput.placeholder = 'Encrypted API key configured';
                        apiKeyInput.style.backgroundColor = '#e8f5e8';
                    }
                } else {
                    // No encrypted keys found
                    const provider = providerSelect.value;
                    if (provider !== 'browser') {
                        apiKeyInput.placeholder = 'Enter your API key (will be encrypted)';
                        apiKeyInput.style.backgroundColor = '';
                    }
                }
            });
        });
    }

    function updateProviderUI(provider) {
        // Show/hide API key input
        if (provider === 'browser') {
            apiKeyContainer.classList.remove('visible');
            apiKeyContainer.classList.add('hidden');
        } else {
            apiKeyContainer.classList.remove('hidden');
            apiKeyContainer.classList.add('visible');
        }

        // Update provider info sections
        document.getElementById('provider-info-browser').style.display = provider === 'browser' ? 'flex' : 'none';
        document.getElementById('provider-info-google').style.display = provider === 'google' ? 'flex' : 'none';
        document.getElementById('provider-info-azure').style.display = provider === 'azure' ? 'flex' : 'none';
        document.getElementById('provider-info-assembly').style.display = provider === 'assembly' ? 'flex' : 'none';

        // Update language options
        updateLanguageOptions(provider);

        // Show message about language options
        let message = '';
        if (provider === 'browser') {
            message = 'Browser API supports a limited set of languages';
        } else if (provider === 'google') {
            message = 'Google Speech API supports a wide range of languages';
        } else if (provider === 'azure') {
            message = 'Azure Speech Service supports the most comprehensive language set';
        } else if (provider === 'assembly') {
            message = 'AssemblyAI focuses on high-quality transcription for major languages';
        }

        if (message) {
            showStatus(message, 'info');
        }

        // Update API key label and placeholder
        const apiKeyLabel = apiKeyContainer.querySelector('label');
        if (apiKeyLabel) {
            if (provider === 'google') {
                apiKeyLabel.textContent = 'Google API Key:';
                apiKeyInput.placeholder = 'Enter your Google Speech-to-Text API key';
            } else if (provider === 'azure') {
                apiKeyLabel.textContent = 'Azure API Key:';
                apiKeyInput.placeholder = 'Enter your Azure Speech Service key';
            } else if (provider === 'assembly') {
                apiKeyLabel.textContent = 'AssemblyAI API Key:';
                apiKeyInput.placeholder = 'Enter your AssemblyAI API key';
            }
        }

        // Check for encrypted API keys
        chrome.storage.local.get(['encryptedVoiceApiKeys'], function(result) {
            if (result.encryptedVoiceApiKeys && window.StashyEncryption) {
                // Show that encrypted keys are available
                if (provider !== 'browser') {
                    apiKeyInput.placeholder = 'Encrypted API key configured';
                    apiKeyInput.style.backgroundColor = '#e8f5e8';
                    apiKeyInput.value = '';
                }
            } else {
                // No encrypted keys found
                if (provider !== 'browser') {
                    apiKeyInput.placeholder = 'Enter your API key (will be encrypted)';
                    apiKeyInput.style.backgroundColor = '';
                    apiKeyInput.value = '';
                }
            }
        });

        // Enable/disable premium features
        const isPremium = provider !== 'browser';
        togglePremiumFeatures(isPremium);

        // Update auto-detect language availability
        updateAutoDetectAvailability(provider);
    }

    // Function to update auto-detect language availability based on provider
    function updateAutoDetectAvailability(provider) {
        const autoDetectContainer = document.querySelector('.checkbox-group:has(#voice-auto-detect)');
        const autoDetectLabel = document.querySelector('label[for="voice-auto-detect"]');

        if (provider === 'browser') {
            // Browser API has limited language detection
            autoDetectCheckbox.disabled = true;
            if (autoDetectLabel) {
                autoDetectLabel.title = 'Auto-detection is limited with Browser API';
            }
            if (autoDetectContainer) {
                autoDetectContainer.classList.add('feature-limited');
            }
        } else if (provider === 'google' || provider === 'azure') {
            // Google and Azure have good language detection
            autoDetectCheckbox.disabled = false;
            if (autoDetectLabel) {
                autoDetectLabel.title = 'Automatically detect the spoken language';
            }
            if (autoDetectContainer) {
                autoDetectContainer.classList.remove('feature-limited');
            }
        } else if (provider === 'assembly') {
            // AssemblyAI has excellent language detection
            autoDetectCheckbox.disabled = false;
            if (autoDetectLabel) {
                autoDetectLabel.title = 'AssemblyAI provides advanced language detection';
            }
            if (autoDetectContainer) {
                autoDetectContainer.classList.remove('feature-limited');
            }
        }
    }

    function togglePremiumFeatures(enabled) {
        // Background noise reduction
        backgroundNoiseReductionCheckbox.disabled = !enabled;
        updateFeatureAvailability(
            backgroundNoiseReductionCheckbox,
            'Background noise reduction is only available with premium providers',
            enabled
        );

        // Speaker identification (diarization)
        speakerDiarizationCheckbox.disabled = !enabled;
        updateFeatureAvailability(
            speakerDiarizationCheckbox,
            'Speaker identification is only available with premium providers',
            enabled
        );

        // Quality options
        const enhancedOption = qualitySelect.querySelector('option[value="enhanced"]');
        const highOption = qualitySelect.querySelector('option[value="high"]');
        const ultraOption = qualitySelect.querySelector('option[value="ultra"]');

        // Add ultra quality option if it doesn't exist
        if (!ultraOption && enabled) {
            const option = document.createElement('option');
            option.value = 'ultra';
            option.textContent = 'Ultra (Highest accuracy)';
            qualitySelect.appendChild(option);
        } else if (ultraOption && !enabled) {
            ultraOption.disabled = true;
        }

        if (enhancedOption) enhancedOption.disabled = !enabled;
        if (highOption) highOption.disabled = !enabled;
        if (ultraOption) ultraOption.disabled = !enabled;

        // If disabled and selected, switch to standard
        if (!enabled && ['enhanced', 'high', 'ultra'].includes(qualitySelect.value)) {
            qualitySelect.value = 'standard';
        }

        // Update quality select tooltip based on provider
        updateQualitySelectTooltip(enabled);
    }

    // Helper function to update feature availability UI
    function updateFeatureAvailability(checkbox, disabledMessage, enabled) {
        const container = checkbox.closest('.checkbox-group');
        const label = container?.querySelector(`label[for="${checkbox.id}"]`);

        if (container) {
            if (enabled) {
                container.classList.remove('feature-disabled');
                container.title = '';
            } else {
                container.classList.add('feature-disabled');
                container.title = disabledMessage;
            }
        }

        if (label) {
            if (!enabled) {
                label.title = disabledMessage;
            } else {
                label.title = '';
            }
        }
    }

    // Update quality select tooltip
    function updateQualitySelectTooltip(isPremium) {
        const qualityContainer = qualitySelect.closest('.form-group');
        const qualityLabel = qualityContainer?.querySelector('label');

        if (qualityContainer) {
            if (isPremium) {
                qualityContainer.title = 'Higher quality settings provide better accuracy but may use more processing power';
                qualityContainer.classList.remove('feature-limited');
            } else {
                qualityContainer.title = 'Enhanced quality options require a premium provider';
                qualityContainer.classList.add('feature-limited');
            }
        }

        if (qualityLabel) {
            if (isPremium) {
                qualityLabel.title = 'Select the desired transcription quality level';
            } else {
                qualityLabel.title = 'Enhanced quality options require a premium provider';
            }
        }
    }

    function updateLanguageOptions(provider) {
        // Save current selection if possible
        const currentLang = languageSelect.value;

        // Clear existing options
        languageSelect.innerHTML = '';

        // Get languages for the selected provider
        const languages = providerLanguages[provider] || providerLanguages.browser;

        // Create a list of languages with display names
        const languagesWithNames = [];

        languages.forEach(lang => {
            // If we don't have a display name, create one from the language code
            if (!languageDisplayNames[lang]) {
                // Create a display name from the language code
                let displayName = lang;

                // If it's a language code with region (e.g., en-US)
                if (lang.includes('-')) {
                    const [langCode, regionCode] = lang.split('-');

                    // Try to get the base language name
                    const baseLangName = languageDisplayNames[langCode] || getLanguageNameFromCode(langCode);

                    // Format as "Language (Region)"
                    displayName = `${baseLangName} (${regionCode})`;
                } else {
                    // It's just a language code without region
                    displayName = getLanguageNameFromCode(lang);
                }

                // Add to our display names
                languageDisplayNames[lang] = displayName;
            }

            // Add to our list with the display name
            languagesWithNames.push({
                code: lang,
                displayName: languageDisplayNames[lang]
            });
        });

        // Sort languages alphabetically by display name
        languagesWithNames.sort((a, b) => a.displayName.localeCompare(b.displayName));

        // Group languages by their base name (e.g., "English" for "English (United States)")
        const languageGroups = {};

        languagesWithNames.forEach(lang => {
            // Extract the base language name (before any parentheses)
            const baseName = lang.displayName.split(' (')[0];

            // Initialize the group if it doesn't exist
            if (!languageGroups[baseName]) {
                languageGroups[baseName] = [];
            }

            // Add the language to its group
            languageGroups[baseName].push(lang);
        });

        // Sort the base language names alphabetically
        const sortedBaseNames = Object.keys(languageGroups).sort();

        // Create option groups for each base language
        sortedBaseNames.forEach(baseName => {
            const languagesInGroup = languageGroups[baseName];

            // If there's only one language in the group, add it directly
            if (languagesInGroup.length === 1) {
                const lang = languagesInGroup[0];
                const option = document.createElement('option');
                option.value = lang.code;
                option.textContent = lang.displayName;
                languageSelect.appendChild(option);
                return;
            }

            // Create an optgroup for languages with variants
            const optgroup = document.createElement('optgroup');
            optgroup.label = baseName;

            // Sort variants - put the base language first, then sort others alphabetically
            const sortedVariants = [...languagesInGroup].sort((a, b) => {
                // If one is the base language (no parentheses), it goes first
                const aIsBase = !a.displayName.includes('(');
                const bIsBase = !b.displayName.includes('(');

                if (aIsBase && !bIsBase) return -1;
                if (!aIsBase && bIsBase) return 1;

                // Otherwise sort alphabetically
                return a.displayName.localeCompare(b.displayName);
            });

            // Add each variant to the group
            sortedVariants.forEach(lang => {
                const option = document.createElement('option');
                option.value = lang.code;

                // For the base language (no parentheses), show the full name
                if (!lang.displayName.includes('(')) {
                    option.textContent = lang.displayName;
                } else {
                    // For variants, show the full name to avoid confusion
                    option.textContent = lang.displayName;
                }

                optgroup.appendChild(option);
            });

            languageSelect.appendChild(optgroup);
        });

        // Restore selection if it exists in new options
        if (languageSelect.querySelector(`option[value="${currentLang}"]`)) {
            languageSelect.value = currentLang;
        } else {
            // Default to the first option if the previous selection is not available
            languageSelect.selectedIndex = 0;
        }
    }

    // Helper function to get a language name from a language code
    function getLanguageNameFromCode(code) {
        // Map of ISO 639-1/639-2 language codes to English language names
        const languageCodeMap = {
            'af': 'Afrikaans', 'sq': 'Albanian', 'am': 'Amharic', 'ar': 'Arabic',
            'hy': 'Armenian', 'az': 'Azerbaijani', 'eu': 'Basque', 'be': 'Belarusian',
            'bn': 'Bengali', 'bs': 'Bosnian', 'bg': 'Bulgarian', 'ca': 'Catalan',
            'ceb': 'Cebuano', 'zh': 'Chinese', 'co': 'Corsican', 'hr': 'Croatian',
            'cs': 'Czech', 'da': 'Danish', 'nl': 'Dutch', 'en': 'English',
            'eo': 'Esperanto', 'et': 'Estonian', 'fi': 'Finnish', 'fr': 'French',
            'fy': 'Frisian', 'gl': 'Galician', 'ka': 'Georgian', 'de': 'German',
            'el': 'Greek', 'gu': 'Gujarati', 'ht': 'Haitian Creole', 'ha': 'Hausa',
            'haw': 'Hawaiian', 'he': 'Hebrew', 'hi': 'Hindi', 'hmn': 'Hmong',
            'hu': 'Hungarian', 'is': 'Icelandic', 'ig': 'Igbo', 'id': 'Indonesian',
            'ga': 'Irish', 'it': 'Italian', 'ja': 'Japanese', 'jv': 'Javanese',
            'kn': 'Kannada', 'kk': 'Kazakh', 'km': 'Khmer', 'rw': 'Kinyarwanda',
            'ko': 'Korean', 'ku': 'Kurdish', 'ky': 'Kyrgyz', 'lo': 'Lao',
            'la': 'Latin', 'lv': 'Latvian', 'lt': 'Lithuanian', 'lb': 'Luxembourgish',
            'mk': 'Macedonian', 'mg': 'Malagasy', 'ms': 'Malay', 'ml': 'Malayalam',
            'mt': 'Maltese', 'mi': 'Maori', 'mr': 'Marathi', 'mn': 'Mongolian',
            'my': 'Myanmar (Burmese)', 'ne': 'Nepali', 'no': 'Norwegian', 'ny': 'Nyanja (Chichewa)',
            'or': 'Odia (Oriya)', 'ps': 'Pashto', 'fa': 'Persian', 'pl': 'Polish',
            'pt': 'Portuguese', 'pa': 'Punjabi', 'ro': 'Romanian', 'ru': 'Russian',
            'sm': 'Samoan', 'gd': 'Scots Gaelic', 'sr': 'Serbian', 'st': 'Sesotho',
            'sn': 'Shona', 'sd': 'Sindhi', 'si': 'Sinhala (Sinhalese)', 'sk': 'Slovak',
            'sl': 'Slovenian', 'so': 'Somali', 'es': 'Spanish', 'su': 'Sundanese',
            'sw': 'Swahili', 'sv': 'Swedish', 'tl': 'Tagalog (Filipino)', 'tg': 'Tajik',
            'ta': 'Tamil', 'tt': 'Tatar', 'te': 'Telugu', 'th': 'Thai',
            'tr': 'Turkish', 'tk': 'Turkmen', 'uk': 'Ukrainian', 'ur': 'Urdu',
            'ug': 'Uyghur', 'uz': 'Uzbek', 'vi': 'Vietnamese', 'cy': 'Welsh',
            'xh': 'Xhosa', 'yi': 'Yiddish', 'yo': 'Yoruba', 'zu': 'Zulu',
            'fil': 'Filipino', 'he': 'Hebrew', 'nb': 'Norwegian Bokmål'
        };

        return languageCodeMap[code] || code.toUpperCase();
    }

    // Save settings function - exposed globally for the UI script
    window.saveSettings = function() {
        const provider = providerSelect.value;
        const language = languageSelect.value;
        const apiKey = apiKeyInput.value.trim();

        // Validate settings before saving
        if (provider !== 'browser' && apiKey.trim() === '') {
            showStatus('API key is required for this provider', 'error');
            apiKeyInput.focus();
            return false;
        }

        // Prepare preferences object
        const preferences = {
            provider: provider,
            language: language,
            enablePunctuation: punctuationCheckbox.checked,
            enableAutomaticCapitalization: capitalizationCheckbox.checked,
            enableProfanityFilter: profanityFilterCheckbox.checked,
            enableInterimResults: interimResultsCheckbox.checked,
            silenceThreshold: parseInt(silenceThresholdInput.value) * 1000,

            // Enhanced options
            autoDetectLanguage: autoDetectCheckbox.checked && !autoDetectCheckbox.disabled,
            backgroundNoiseReduction: backgroundNoiseReductionCheckbox.checked && !backgroundNoiseReductionCheckbox.disabled,
            speakerDiarization: speakerDiarizationCheckbox.checked && !speakerDiarizationCheckbox.disabled,
            quality: qualitySelect.value,

            // Audio processing settings
            audioMonitoring: audioMonitoringCheckbox.checked,
            adaptiveThresholds: adaptiveThresholdsCheckbox.checked,
            enhancedConstraints: enhancedConstraintsCheckbox.checked,
            audioSensitivity: parseInt(audioSensitivityInput.value),
            noiseSuppression: noiseSuppressionSelect.value,

            // Additional metadata
            lastUpdated: new Date().toISOString(),
            version: '3.0'
        };

        // Add provider-specific settings
        if (provider === 'google') {
            preferences.googleSpecificSettings = {
                enhancedModel: preferences.quality !== 'standard',
                automaticPunctuation: preferences.enablePunctuation,
                profanityFilter: preferences.enableProfanityFilter,
                enableSpeakerDiarization: preferences.speakerDiarization,
                languageCode: language,
                alternativeLanguageCodes: preferences.autoDetectLanguage ? getAlternativeLanguages(language) : []
            };
        } else if (provider === 'azure') {
            preferences.azureSpecificSettings = {
                recognitionMode: preferences.quality === 'ultra' ? 'conversation' : 'interactive',
                profanityOption: preferences.enableProfanityFilter ? 'masked' : 'raw',
                enableAudioLogging: false,
                enableLanguageIdentification: preferences.autoDetectLanguage,
                speakerDiarizationEnabled: preferences.speakerDiarization
            };
        } else if (provider === 'assembly') {
            preferences.assemblySpecificSettings = {
                acousticModel: getAcousticModelForQuality(preferences.quality),
                disfluencies: true,
                punctuate: preferences.enablePunctuation,
                format_text: preferences.enableAutomaticCapitalization,
                language_code: language,
                language_detection: preferences.autoDetectLanguage,
                diarization: preferences.speakerDiarization
            };
        }

        // Prepare API keys object for secure storage
        let apiKeys = {};

        // Only save API key if provided and encryption is available
        if (provider !== 'browser' && apiKey) {
            // Check if encryption is available
            if (!window.StashyEncryption || typeof window.StashyEncryption.encrypt !== 'function') {
                showStatus('Encryption not available. API keys cannot be saved securely.', 'error');
                return;
            }

            // Get existing encrypted keys first
            chrome.storage.local.get(['encryptedVoiceApiKeys'], function(result) {
                // Start with empty object or decrypt existing keys
                if (result.encryptedVoiceApiKeys && window.StashyEncryption) {
                    window.StashyEncryption.init()
                        .then(() => window.StashyEncryption.decrypt(result.encryptedVoiceApiKeys))
                        .then(decryptedKeys => {
                            apiKeys = decryptedKeys || {};
                            updateAndSaveApiKey();
                        })
                        .catch(error => {
                            console.warn('Could not decrypt existing keys, starting fresh:', error);
                            apiKeys = {};
                            updateAndSaveApiKey();
                        });
                } else {
                    apiKeys = {};
                    updateAndSaveApiKey();
                }

                function updateAndSaveApiKey() {
                    // Update key for selected provider
                    if (provider === 'google') {
                        apiKeys.googleSpeechApiKey = apiKey;
                    } else if (provider === 'azure') {
                        apiKeys.azureSpeechApiKey = apiKey;
                    } else if (provider === 'assembly') {
                        apiKeys.assemblyAiApiKey = apiKey;
                    }

                    // Save using the secure encryption method
                    if (typeof saveApiKeys === 'function') {
                        saveApiKeys(apiKeys)
                            .then(() => {
                                // Use enhanced status if available
                                const statusFn = window.showEnhancedStatus || showStatus;
                                statusFn('API key saved securely with AES-256 encryption', 'success', {
                                    duration: 4000
                                });

                                // Clear the input field for security
                                apiKeyInput.value = '';
                                apiKeyInput.placeholder = 'Encrypted API key configured';
                                apiKeyInput.style.backgroundColor = '#e8f5e8';
                            })
                            .catch(error => {
                                console.error('Error saving API key:', error);

                                // Provide actionable error message
                                const statusFn = window.showEnhancedStatus || showStatus;
                                let errorMessage = 'Failed to save API key';
                                let actionable = false;

                                if (error.message.includes('Encryption module not available')) {
                                    errorMessage = 'Encryption not available. Please refresh the page and try again';
                                    actionable = true;
                                } else if (error.message.includes('Invalid API keys')) {
                                    errorMessage = 'Invalid API key format. Please check your key and try again';
                                } else {
                                    errorMessage = `Failed to save API key: ${error.message}`;
                                }

                                statusFn(errorMessage, 'error', {
                                    persistent: true,
                                    actionable: actionable,
                                    action: actionable ? {
                                        text: 'Refresh Page',
                                        callback: () => window.location.reload()
                                    } : null
                                });
                            });
                    } else {
                        const statusFn = window.showEnhancedStatus || showStatus;
                        statusFn('API key saving function not available. Please refresh the page', 'error', {
                            persistent: true,
                            actionable: true,
                            action: {
                                text: 'Refresh Page',
                                callback: () => window.location.reload()
                            }
                        });
                    }
                }
            });
            return; // Exit early since we're handling API key saving separately
        }

            // Save preferences only (API keys are handled separately with encryption)
            chrome.storage.local.set({
                voiceApiPreferences: preferences
            }, function() {
                showStatus('Settings saved successfully!', 'success');

                // Send message to content script to update settings
                chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
                    if (tabs[0]) {
                        chrome.tabs.sendMessage(tabs[0].id, {
                            action: 'updateVoiceSettings',
                            preferences: preferences
                        });
                    }
                });
            });
        });

        return true;
    };

    // Helper function to get alternative languages for auto-detection
    function getAlternativeLanguages(primaryLanguage) {
        // Get the base language code (e.g., 'en' from 'en-US')
        const baseLanguage = primaryLanguage.split('-')[0];

        // Get all languages with the same base
        const alternatives = providerLanguages[providerSelect.value].filter(lang => {
            return lang !== primaryLanguage && lang.startsWith(baseLanguage + '-');
        });

        // Limit to 4 alternatives to avoid overwhelming the API
        return alternatives.slice(0, 4);
    }

    // Helper function to get the acoustic model based on quality setting
    function getAcousticModelForQuality(quality) {
        switch (quality) {
            case 'standard':
                return 'standard';
            case 'enhanced':
                return 'enhanced';
            case 'high':
                return 'nova';
            case 'ultra':
                return 'nova-2';
            default:
                return 'standard';
        }
    }

    function showStatus(message, type) {
        statusText.textContent = message;
        statusMessage.className = 'status-message ' + type;
        statusMessage.classList.add('visible');

        setTimeout(function() {
            statusMessage.classList.remove('visible');
        }, 3000);
    }

    // Reset settings to defaults
    resetButton.addEventListener('click', function() {
        if (confirm('Reset all voice settings to default values?')) {
            // Default settings
            const defaultSettings = {
                provider: 'browser',
                language: 'en-US',
                enablePunctuation: true,
                enableAutomaticCapitalization: true,
                enableProfanityFilter: false,
                enableInterimResults: true,
                silenceThreshold: 15000,
                autoDetectLanguage: false,
                backgroundNoiseReduction: false,
                speakerDiarization: false,
                quality: 'standard',
                version: '2.0',
                lastUpdated: new Date().toISOString()
            };

            // Apply defaults to UI
            providerSelect.value = defaultSettings.provider;
            updateProviderUI(defaultSettings.provider);

            languageSelect.value = defaultSettings.language;
            punctuationCheckbox.checked = defaultSettings.enablePunctuation;
            capitalizationCheckbox.checked = defaultSettings.enableAutomaticCapitalization;
            profanityFilterCheckbox.checked = defaultSettings.enableProfanityFilter;
            interimResultsCheckbox.checked = defaultSettings.enableInterimResults;

            silenceThresholdInput.value = defaultSettings.silenceThreshold / 1000;
            document.getElementById('silence-value').textContent = defaultSettings.silenceThreshold / 1000 + ' seconds';

            autoDetectCheckbox.checked = defaultSettings.autoDetectLanguage;
            backgroundNoiseReductionCheckbox.checked = defaultSettings.backgroundNoiseReduction;
            speakerDiarizationCheckbox.checked = defaultSettings.speakerDiarization;
            qualitySelect.value = defaultSettings.quality;

            apiKeyInput.value = '';

            // Save defaults to storage
            chrome.storage.local.set({
                voiceApiPreferences: defaultSettings
            }, function() {
                showStatus('Settings reset to defaults', 'info');

                // Send message to content script to update settings
                chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
                    if (tabs[0]) {
                        chrome.tabs.sendMessage(tabs[0].id, {
                            action: 'updateVoiceSettings',
                            preferences: defaultSettings
                        });
                    }
                });
            });
        }
    });

    // Add event listener for auto-detect checkbox
    autoDetectCheckbox.addEventListener('change', function() {
        if (this.checked) {
            // If auto-detect is enabled, show a message about language selection
            const provider = providerSelect.value;
            let message = '';

            if (provider === 'browser') {
                message = 'Browser API has limited language detection capabilities';
                showStatus(message, 'warning');
            } else if (provider === 'google') {
                message = 'Google will attempt to detect the language automatically';
                showStatus(message, 'info');
            } else if (provider === 'azure') {
                message = 'Azure will identify the spoken language';
                showStatus(message, 'info');
            } else if (provider === 'assembly') {
                message = 'AssemblyAI provides advanced language detection';
                showStatus(message, 'info');
            }

            // Language selection becomes less important with auto-detect
            languageSelect.classList.add('secondary-importance');
        } else {
            // If auto-detect is disabled, language selection is important
            languageSelect.classList.remove('secondary-importance');
        }
    });

    // Add event listener for background noise reduction
    backgroundNoiseReductionCheckbox.addEventListener('change', function() {
        if (this.checked) {
            showStatus('Background noise reduction enabled', 'info');
        }
    });

    // Add event listener for speaker diarization
    speakerDiarizationCheckbox.addEventListener('change', function() {
        if (this.checked) {
            showStatus('Speaker identification enabled', 'info');
        }
    });

    // Add event listener for quality select
    qualitySelect.addEventListener('change', function() {
        const provider = providerSelect.value;
        const quality = this.value;

        if (quality === 'standard') {
            showStatus('Standard quality selected', 'info');
        } else if (quality === 'enhanced') {
            showStatus('Enhanced quality selected - better accuracy', 'info');
        } else if (quality === 'high') {
            showStatus('High quality selected - best for most uses', 'info');
        } else if (quality === 'ultra') {
            showStatus('Ultra quality selected - highest accuracy', 'info');
        }
    });
});