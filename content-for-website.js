// content-for-website.js
// Secure bridge for communication between stashyapp.com and Stashy Chrome Extension

console.log('Stashy: Website communication bridge loaded');

/**
 * Listens for the 'stashyGetUserInfo' event dispatched by the website's script.js.
 * This provides a secure way for your website to request user authentication info from the extension.
 */
document.addEventListener('stashyGetUserInfo', async (event) => {
    console.log('Stashy Bridge: Received "stashyGetUserInfo" request from website');

    try {
        // Ask the background script for the user's information
        const userInfo = await chrome.runtime.sendMessage({ action: 'getUserInfo' });

        if (userInfo && userInfo.success) {
            console.log('Stashy Bridge: Successfully retrieved user info');
            // Send the user info back to the website in a custom event
            document.dispatchEvent(new CustomEvent('stashyUserInfoResponse', {
                detail: {
                    success: true,
                    userId: userInfo.userId,
                    email: userInfo.email
                }
            }));
        } else {
            // Handle cases where the user is not signed in or an error occurred
            throw new Error(userInfo?.error || 'User is not signed in to the extension');
        }
    } catch (error) {
        console.error('Stashy Bridge: Error getting user info from background script:', error);
        // Send a failure response back to the website
        document.dispatchEvent(new CustomEvent('stashyUserInfoResponse', {
            detail: {
                success: false,
                error: error.message
            }
        }));
    }
});

/**
 * Listen for premium status requests from the website
 */
document.addEventListener('stashyGetPremiumStatus', async (event) => {
    console.log('Stashy Bridge: Received "stashyGetPremiumStatus" request from website');

    try {
        // Ask the background script for the premium status
        const premiumStatus = await chrome.runtime.sendMessage({ action: 'getPremiumStatus' });

        if (premiumStatus) {
            console.log('Stashy Bridge: Successfully retrieved premium status');
            // Send the premium status back to the website
            document.dispatchEvent(new CustomEvent('stashyPremiumStatusResponse', {
                detail: {
                    success: true,
                    isPremium: premiumStatus.isPremium,
                    expiryDate: premiumStatus.expiryDate
                }
            }));
        } else {
            throw new Error('Failed to retrieve premium status');
        }
    } catch (error) {
        console.error('Stashy Bridge: Error getting premium status from background script:', error);
        // Send a failure response back to the website
        document.dispatchEvent(new CustomEvent('stashyPremiumStatusResponse', {
            detail: {
                success: false,
                error: error.message
            }
        }));
    }
});

/**
 * Listen for extension status check from the website
 * This allows the website to detect if the extension is installed and active
 */
document.addEventListener('stashyCheckExtension', (event) => {
    console.log('Stashy Bridge: Received "stashyCheckExtension" request from website');
    
    // Immediately respond that the extension is installed and active
    document.dispatchEvent(new CustomEvent('stashyExtensionResponse', {
        detail: {
            installed: true,
            version: chrome.runtime.getManifest().version,
            name: chrome.runtime.getManifest().name
        }
    }));
});

// Notify the website that the bridge is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('Stashy Bridge: DOM loaded, notifying website that bridge is ready');
    document.dispatchEvent(new CustomEvent('stashyBridgeReady', {
        detail: {
            ready: true,
            timestamp: Date.now()
        }
    }));
});

// If DOM is already loaded, dispatch immediately
if (document.readyState === 'loading') {
    // DOM is still loading, event listener above will handle it
} else {
    // DOM is already loaded
    console.log('Stashy Bridge: DOM already loaded, notifying website that bridge is ready');
    document.dispatchEvent(new CustomEvent('stashyBridgeReady', {
        detail: {
            ready: true,
            timestamp: Date.now()
        }
    }));
}

console.log('Stashy: Website communication bridge initialized');
