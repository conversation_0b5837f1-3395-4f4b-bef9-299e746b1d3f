/**
 * Stashy IndexedDB Storage Module
 * Provides persistent storage capabilities using IndexedDB
 * with improved caching, pagination, and performance
 *
 * Features:
 * - Tiered storage with memory cache and IndexedDB
 * - Adaptive TTL policies for cache items
 * - Memory optimization to reduce RAM usage
 * - Error handling and recovery mechanisms
 * - Data compression for large objects
 * - Background synchronization
 */

// Create a namespace to avoid global pollution
window.StashyIndexedDB = (function() {
    // Constants
    const DB_NAME = 'StashyDB';
    const DB_VERSION = 2; // Increased version for schema updates
    const STORES = {
        NOTES: 'notes',
        HIGHLIGHTS: 'highlights',
        METADATA: 'metadata',
        CACHE: 'cache',
        SYNC_QUEUE: 'sync_queue', // New store for sync operations
        BACKUP: 'backup' // New store for backups
    };

    // Private variables
    let db = null;
    let isInitialized = false;
    let initPromise = null;
    let memoryCache = {
        // Temporary in-memory cache for frequently accessed items
        // Format: { key: { data, timestamp, ttl } }
    };
    let recoveryAttempts = 0;
    let lastMemoryOptimization = 0;

    // Configuration
    const config = {
        memoryCacheEnabled: true,
        memoryCacheMaxItems: 100,
        memoryCacheDefaultTTL: 5 * 60 * 1000, // 5 minutes in milliseconds
        compressionEnabled: true,
        compressionThreshold: 10 * 1024, // 10 KB
        errorRecoveryEnabled: true,
        autoMemoryOptimization: true,
        syncEnabled: true,
        debug: false
    };

    /**
     * Log messages when debug mode is enabled
     * @param {...any} args - Arguments to log
     */
    function debugLog(...args) {
        if (config.debug) {
            console.log('[StashyIndexedDB]', ...args);
        }
    }

    /**
     * Initializes the IndexedDB database
     * @returns {Promise<IDBDatabase>} A promise that resolves with the database
     */
    function initDB() {
        if (initPromise) return initPromise;

        initPromise = new Promise((resolve, reject) => {
            if (!window.indexedDB) {
                console.error('Stashy: IndexedDB not supported in this browser');
                reject(new Error('IndexedDB not supported'));
                return;
            }

            debugLog('Opening database...');
            const request = window.indexedDB.open(DB_NAME, DB_VERSION);

            request.onerror = (event) => {
                console.error('Stashy: Error opening IndexedDB:', event.target.error);
                reject(event.target.error);
            };

            request.onsuccess = (event) => {
                db = event.target.result;
                isInitialized = true;
                debugLog('Database opened successfully');
                resolve(db);
            };

            request.onupgradeneeded = (event) => {
                debugLog('Database upgrade needed');
                const db = event.target.result;
                const oldVersion = event.oldVersion;

                debugLog(`Upgrading database from version ${oldVersion} to ${DB_VERSION}`);

                // Create object stores if they don't exist
                if (!db.objectStoreNames.contains(STORES.NOTES)) {
                    const notesStore = db.createObjectStore(STORES.NOTES, { keyPath: 'key' });
                    notesStore.createIndex('url', 'url', { unique: false });
                    notesStore.createIndex('notebookId', 'notebookId', { unique: false });
                    notesStore.createIndex('lastModified', 'lastModified', { unique: false });
                    notesStore.createIndex('timestamp', 'timestamp', { unique: false });
                    debugLog('Created notes store');
                }

                if (!db.objectStoreNames.contains(STORES.HIGHLIGHTS)) {
                    const highlightsStore = db.createObjectStore(STORES.HIGHLIGHTS, { keyPath: 'key' });
                    highlightsStore.createIndex('url', 'url', { unique: false });
                    highlightsStore.createIndex('notebookId', 'notebookId', { unique: false });
                    highlightsStore.createIndex('lastModified', 'lastModified', { unique: false });
                    highlightsStore.createIndex('timestamp', 'timestamp', { unique: false });
                    debugLog('Created highlights store');
                }

                if (!db.objectStoreNames.contains(STORES.METADATA)) {
                    db.createObjectStore(STORES.METADATA, { keyPath: 'key' });
                    debugLog('Created metadata store');
                }

                if (!db.objectStoreNames.contains(STORES.CACHE)) {
                    const cacheStore = db.createObjectStore(STORES.CACHE, { keyPath: 'key' });
                    cacheStore.createIndex('expiry', 'expiry', { unique: false });
                    cacheStore.createIndex('timestamp', 'timestamp', { unique: false });
                    debugLog('Created cache store');
                }

                // New stores for version 2
                if (oldVersion < 2) {
                    if (!db.objectStoreNames.contains(STORES.SYNC_QUEUE)) {
                        const syncStore = db.createObjectStore(STORES.SYNC_QUEUE, { keyPath: 'id' });
                        syncStore.createIndex('timestamp', 'timestamp', { unique: false });
                        syncStore.createIndex('status', 'status', { unique: false });
                        debugLog('Created sync queue store');
                    }

                    if (!db.objectStoreNames.contains(STORES.BACKUP)) {
                        const backupStore = db.createObjectStore(STORES.BACKUP, { keyPath: 'key' });
                        backupStore.createIndex('timestamp', 'timestamp', { unique: false });
                        backupStore.createIndex('type', 'type', { unique: false });
                        debugLog('Created backup store');
                    }
                }
            };
        });

        return initPromise;
    }

    /**
     * Ensures the database is initialized before performing operations
     * @returns {Promise<IDBDatabase>} A promise that resolves with the database
     */
    async function ensureDB() {
        if (isInitialized && db) return db;
        return initDB();
    }

    /**
     * Adds or updates an item in the memory cache
     * @param {string} key - The cache key
     * @param {any} data - The data to cache
     * @param {number} ttl - Time to live in milliseconds
     */
    function updateMemoryCache(key, data, ttl = config.memoryCacheDefaultTTL) {
        if (!config.memoryCacheEnabled) return;

        // Add to memory cache
        memoryCache[key] = {
            data: data,
            timestamp: Date.now(),
            ttl: ttl
        };

        // Prune cache if it exceeds the maximum size
        const keys = Object.keys(memoryCache);
        if (keys.length > config.memoryCacheMaxItems) {
            // Remove oldest items
            const oldestKeys = keys
                .map(k => ({ key: k, timestamp: memoryCache[k].timestamp }))
                .sort((a, b) => a.timestamp - b.timestamp)
                .slice(0, keys.length - config.memoryCacheMaxItems)
                .map(item => item.key);

            oldestKeys.forEach(k => delete memoryCache[k]);
            debugLog(`Pruned ${oldestKeys.length} items from memory cache`);
        }
    }

    /**
     * Gets an item from the memory cache if it exists and is not expired
     * @param {string} key - The cache key
     * @returns {any|null} The cached data or null if not found or expired
     */
    function getFromMemoryCache(key) {
        if (!config.memoryCacheEnabled || !memoryCache[key]) return null;

        const cacheItem = memoryCache[key];
        const age = Date.now() - cacheItem.timestamp;

        // Check if the item has expired
        if (age > cacheItem.ttl) {
            delete memoryCache[key];
            return null;
        }

        return cacheItem.data;
    }

    /**
     * Clears the memory cache
     * @param {string} [keyPrefix] - Optional prefix to clear only matching keys
     */
    function clearMemoryCache(keyPrefix) {
        if (!config.memoryCacheEnabled) return;

        if (keyPrefix) {
            // Clear only keys with the specified prefix
            Object.keys(memoryCache).forEach(key => {
                if (key.startsWith(keyPrefix)) {
                    delete memoryCache[key];
                }
            });
            debugLog(`Cleared memory cache items with prefix: ${keyPrefix}`);
        } else {
            // Clear all items
            memoryCache = {};
            debugLog('Cleared entire memory cache');
        }
    }

    /**
     * Stores an item in the specified object store
     * @param {string} storeName - The name of the object store
     * @param {Object} data - The data to store
     * @param {boolean} [useMemoryCache=true] - Whether to also update the memory cache
     * @returns {Promise<Object>} A promise that resolves with the stored data
     */
    async function setItem(storeName, data, useMemoryCache = true) {
        try {
            await ensureDB();

            // Ensure data has a key
            if (!data.key) {
                throw new Error('Data must have a key property');
            }

            // Add lastModified timestamp if not present
            if (!data.lastModified) {
                data.lastModified = Date.now();
            }

            return new Promise((resolve, reject) => {
                const transaction = db.transaction([storeName], 'readwrite');
                const store = transaction.objectStore(storeName);

                const request = store.put(data);

                request.onsuccess = () => {
                    debugLog(`Stored item in ${storeName}:`, data.key);

                    // Update memory cache if enabled
                    if (useMemoryCache) {
                        updateMemoryCache(data.key, data);
                    }

                    resolve(data);
                };

                request.onerror = (event) => {
                    console.error(`Stashy: Error storing item in ${storeName}:`, event.target.error);
                    reject(event.target.error);
                };
            });
        } catch (error) {
            console.error(`Stashy: Exception in setItem (${storeName}):`, error);
            throw error;
        }
    }

    /**
     * Retrieves an item from the specified object store
     * @param {string} storeName - The name of the object store
     * @param {string} key - The key of the item to retrieve
     * @param {boolean} [useMemoryCache=true] - Whether to check the memory cache first
     * @returns {Promise<Object|null>} A promise that resolves with the retrieved data or null if not found
     */
    async function getItem(storeName, key, useMemoryCache = true) {
        try {
            // Check memory cache first if enabled
            if (useMemoryCache) {
                const cachedData = getFromMemoryCache(key);
                if (cachedData) {
                    debugLog(`Retrieved item from memory cache:`, key);
                    return cachedData;
                }
            }

            await ensureDB();

            return new Promise((resolve, reject) => {
                const transaction = db.transaction([storeName], 'readonly');
                const store = transaction.objectStore(storeName);

                const request = store.get(key);

                request.onsuccess = () => {
                    const data = request.result;

                    if (data && useMemoryCache) {
                        updateMemoryCache(key, data);
                    }

                    resolve(data || null);
                };

                request.onerror = (event) => {
                    console.error(`Stashy: Error retrieving item from ${storeName}:`, event.target.error);
                    reject(event.target.error);
                };
            });
        } catch (error) {
            console.error(`Stashy: Exception in getItem (${storeName}):`, error);
            throw error;
        }
    }

    /**
     * Removes an item from the specified object store
     * @param {string} storeName - The name of the object store
     * @param {string} key - The key of the item to remove
     * @returns {Promise<boolean>} A promise that resolves with true if successful
     */
    async function removeItem(storeName, key) {
        try {
            await ensureDB();

            // Remove from memory cache if present
            if (memoryCache[key]) {
                delete memoryCache[key];
            }

            return new Promise((resolve, reject) => {
                const transaction = db.transaction([storeName], 'readwrite');
                const store = transaction.objectStore(storeName);

                const request = store.delete(key);

                request.onsuccess = () => {
                    debugLog(`Removed item from ${storeName}:`, key);
                    resolve(true);
                };

                request.onerror = (event) => {
                    console.error(`Stashy: Error removing item from ${storeName}:`, event.target.error);
                    reject(event.target.error);
                };
            });
        } catch (error) {
            console.error(`Stashy: Exception in removeItem (${storeName}):`, error);
            throw error;
        }
    }

    /**
     * Retrieves all items from the specified object store
     * @param {string} storeName - The name of the object store
     * @param {Object} [options] - Query options
     * @param {string} [options.indexName] - Name of the index to query
     * @param {IDBKeyRange} [options.range] - Range to query
     * @param {number} [options.limit] - Maximum number of items to retrieve
     * @param {number} [options.offset] - Number of items to skip
     * @returns {Promise<Array>} A promise that resolves with an array of items
     */
    async function getAllItems(storeName, options = {}) {
        try {
            await ensureDB();

            return new Promise((resolve, reject) => {
                const transaction = db.transaction([storeName], 'readonly');
                const store = transaction.objectStore(storeName);

                let source = store;
                if (options.indexName) {
                    source = store.index(options.indexName);
                }

                const items = [];
                let cursorRequest;

                if (options.range) {
                    cursorRequest = source.openCursor(options.range);
                } else {
                    cursorRequest = source.openCursor();
                }

                let skipped = 0;
                let retrieved = 0;

                cursorRequest.onsuccess = (event) => {
                    const cursor = event.target.result;

                    if (cursor) {
                        // Skip items if offset is specified
                        if (options.offset && skipped < options.offset) {
                            skipped++;
                            cursor.continue();
                            return;
                        }

                        // Check if we've reached the limit
                        if (options.limit && retrieved >= options.limit) {
                            resolve(items);
                            return;
                        }

                        items.push(cursor.value);
                        retrieved++;

                        cursor.continue();
                    } else {
                        resolve(items);
                    }
                };

                cursorRequest.onerror = (event) => {
                    console.error(`Stashy: Error retrieving items from ${storeName}:`, event.target.error);
                    reject(event.target.error);
                };
            });
        } catch (error) {
            console.error(`Stashy: Exception in getAllItems (${storeName}):`, error);
            throw error;
        }
    }

    /**
     * Counts the number of items in the specified object store
     * @param {string} storeName - The name of the object store
     * @param {Object} [options] - Query options
     * @param {string} [options.indexName] - Name of the index to query
     * @param {IDBKeyRange} [options.range] - Range to query
     * @returns {Promise<number>} A promise that resolves with the count
     */
    async function countItems(storeName, options = {}) {
        try {
            await ensureDB();

            return new Promise((resolve, reject) => {
                const transaction = db.transaction([storeName], 'readonly');
                const store = transaction.objectStore(storeName);

                let source = store;
                if (options.indexName) {
                    source = store.index(options.indexName);
                }

                let countRequest;

                if (options.range) {
                    countRequest = source.count(options.range);
                } else {
                    countRequest = source.count();
                }

                countRequest.onsuccess = () => {
                    resolve(countRequest.result);
                };

                countRequest.onerror = (event) => {
                    console.error(`Stashy: Error counting items in ${storeName}:`, event.target.error);
                    reject(event.target.error);
                };
            });
        } catch (error) {
            console.error(`Stashy: Exception in countItems (${storeName}):`, error);
            throw error;
        }
    }

    /**
     * Stores a cache item with an expiration time
     * @param {string} key - The cache key
     * @param {any} data - The data to cache
     * @param {number|string} [ttl=3600000] - Time to live in milliseconds or TTL category
     * @param {Object} [options] - Additional options
     * @returns {Promise<Object>} A promise that resolves with the stored cache item
     */
    async function setCacheItem(key, data, ttl = 3600000, options = {}) {
        try {
            await ensureDB();

            // Check if we should optimize memory before storing
            if (config.autoMemoryOptimization &&
                Date.now() - lastMemoryOptimization > 60000) { // Once per minute at most

                if (window.StashyMemoryOptimizer) {
                    window.StashyMemoryOptimizer.optimizeMemory();
                    lastMemoryOptimization = Date.now();
                }
            }

            // Calculate TTL using TTL manager if available
            let finalTTL = ttl;
            if (window.StashyTTLManager && typeof ttl === 'string') {
                finalTTL = window.StashyTTLManager.calculateTTL(ttl, key);
            }

            // Prepare the cache item
            let itemToStore = {
                key: key,
                data: data,
                timestamp: Date.now(),
                expiry: Date.now() + finalTTL,
                compressed: false
            };

            // Compress data if enabled and appropriate
            if (config.compressionEnabled && window.StashyCompression) {
                const shouldCompress = window.StashyCompression.shouldCompress(data, key);

                if (shouldCompress) {
                    try {
                        const compressedData = await window.StashyCompression.compress(data, {
                            metadata: { key, ttl: finalTTL }
                        });

                        if (compressedData.compressed) {
                            itemToStore.data = compressedData.data;
                            itemToStore.compressed = true;
                            itemToStore.algorithm = compressedData.algorithm;
                            itemToStore.originalSize = compressedData.originalSize;
                            itemToStore.compressedSize = compressedData.compressedSize;

                            debugLog(`Compressed cache item ${key}: ${compressedData.originalSize} → ${compressedData.compressedSize} bytes`);
                        }
                    } catch (compressionError) {
                        console.warn(`Stashy: Error compressing cache item ${key}:`, compressionError);
                        // Continue with uncompressed data
                    }
                }
            }

            // Store in IndexedDB
            return new Promise((resolve, reject) => {
                const transaction = db.transaction([STORES.CACHE], 'readwrite');
                const store = transaction.objectStore(STORES.CACHE);

                const request = store.put(itemToStore);

                request.onsuccess = () => {
                    debugLog(`Stored cache item:`, key);

                    // Also update memory cache with the original (uncompressed) data
                    updateMemoryCache(key, data, finalTTL);

                    // Record access in TTL manager if available
                    if (window.StashyTTLManager) {
                        window.StashyTTLManager.recordAccess(key);
                    }

                    // Queue for sync if enabled
                    if (config.syncEnabled && window.StashySyncManager && options.sync !== false) {
                        window.StashySyncManager.queueForSync({
                            type: 'cache',
                            key: key,
                            operation: 'write',
                            data: itemToStore
                        });
                    }

                    resolve(itemToStore);
                };

                request.onerror = (event) => {
                    const error = event.target.error;
                    console.error(`Stashy: Error storing cache item:`, error);

                    // Handle error with error handler if available
                    if (config.errorRecoveryEnabled && window.StashyDBErrorHandler) {
                        window.StashyDBErrorHandler.handleError(error, {
                            operation: 'setCacheItem',
                            operationId: `cache-set-${key}-${Date.now()}`,
                            data: itemToStore
                        }).then(recoveryResult => {
                            if (recoveryResult.success && recoveryResult.strategy === 'retry') {
                                // Retry after delay
                                setTimeout(() => {
                                    setCacheItem(key, data, ttl, options)
                                        .then(resolve)
                                        .catch(reject);
                                }, recoveryResult.delay || 1000);
                            } else {
                                reject(error);
                            }
                        }).catch(() => {
                            reject(error);
                        });
                    } else {
                        reject(error);
                    }
                };
            });
        } catch (error) {
            console.error(`Stashy: Exception in setCacheItem:`, error);

            // Increment recovery attempts counter
            recoveryAttempts++;

            // Try to recover if possible
            if (config.errorRecoveryEnabled && recoveryAttempts < 3) {
                console.warn(`Stashy: Attempting recovery for setCacheItem (attempt ${recoveryAttempts})`);

                // Wait a bit before retrying
                await new Promise(resolve => setTimeout(resolve, 500 * recoveryAttempts));

                // Try again
                return setCacheItem(key, data, ttl, options);
            }

            // Reset recovery counter
            recoveryAttempts = 0;
            throw error;
        }
    }

    /**
     * Retrieves a cache item if it exists and is not expired
     * @param {string} key - The cache key
     * @param {Object} [options] - Additional options
     * @returns {Promise<any|null>} A promise that resolves with the cached data or null if not found or expired
     */
    async function getCacheItem(key, options = {}) {
        try {
            // Check memory cache first
            const cachedData = getFromMemoryCache(key);
            if (cachedData) {
                // Record access in TTL manager if available
                if (window.StashyTTLManager) {
                    window.StashyTTLManager.recordAccess(key);
                }
                return cachedData;
            }

            await ensureDB();

            return new Promise((resolve, reject) => {
                const transaction = db.transaction([STORES.CACHE], 'readonly');
                const store = transaction.objectStore(STORES.CACHE);

                const request = store.get(key);

                request.onsuccess = async () => {
                    const cacheItem = request.result;

                    if (!cacheItem) {
                        resolve(null);
                        return;
                    }

                    // Check if the item has expired
                    if (Date.now() > cacheItem.expiry) {
                        // Remove expired item
                        const deleteTransaction = db.transaction([STORES.CACHE], 'readwrite');
                        const deleteStore = deleteTransaction.objectStore(STORES.CACHE);
                        deleteStore.delete(key);

                        resolve(null);
                        return;
                    }

                    try {
                        // Handle compressed data
                        let finalData = cacheItem.data;

                        if (cacheItem.compressed && window.StashyCompression) {
                            try {
                                finalData = await window.StashyCompression.decompress({
                                    compressed: true,
                                    data: cacheItem.data,
                                    algorithm: cacheItem.algorithm
                                });

                                debugLog(`Decompressed cache item ${key}`);
                            } catch (decompressionError) {
                                console.warn(`Stashy: Error decompressing cache item ${key}:`, decompressionError);
                                // Continue with compressed data
                            }
                        }

                        // Update memory cache with decompressed data
                        updateMemoryCache(key, finalData, cacheItem.expiry - Date.now());

                        // Record access in TTL manager if available
                        if (window.StashyTTLManager) {
                            window.StashyTTLManager.recordAccess(key);
                        }

                        resolve(finalData);
                    } catch (processingError) {
                        console.error(`Stashy: Error processing cache item ${key}:`, processingError);
                        resolve(cacheItem.data); // Return raw data as fallback
                    }
                };

                request.onerror = (event) => {
                    const error = event.target.error;
                    console.error(`Stashy: Error retrieving cache item:`, error);

                    // Handle error with error handler if available
                    if (config.errorRecoveryEnabled && window.StashyDBErrorHandler) {
                        window.StashyDBErrorHandler.handleError(error, {
                            operation: 'getCacheItem',
                            operationId: `cache-get-${key}-${Date.now()}`
                        }).then(recoveryResult => {
                            if (recoveryResult.success && recoveryResult.strategy === 'retry') {
                                // Retry after delay
                                setTimeout(() => {
                                    getCacheItem(key, options)
                                        .then(resolve)
                                        .catch(reject);
                                }, recoveryResult.delay || 1000);
                            } else if (recoveryResult.success && recoveryResult.strategy === 'fallback') {
                                // Try to get from fallback storage
                                if (window.StashyCacheManager) {
                                    window.StashyCacheManager.get(key)
                                        .then(resolve)
                                        .catch(() => reject(error));
                                } else {
                                    reject(error);
                                }
                            } else {
                                reject(error);
                            }
                        }).catch(() => {
                            reject(error);
                        });
                    } else {
                        reject(error);
                    }
                };
            });
        } catch (error) {
            console.error(`Stashy: Exception in getCacheItem:`, error);

            // Increment recovery attempts counter
            recoveryAttempts++;

            // Try to recover if possible
            if (config.errorRecoveryEnabled && recoveryAttempts < 3) {
                console.warn(`Stashy: Attempting recovery for getCacheItem (attempt ${recoveryAttempts})`);

                // Wait a bit before retrying
                await new Promise(resolve => setTimeout(resolve, 500 * recoveryAttempts));

                // Try again
                return getCacheItem(key, options);
            }

            // Reset recovery counter
            recoveryAttempts = 0;

            // Try fallback storage as last resort
            if (window.StashyCacheManager) {
                try {
                    return await window.StashyCacheManager.get(key);
                } catch (fallbackError) {
                    console.error(`Stashy: Fallback storage also failed for ${key}:`, fallbackError);
                    throw error; // Throw the original error
                }
            }

            throw error;
        }
    }

    /**
     * Clears expired cache items
     * @param {Object} [options] - Additional options
     * @returns {Promise<number>} A promise that resolves with the number of items cleared
     */
    async function clearExpiredCache(options = {}) {
        try {
            await ensureDB();

            return new Promise((resolve, reject) => {
                const transaction = db.transaction([STORES.CACHE], 'readwrite');
                const store = transaction.objectStore(STORES.CACHE);
                const index = store.index('expiry');

                const now = Date.now();
                const range = IDBKeyRange.upperBound(now);

                let count = 0;
                const cursorRequest = index.openCursor(range);
                const deletedKeys = [];

                cursorRequest.onsuccess = (event) => {
                    const cursor = event.target.result;

                    if (cursor) {
                        const key = cursor.value.key;
                        store.delete(cursor.primaryKey);

                        // Also remove from memory cache
                        if (memoryCache[key]) {
                            delete memoryCache[key];
                        }

                        deletedKeys.push(key);
                        count++;
                        cursor.continue();
                    } else {
                        debugLog(`Cleared ${count} expired cache items`);

                        // Notify TTL manager if available
                        if (window.StashyTTLManager) {
                            deletedKeys.forEach(key => {
                                window.StashyTTLManager.clearUsageStats(key);
                            });
                        }

                        resolve(count);
                    }
                };

                cursorRequest.onerror = (event) => {
                    const error = event.target.error;
                    console.error(`Stashy: Error clearing expired cache:`, error);

                    // Handle error with error handler if available
                    if (config.errorRecoveryEnabled && window.StashyDBErrorHandler) {
                        window.StashyDBErrorHandler.handleError(error, {
                            operation: 'clearExpiredCache'
                        }).then(recoveryResult => {
                            if (recoveryResult.success) {
                                resolve(count); // Return the count we have so far
                            } else {
                                reject(error);
                            }
                        }).catch(() => {
                            reject(error);
                        });
                    } else {
                        reject(error);
                    }
                };
            });
        } catch (error) {
            console.error(`Stashy: Exception in clearExpiredCache:`, error);

            // Try to recover if possible
            if (config.errorRecoveryEnabled && window.StashyDBErrorHandler) {
                try {
                    const recoveryResult = await window.StashyDBErrorHandler.handleError(error, {
                        operation: 'clearExpiredCache'
                    });

                    if (recoveryResult.success) {
                        return 0; // Return 0 as we couldn't clear anything
                    }
                } catch (recoveryError) {
                    console.error('Stashy: Error recovery failed:', recoveryError);
                }
            }

            throw error;
        }
    }

    /**
     * Trims the memory cache by removing a percentage of least recently used items
     * @param {number} [percentage=0.2] - Percentage of items to remove (0.0 to 1.0)
     * @returns {Object} Result of the trim operation
     */
    function trimMemoryCache(percentage = 0.2) {
        if (!config.memoryCacheEnabled || percentage <= 0) {
            return { success: false, reason: 'Memory cache disabled or invalid percentage', removedCount: 0 };
        }

        const keys = Object.keys(memoryCache);
        if (keys.length === 0) {
            return { success: true, reason: 'Memory cache already empty', removedCount: 0 };
        }

        // Calculate how many items to remove
        const removeCount = Math.max(1, Math.floor(keys.length * percentage));

        // Sort items by last access time
        const sortedItems = keys
            .map(key => ({
                key,
                timestamp: memoryCache[key].timestamp
            }))
            .sort((a, b) => a.timestamp - b.timestamp);

        // Get the oldest items
        const itemsToRemove = sortedItems.slice(0, removeCount);

        // Remove the items
        itemsToRemove.forEach(item => {
            delete memoryCache[item.key];
        });

        debugLog(`Trimmed memory cache: removed ${itemsToRemove.length} of ${keys.length} items`);

        return {
            success: true,
            removedCount: itemsToRemove.length,
            totalCount: keys.length - itemsToRemove.length,
            percentage: percentage
        };
    }

    // Initialize the database when the module loads
    initDB().catch(error => {
        console.error('Stashy: Failed to initialize IndexedDB:', error);
    });

    // Return the public API
    return {
        // Core storage operations
        setNote: (data) => setItem(STORES.NOTES, data),
        getNote: (key) => getItem(STORES.NOTES, key),
        removeNote: (key) => removeItem(STORES.NOTES, key),
        getNotes: (options) => getAllItems(STORES.NOTES, options),
        countNotes: (options) => countItems(STORES.NOTES, options),

        setHighlight: (data) => setItem(STORES.HIGHLIGHTS, data),
        getHighlight: (key) => getItem(STORES.HIGHLIGHTS, key),
        removeHighlight: (key) => removeItem(STORES.HIGHLIGHTS, key),
        getHighlights: (options) => getAllItems(STORES.HIGHLIGHTS, options),
        countHighlights: (options) => countItems(STORES.HIGHLIGHTS, options),

        setMetadata: (data) => setItem(STORES.METADATA, data),
        getMetadata: (key) => getItem(STORES.METADATA, key),
        removeMetadata: (key) => removeItem(STORES.METADATA, key),

        // Cache operations
        setCacheItem,
        getCacheItem,
        clearExpiredCache,
        clearMemoryCache,
        trimMemoryCache,

        // Backup operations
        createBackup: (data) => setItem(STORES.BACKUP, {
            ...data,
            timestamp: Date.now(),
            key: `backup_${data.type || 'general'}_${Date.now()}`
        }),
        getBackup: (key) => getItem(STORES.BACKUP, key),
        getBackups: (options) => getAllItems(STORES.BACKUP, options),

        // Sync operations
        addToSyncQueue: (data) => setItem(STORES.SYNC_QUEUE, {
            ...data,
            id: data.id || `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            timestamp: Date.now(),
            status: data.status || 'pending'
        }),
        getSyncItem: (id) => getItem(STORES.SYNC_QUEUE, id),
        getSyncQueue: (options) => getAllItems(STORES.SYNC_QUEUE, options),
        removeSyncItem: (id) => removeItem(STORES.SYNC_QUEUE, id),

        // Memory management
        getMemoryCacheStats: () => ({
            enabled: config.memoryCacheEnabled,
            itemCount: Object.keys(memoryCache).length,
            maxItems: config.memoryCacheMaxItems,
            defaultTTL: config.memoryCacheDefaultTTL
        }),

        // Error handling
        getRecoveryAttempts: () => recoveryAttempts,
        resetRecoveryAttempts: () => { recoveryAttempts = 0; },

        // Utility methods
        init: initDB,
        STORES,

        // Configuration
        updateConfig: (newConfig) => {
            Object.assign(config, newConfig);
            return { ...config };
        },
        getConfig: () => ({ ...config })
    };
})();

console.log("Stashy: IndexedDB Storage Module Loaded");